#!/usr/bin/env python
"""
测试首次登录强制重置密码功能
"""
import os
import sys
import django
import requests
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def test_first_login_workflow():
    """测试首次登录完整流程"""
    print("🧪 测试首次登录强制重置密码功能...")
    
    base_url = 'http://localhost:8001'
    
    # 第一步：创建测试用户
    print("\n1. 创建测试用户...")
    
    # 使用管理员登录
    admin_login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        admin_response = requests.post(f"{base_url}/api/auth/login/", json=admin_login_data)
        if admin_response.status_code != 200:
            print(f"❌ 管理员登录失败: {admin_response.text}")
            return False
        
        admin_token = admin_response.json().get("access_token")
        admin_headers = {"Authorization": f"Bearer {admin_token}"}
        print("✅ 管理员登录成功")
        
        # 创建新用户
        new_user_data = {
            "username": "testfirstlogin",
            "email": "<EMAIL>",
            "name": "测试首次登录用户",
            "password": "TempPassword123!",
            "password_confirm": "TempPassword123!"
        }
        
        create_response = requests.post(
            f"{base_url}/api/users/",
            json=new_user_data,
            headers=admin_headers
        )
        
        if create_response.status_code == 201:
            user_data = create_response.json()["data"]
            print(f"✅ 用户创建成功: {user_data['username']}")
            user_id = user_data["id"]
        else:
            print(f"❌ 用户创建失败: {create_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 创建用户过程出错: {e}")
        return False
    
    # 第二步：测试首次登录
    print("\n2. 测试首次登录...")
    
    first_login_data = {
        "username": "testfirstlogin",
        "password": "TempPassword123!"
    }
    
    try:
        first_login_response = requests.post(f"{base_url}/api/auth/login/", json=first_login_data)
        
        if first_login_response.status_code == 200:
            response_data = first_login_response.json()
            
            if response_data.get("requires_password_change"):
                print("✅ 检测到首次登录，需要修改密码")
                print(f"   临时令牌: {response_data.get('temp_token')[:20]}...")
                print(f"   用户ID: {response_data.get('user_id')}")
                print(f"   是否首次登录: {response_data.get('is_first_login')}")
                
                temp_token = response_data.get("temp_token")
                user_id = response_data.get("user_id")
            else:
                print("❌ 首次登录检测失败，应该要求修改密码")
                return False
        else:
            print(f"❌ 首次登录失败: {first_login_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 首次登录过程出错: {e}")
        return False
    
    # 第三步：测试密码修改
    print("\n3. 测试首次登录密码修改...")
    
    password_change_data = {
        "temp_token": temp_token,
        "user_id": user_id,
        "new_password": "NewPassword123!",
        "new_password_confirm": "NewPassword123!"
    }
    
    try:
        change_response = requests.post(
            f"{base_url}/api/auth/password/first-login-change/",
            json=password_change_data
        )
        
        if change_response.status_code == 200:
            response_data = change_response.json()
            print("✅ 首次登录密码修改成功")
            print(f"   获得访问令牌: {response_data.get('access_token')[:20]}...")
            print(f"   用户信息: {response_data.get('user', {}).get('username')}")
            
            new_access_token = response_data.get("access_token")
        else:
            print(f"❌ 密码修改失败: {change_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 密码修改过程出错: {e}")
        return False
    
    # 第四步：测试正常登录
    print("\n4. 测试修改密码后的正常登录...")
    
    normal_login_data = {
        "username": "testfirstlogin",
        "password": "NewPassword123!"
    }
    
    try:
        normal_response = requests.post(f"{base_url}/api/auth/login/", json=normal_login_data)
        
        if normal_response.status_code == 200:
            response_data = normal_response.json()
            
            if not response_data.get("requires_password_change"):
                print("✅ 正常登录成功，不再要求修改密码")
                print(f"   用户: {response_data.get('user', {}).get('username')}")
            else:
                print("❌ 登录后仍要求修改密码，逻辑错误")
                return False
        else:
            print(f"❌ 正常登录失败: {normal_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 正常登录过程出错: {e}")
        return False
    
    # 第五步：清理测试数据
    print("\n5. 清理测试数据...")
    
    try:
        delete_response = requests.delete(
            f"{base_url}/api/users/{user_id}/",
            headers=admin_headers
        )
        
        if delete_response.status_code in [200, 204]:
            print("✅ 测试用户删除成功")
        else:
            print(f"⚠️ 测试用户删除失败: {delete_response.text}")
            
    except Exception as e:
        print(f"⚠️ 清理测试数据出错: {e}")
    
    return True


def test_audit_logs():
    """测试审计日志记录"""
    print("\n🧪 测试首次登录审计日志...")
    
    try:
        from apps.audit.models import BusinessOperationLog
        
        # 查找首次登录相关的日志
        first_login_logs = BusinessOperationLog.objects.filter(
            action_type__in=['first_login', 'first_login_password_change']
        ).order_by('-created_at')[:5]
        
        print(f"📊 找到 {first_login_logs.count()} 条首次登录相关日志:")
        
        for log in first_login_logs:
            print(f"   • {log.action_type}: {log.description}")
            print(f"     时间: {log.created_at}")
            print(f"     用户: {log.user.username if log.user else 'N/A'}")
            print(f"     额外数据: {log.extra_data}")
            print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ 审计日志测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试首次登录强制重置密码功能")
    print("=" * 60)
    
    tests = [
        test_first_login_workflow,
        test_audit_logs,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 40)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            print("-" * 40)
    
    print("=" * 60)
    print(f"📊 总体测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 首次登录强制重置密码功能测试通过！")
        print("\n📋 功能特性:")
        print("   • 新用户创建时自动标记为首次登录")
        print("   • 首次登录时强制要求修改密码")
        print("   • 提供临时令牌进行密码修改")
        print("   • 密码修改后自动完成首次登录流程")
        print("   • 完整的审计日志记录")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查实现")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
