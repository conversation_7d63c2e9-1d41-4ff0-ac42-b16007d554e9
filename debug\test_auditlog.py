#!/usr/bin/env python
"""
测试auditlog功能
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()


def test_auditlog():
    """测试auditlog功能"""
    print("🧪 测试auditlog功能...")

    try:
        from auditlog.models import LogEntry

        print("✅ 成功导入LogEntry模型")

        # 测试查询
        count = LogEntry.objects.count()
        print(f"✅ LogEntry记录数: {count}")

        # 测试字段访问
        if count > 0:
            entry = LogEntry.objects.first()
            print(f"✅ 第一条记录: {entry}")

            # 测试各个字段
            print(f"  - action: {entry.action}")
            print(f"  - timestamp: {entry.timestamp}")
            print(f"  - changes: {entry.changes}")
            print(f"  - object_repr: {entry.object_repr}")

        # 测试创建记录
        from django.contrib.auth import get_user_model
        from django.contrib.contenttypes.models import ContentType

        User = get_user_model()
        user_ct = ContentType.objects.get_for_model(User)

        # 创建一个测试记录
        test_entry = LogEntry.objects.create(
            content_type=user_ct,
            object_pk="test",
            object_id=1,
            object_repr="Test Object",
            action=1,  # CREATE
            changes={"test": "value"},
        )
        print(f"✅ 成功创建测试记录: {test_entry}")

        # 删除测试记录
        test_entry.delete()
        print("✅ 成功删除测试记录")

        return True

    except Exception as e:
        print(f"❌ auditlog测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_login_process():
    """测试登录过程中可能触发auditlog的地方"""
    print("\n🧪 测试登录相关的auditlog...")

    try:
        from django.contrib.auth import get_user_model
        from apps.audit.models import BusinessOperationLog

        User = get_user_model()

        # 检查是否有用户
        users = User.objects.all()[:5]
        print(f"✅ 用户数量: {User.objects.count()}")

        for user in users:
            print(f"  - 用户: {user.username}")

        # 检查BusinessOperationLog
        log_count = BusinessOperationLog.objects.count()
        print(f"✅ BusinessOperationLog记录数: {log_count}")

        return True

    except Exception as e:
        print(f"❌ 登录测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试auditlog问题")
    print("=" * 50)

    success1 = test_auditlog()
    success2 = test_login_process()

    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 所有测试通过！auditlog功能正常")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

    return success1 and success2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
