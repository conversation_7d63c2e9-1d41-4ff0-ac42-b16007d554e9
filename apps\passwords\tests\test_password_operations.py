"""
密码操作相关视图测试
"""
from rest_framework import status
from django.urls import reverse
from apps.passwords.models import PasswordEntry, PasswordHistory
from apps.audit.models import BusinessOperationLog, PasswordAccessLog
from .base import BasePasswordAPITestCase, BasePasswordPermissionTestCase
from .factories import PasswordEntryFactory
from utils.encryption import decrypt_data


class PasswordCopyViewTest(BasePasswordPermissionTestCase):
    """密码复制视图测试"""
    
    def test_copy_own_password(self):
        """测试复制自己的密码"""
        self.authenticate_user()
        self.clear_logs()
        
        url = self.get_url("password_copy", pk=self.password_entry.id)
        
        response = self.client.post(url)
        data = self.assert_response_success(response)
        
        # 验证返回的密码
        self.assertIn("password", data)
        decrypted_password = decrypt_data(self.password_entry.password)
        self.assertEqual(data["password"], decrypted_password)
        
        # 验证访问日志
        self.assert_access_logged(self.password_entry, "copy")
    
    def test_copy_others_password_no_permission(self):
        """测试复制他人密码（无权限）"""
        self.authenticate_user()
        
        url = self.get_url("password_copy", pk=self.other_password_entry.id)
        
        response = self.client.post(url)
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)
    
    def test_copy_others_password_with_permission(self):
        """测试复制他人密码（有权限）"""
        # 授予读权限
        self.grant_password_permission(
            self.user, 
            self.other_password_entry, 
            "read"
        )
        
        self.authenticate_user()
        self.clear_logs()
        
        url = self.get_url("password_copy", pk=self.other_password_entry.id)
        
        response = self.client.post(url)
        data = self.assert_response_success(response)
        
        # 验证返回的密码
        self.assertIn("password", data)
        
        # 验证访问日志
        self.assert_access_logged(self.other_password_entry, "copy")


class PasswordToggleFavoriteViewTest(BasePasswordPermissionTestCase):
    """密码收藏切换视图测试"""
    
    def test_toggle_favorite_own_password(self):
        """测试切换自己密码的收藏状态"""
        self.authenticate_user()
        self.clear_logs()
        
        # 初始状态为非收藏
        self.assertFalse(self.password_entry.is_favorite)
        
        url = self.get_url("password_toggle_favorite", pk=self.password_entry.id)
        
        # 第一次切换：设为收藏
        response = self.client.post(url)
        data = self.assert_response_success(response)
        
        self.password_entry.refresh_from_db()
        self.assertTrue(self.password_entry.is_favorite)
        self.assertTrue(data["is_favorite"])
        
        # 验证操作日志
        self.assert_operation_logged("password_favorite", "password_entry")
        
        # 第二次切换：取消收藏
        response = self.client.post(url)
        data = self.assert_response_success(response)
        
        self.password_entry.refresh_from_db()
        self.assertFalse(self.password_entry.is_favorite)
        self.assertFalse(data["is_favorite"])
    
    def test_toggle_favorite_others_password_no_permission(self):
        """测试切换他人密码收藏状态（无权限）"""
        self.authenticate_user()
        
        url = self.get_url("password_toggle_favorite", pk=self.other_password_entry.id)
        
        response = self.client.post(url)
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)


class PasswordUpdateOnlyViewTest(BasePasswordPermissionTestCase):
    """密码更新视图测试"""
    
    def test_update_password_only_success(self):
        """测试成功更新密码"""
        self.authenticate_user()
        self.clear_logs()
        
        url = self.get_url("password_update_only", pk=self.password_entry.id)
        new_password = "new_secure_password_456"
        data = {"password": new_password}
        
        response = self.client.patch(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证密码已更新
        self.password_entry.refresh_from_db()
        decrypted_password = decrypt_data(self.password_entry.password)
        self.assertEqual(decrypted_password, new_password)
        
        # 验证密码历史记录
        history_exists = PasswordHistory.objects.filter(
            password_entry=self.password_entry,
            changed_by=self.user
        ).exists()
        self.assertTrue(history_exists)
        
        # 验证操作日志
        self.assert_operation_logged("password_update", "password_entry")
    
    def test_update_password_invalid_data(self):
        """测试更新密码时数据无效"""
        self.authenticate_user()
        
        url = self.get_url("password_update_only", pk=self.password_entry.id)
        data = {"password": ""}  # 空密码
        
        response = self.client.patch(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_update_others_password_no_permission(self):
        """测试更新他人密码（无权限）"""
        self.authenticate_user()
        
        url = self.get_url("password_update_only", pk=self.other_password_entry.id)
        data = {"password": "hacked_password"}
        
        response = self.client.patch(url, data, format="json")
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)


class PasswordInfoUpdateViewTest(BasePasswordPermissionTestCase):
    """密码信息更新视图测试"""
    
    def test_update_password_info_success(self):
        """测试成功更新密码信息"""
        self.authenticate_user()
        self.clear_logs()
        
        url = self.get_url("password_info_update", pk=self.password_entry.id)
        data = {
            "title": "Updated Title",
            "username": "updated_user",
            "url": "https://updated.example.com",
            "notes": "Updated notes",
            "ip_address": "*************",
            "port": 8080
        }
        
        response = self.client.patch(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证信息已更新
        self.password_entry.refresh_from_db()
        self.assertEqual(self.password_entry.title, data["title"])
        self.assertEqual(self.password_entry.username, data["username"])
        self.assertEqual(self.password_entry.url, data["url"])
        self.assertEqual(self.password_entry.notes, data["notes"])
        self.assertEqual(self.password_entry.ip_address, data["ip_address"])
        self.assertEqual(self.password_entry.port, data["port"])
        
        # 验证操作日志
        self.assert_operation_logged("password_update", "password_entry")
    
    def test_update_password_info_partial(self):
        """测试部分更新密码信息"""
        self.authenticate_user()
        
        url = self.get_url("password_info_update", pk=self.password_entry.id)
        data = {"title": "Only Title Updated"}
        
        response = self.client.patch(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证只有标题被更新
        self.password_entry.refresh_from_db()
        self.assertEqual(self.password_entry.title, data["title"])
    
    def test_update_password_info_invalid_data(self):
        """测试更新密码信息时数据无效"""
        self.authenticate_user()
        
        url = self.get_url("password_info_update", pk=self.password_entry.id)
        data = {
            "title": "",  # 标题不能为空
            "port": 99999  # 端口号超出范围
        }
        
        response = self.client.patch(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_update_others_password_info_no_permission(self):
        """测试更新他人密码信息（无权限）"""
        self.authenticate_user()
        
        url = self.get_url("password_info_update", pk=self.other_password_entry.id)
        data = {"title": "Hacked Title"}
        
        response = self.client.patch(url, data, format="json")
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)


class BatchDeletePasswordsViewTest(BasePasswordAPITestCase):
    """批量删除密码视图测试"""
    
    def test_batch_delete_success(self):
        """测试成功批量删除密码"""
        # 创建多个密码条目
        password1 = PasswordEntryFactory(owner=self.user)
        password2 = PasswordEntryFactory(owner=self.user)
        password3 = PasswordEntryFactory(owner=self.user)
        
        self.authenticate_user()
        self.clear_logs()
        
        url = reverse("password_batch_delete")
        data = {
            "password_ids": [
                str(password1.id),
                str(password2.id),
                str(password3.id)
            ]
        }
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证密码已被软删除
        for password in [password1, password2, password3]:
            password.refresh_from_db()
            self.assertTrue(password.is_deleted)
            self.assertIsNotNone(password.deleted_at)
        
        # 验证响应数据
        self.assertEqual(response_data["deleted_count"], 3)
        self.assertEqual(len(response_data["deleted_passwords"]), 3)
        
        # 验证操作日志
        self.assert_operation_logged("password_batch_delete", "password_entry")
    
    def test_batch_delete_empty_list(self):
        """测试批量删除空列表"""
        self.authenticate_user()
        
        url = reverse("password_batch_delete")
        data = {"password_ids": []}
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_batch_delete_others_passwords(self):
        """测试批量删除他人密码（无权限）"""
        # 创建他人的密码
        other_password = PasswordEntryFactory(owner=self.other_user)
        
        self.authenticate_user()
        
        url = reverse("password_batch_delete")
        data = {"password_ids": [str(other_password.id)]}
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证没有密码被删除
        self.assertEqual(response_data["deleted_count"], 0)
        
        # 验证他人密码未被删除
        other_password.refresh_from_db()
        self.assertFalse(other_password.is_deleted)
    
    def test_batch_delete_mixed_permissions(self):
        """测试批量删除混合权限的密码"""
        # 创建自己的密码和他人的密码
        own_password = PasswordEntryFactory(owner=self.user)
        other_password = PasswordEntryFactory(owner=self.other_user)
        
        self.authenticate_user()
        self.clear_logs()
        
        url = reverse("password_batch_delete")
        data = {
            "password_ids": [
                str(own_password.id),
                str(other_password.id)
            ]
        }
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证只有自己的密码被删除
        self.assertEqual(response_data["deleted_count"], 1)
        
        own_password.refresh_from_db()
        self.assertTrue(own_password.is_deleted)
        
        other_password.refresh_from_db()
        self.assertFalse(other_password.is_deleted)
