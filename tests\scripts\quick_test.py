#!/usr/bin/env python3
"""
快速测试脚本 - 用于快速验证用户管理CRUD API功能
"""
import os
import sys
import requests
import json
import time
from datetime import datetime

# 配置
BASE_URL = 'http://localhost:8001'
API_BASE = f'{BASE_URL}/api/users'

class QuickAPITester:
    """快速API测试器"""
    
    def __init__(self, base_url=BASE_URL):
        self.base_url = base_url
        self.api_base = f'{base_url}/api/users'
        self.session = requests.Session()
        self.token = None
        self.test_results = []
        
    def log_test(self, test_name, success, message="", duration=0):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name} ({duration:.3f}s)")
        if message:
            print(f"    {message}")
        
        self.test_results.append({
            'test_name': test_name,
            'success': success,
            'message': message,
            'duration': duration,
            'timestamp': datetime.now().isoformat()
        })
    
    def test_server_connection(self):
        """测试服务器连接"""
        start_time = time.time()
        try:
            response = requests.get(f'{self.base_url}/admin/', timeout=5)
            success = response.status_code in [200, 302, 404]  # 任何有效响应都表示服务器在运行
            message = f"服务器响应状态码: {response.status_code}"
        except requests.exceptions.RequestException as e:
            success = False
            message = f"连接失败: {str(e)}"
        
        duration = time.time() - start_time
        self.log_test("服务器连接测试", success, message, duration)
        return success
    
    def test_login(self, username='admin', password='admin123'):
        """测试登录功能"""
        start_time = time.time()
        try:
            login_data = {
                'username': username,
                'password': password
            }
            
            response = self.session.post(
                f'{self.api_base}/auth/login/',
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'access_token' in data or ('data' in data and 'access' in data['data']):
                    # 兼容不同的响应格式
                    if 'access_token' in data:
                        self.token = data['access_token']
                    else:
                        self.token = data['data']['access']
                    
                    self.session.headers.update({
                        'Authorization': f'Bearer {self.token}'
                    })
                    success = True
                    message = f"登录成功，用户: {username}"
                else:
                    success = False
                    message = f"响应中未找到token: {data}"
            else:
                success = False
                message = f"登录失败，状态码: {response.status_code}, 响应: {response.text[:200]}"
                
        except requests.exceptions.RequestException as e:
            success = False
            message = f"登录请求失败: {str(e)}"
        
        duration = time.time() - start_time
        self.log_test("用户登录测试", success, message, duration)
        return success
    
    def test_user_list_api(self):
        """测试用户列表API"""
        start_time = time.time()
        try:
            response = self.session.get(f'{self.api_base}/crud/users/', timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and 'data' in data:
                    user_count = len(data['data'])
                    success = True
                    message = f"获取用户列表成功，用户数量: {user_count}"
                else:
                    success = False
                    message = f"响应格式错误: {data}"
            else:
                success = False
                message = f"请求失败，状态码: {response.status_code}, 响应: {response.text[:200]}"
                
        except requests.exceptions.RequestException as e:
            success = False
            message = f"请求异常: {str(e)}"
        
        duration = time.time() - start_time
        self.log_test("用户列表API测试", success, message, duration)
        return success
    
    def test_user_create_api(self):
        """测试用户创建API"""
        start_time = time.time()
        try:
            user_data = {
                'username': f'test_user_{int(time.time())}',
                'email': f'test_{int(time.time())}@example.com',
                'name': '测试用户',
                'password': 'TestPassword123!',
                'password_confirm': 'TestPassword123!'
            }
            
            response = self.session.post(
                f'{self.api_base}/crud/users/',
                json=user_data,
                timeout=10
            )
            
            if response.status_code == 201:
                data = response.json()
                if data.get('success') and 'data' in data:
                    created_user = data['data']
                    success = True
                    message = f"创建用户成功，ID: {created_user.get('id')}, 用户名: {created_user.get('username')}"
                    # 保存创建的用户ID用于后续测试
                    self.created_user_id = created_user.get('id')
                else:
                    success = False
                    message = f"响应格式错误: {data}"
            else:
                success = False
                message = f"创建失败，状态码: {response.status_code}, 响应: {response.text[:200]}"
                
        except requests.exceptions.RequestException as e:
            success = False
            message = f"请求异常: {str(e)}"
        
        duration = time.time() - start_time
        self.log_test("用户创建API测试", success, message, duration)
        return success
    
    def test_user_detail_api(self):
        """测试用户详情API"""
        if not hasattr(self, 'created_user_id') or not self.created_user_id:
            self.log_test("用户详情API测试", False, "没有可用的用户ID进行测试")
            return False
        
        start_time = time.time()
        try:
            response = self.session.get(
                f'{self.api_base}/crud/users/{self.created_user_id}/',
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and 'data' in data:
                    user_detail = data['data']
                    success = True
                    message = f"获取用户详情成功，用户名: {user_detail.get('username')}"
                else:
                    success = False
                    message = f"响应格式错误: {data}"
            else:
                success = False
                message = f"请求失败，状态码: {response.status_code}, 响应: {response.text[:200]}"
                
        except requests.exceptions.RequestException as e:
            success = False
            message = f"请求异常: {str(e)}"
        
        duration = time.time() - start_time
        self.log_test("用户详情API测试", success, message, duration)
        return success
    
    def test_user_update_api(self):
        """测试用户更新API"""
        if not hasattr(self, 'created_user_id') or not self.created_user_id:
            self.log_test("用户更新API测试", False, "没有可用的用户ID进行测试")
            return False
        
        start_time = time.time()
        try:
            update_data = {
                'name': '更新后的测试用户',
                'phone': '13800138000'
            }
            
            response = self.session.patch(
                f'{self.api_base}/crud/users/{self.created_user_id}/',
                json=update_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and 'data' in data:
                    updated_user = data['data']
                    success = True
                    message = f"更新用户成功，新姓名: {updated_user.get('name')}"
                else:
                    success = False
                    message = f"响应格式错误: {data}"
            else:
                success = False
                message = f"更新失败，状态码: {response.status_code}, 响应: {response.text[:200]}"
                
        except requests.exceptions.RequestException as e:
            success = False
            message = f"请求异常: {str(e)}"
        
        duration = time.time() - start_time
        self.log_test("用户更新API测试", success, message, duration)
        return success
    
    def test_department_list_api(self):
        """测试部门列表API"""
        start_time = time.time()
        try:
            response = self.session.get(f'{self.api_base}/crud/departments/', timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    dept_count = len(data.get('data', []))
                    success = True
                    message = f"获取部门列表成功，部门数量: {dept_count}"
                else:
                    success = False
                    message = f"响应格式错误: {data}"
            else:
                success = False
                message = f"请求失败，状态码: {response.status_code}, 响应: {response.text[:200]}"
                
        except requests.exceptions.RequestException as e:
            success = False
            message = f"请求异常: {str(e)}"
        
        duration = time.time() - start_time
        self.log_test("部门列表API测试", success, message, duration)
        return success
    
    def test_search_functionality(self):
        """测试搜索功能"""
        start_time = time.time()
        try:
            response = self.session.get(
                f'{self.api_base}/crud/users/?search=test',
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result_count = len(data.get('data', []))
                    success = True
                    message = f"搜索功能正常，找到 {result_count} 个结果"
                else:
                    success = False
                    message = f"响应格式错误: {data}"
            else:
                success = False
                message = f"搜索失败，状态码: {response.status_code}, 响应: {response.text[:200]}"
                
        except requests.exceptions.RequestException as e:
            success = False
            message = f"请求异常: {str(e)}"
        
        duration = time.time() - start_time
        self.log_test("搜索功能测试", success, message, duration)
        return success
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始运行用户管理CRUD API快速测试...")
        print("=" * 60)
        
        start_time = time.time()
        
        # 测试序列
        tests = [
            self.test_server_connection,
            self.test_login,
            self.test_user_list_api,
            self.test_user_create_api,
            self.test_user_detail_api,
            self.test_user_update_api,
            self.test_department_list_api,
            self.test_search_functionality
        ]
        
        passed = 0
        failed = 0
        
        for test in tests:
            try:
                if test():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                print(f"❌ 测试执行异常: {test.__name__} - {str(e)}")
                failed += 1
        
        total_time = time.time() - start_time
        
        # 生成报告
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  总耗时: {total_time:.2f}秒")
        print(f"✅ 通过: {passed}")
        print(f"❌ 失败: {failed}")
        print(f"📊 成功率: {passed/(passed+failed)*100:.1f}%")
        
        if failed == 0:
            print("\n🎉 所有测试通过！API功能正常。")
        else:
            print(f"\n⚠️  有 {failed} 个测试失败，请检查API配置和服务状态。")
        
        # 保存测试结果
        self.save_results(total_time, passed, failed)
        
        return failed == 0
    
    def save_results(self, total_time, passed, failed):
        """保存测试结果"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_time': total_time,
                'passed': passed,
                'failed': failed,
                'success_rate': passed/(passed+failed)*100 if (passed+failed) > 0 else 0
            },
            'tests': self.test_results
        }
        
        # 确保reports目录存在
        reports_dir = 'reports'
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        
        # 保存结果
        report_file = os.path.join(reports_dir, f'quick_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"📄 详细报告已保存到: {report_file}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='用户管理CRUD API快速测试工具')
    parser.add_argument('--url', default='http://localhost:8001', help='API服务器地址')
    parser.add_argument('--username', default='admin', help='登录用户名')
    parser.add_argument('--password', default='admin123', help='登录密码')
    
    args = parser.parse_args()
    
    tester = QuickAPITester(args.url)
    
    # 设置登录凭据
    if args.username != 'admin' or args.password != 'admin123':
        tester.test_login = lambda: tester.test_login(args.username, args.password)
    
    success = tester.run_all_tests()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
