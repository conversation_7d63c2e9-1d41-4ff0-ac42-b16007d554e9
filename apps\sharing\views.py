from rest_framework import generics, status, permissions, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from .models import OneTimeLink
from .serializers import (
    ShareLinkSerializer,
    ShareLinkAccessSerializer,
    ShareLinkPasswordSerializer,
    AuthenticatedShareLinkAccessSerializer,
)
from apps.passwords.models import PasswordEntry
from apps.audit.models import BusinessOperationLog, PasswordAccessLog
from utils.permissions import IsOwnerOrSharedWith
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


class SharingPageNumberPagination(PageNumberPagination):
    """分享管理分页类"""

    page_size = 20
    page_size_query_param = "page_size"
    max_page_size = 100


class ShareLinkListCreateView(generics.ListCreateAPIView):
    """分享链接列表和创建视图"""

    serializer_class = ShareLinkSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取当前用户的分享链接"""
        return (
            OneTimeLink.objects.filter(created_by=self.request.user, status="active")
            .select_related("password_entry")
            .order_by("-created_at")
        )

    def perform_create(self, serializer):
        """创建分享链接"""
        instance = serializer.save(created_by=self.request.user)

        # 记录操作日志
        BusinessOperationLog.objects.create(
            user=self.request.user,
            action_type="password_share",
            target_type="password_entry",
            target_id=str(instance.password_entry.id),
            target_name=instance.password_entry.title,
            ip_address=self.request.META.get("REMOTE_ADDR", ""),
            user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
            extra_data={
                "description": f"创建分享链接: {instance.password_entry.title}",
                "token": instance.token,
                "expires_at": (
                    instance.expires_at.isoformat() if instance.expires_at else None
                ),
                "status": instance.status,
                "require_password": instance.require_password,
            },
        )


class ShareLinkDetailView(generics.RetrieveUpdateDestroyAPIView):
    """分享链接详情视图"""

    serializer_class = ShareLinkSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return OneTimeLink.objects.filter(created_by=self.request.user).select_related(
            "password_entry"
        )

    def perform_update(self, serializer):
        """更新分享链接"""
        instance = serializer.save()

        # 记录操作日志
        BusinessOperationLog.objects.create(
            user=self.request.user,
            action_type="password_share",
            target_type="share_link",
            target_id=str(instance.id),
            target_name=instance.password_entry.title,
            ip_address=self.request.META.get("REMOTE_ADDR", ""),
            user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
            extra_data={
                "description": f"更新分享链接: {instance.password_entry.title}",
                "token": instance.token,
                "expires_at": (
                    instance.expires_at.isoformat() if instance.expires_at else None
                ),
                "max_access_count": instance.max_access_count,
                "is_active": instance.is_active,
            },
        )

    def perform_destroy(self, instance):
        """删除分享链接（软删除）"""
        instance.is_active = False
        instance.save()

        # 记录操作日志
        BusinessOperationLog.objects.create(
            user=self.request.user,
            action_type="share_revoke",
            target_type="share_link",
            target_id=str(instance.id),
            target_name=instance.password_entry.title,
            ip_address=self.request.META.get("REMOTE_ADDR", ""),
            user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
            extra_data={
                "description": f"删除分享链接: {instance.password_entry.title}",
                "token": instance.token,
                "password_entry_title": instance.password_entry.title,
            },
        )


@api_view(["GET", "POST"])
@permission_classes([permissions.AllowAny])
def share_link_access(request, token):
    """分享链接访问接口"""
    share_link = get_object_or_404(OneTimeLink, token=token)

    if request.method == "GET":
        # 获取分享链接信息（不包含密码）
        response_data = {
            "title": share_link.password_entry.title,
            "username": share_link.password_entry.username,
            "url": share_link.password_entry.url,
            "ip_address": share_link.password_entry.ip_address,
            "port": share_link.password_entry.port,
            "database_type": share_link.password_entry.database_type,
            "database_name": share_link.password_entry.database_name,
            "system_type": share_link.password_entry.system_type,
            "os_type": share_link.password_entry.os_type,
            "mdw_type": share_link.password_entry.mdw_type,
            "environment": share_link.password_entry.environment,
            "project_name": share_link.password_entry.project_name,
            "notes": share_link.password_entry.notes,
            "require_password": share_link.require_password,
            "is_expired": share_link.is_expired,
            "is_valid": share_link.is_valid,
            "is_authenticated": request.user.is_authenticated,
            "created_by": {
                "id": share_link.created_by.id,
                "username": share_link.created_by.username,
                "first_name": share_link.created_by.first_name,
                "last_name": share_link.created_by.last_name,
                "full_name": share_link.created_by.get_full_name(),
            },
        }
        return Response(response_data)

    elif request.method == "POST":
        # 检查链接是否有效
        if not share_link.is_valid:
            return Response(
                {"error": "分享链接已失效"}, status=status.HTTP_403_FORBIDDEN
            )

        if share_link.is_expired:
            return Response(
                {"error": "分享链接已过期"}, status=status.HTTP_403_FORBIDDEN
            )

        # 如果用户已认证，直接返回密码信息，跳过访问密码验证
        if request.user.is_authenticated:
            # 标记为已使用
            share_link.mark_as_used(
                ip_address=request.META.get("REMOTE_ADDR"),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
            )

            # 记录访问日志（已认证用户）
            PasswordAccessLog.objects.create(
                password_entry=share_link.password_entry,
                user=request.user,  # 记录认证用户
                access_type="view",
                ip_address=request.META.get("REMOTE_ADDR"),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
                access_source="onetime_link",
                source_id=share_link.token,
            )

            # 返回密码信息
            password_serializer = ShareLinkPasswordSerializer(share_link.password_entry)
            return Response(password_serializer.data)

        # 未认证用户需要验证访问密码
        serializer = ShareLinkAccessSerializer(
            data=request.data, context={"share_link": share_link}
        )

        if serializer.is_valid():
            # 标记为已使用
            share_link.mark_as_used(
                ip_address=request.META.get("REMOTE_ADDR"),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
            )

            # 记录访问日志（匿名访问）
            PasswordAccessLog.objects.create(
                password_entry=share_link.password_entry,
                user=None,  # 匿名访问
                access_type="view",
                ip_address=request.META.get("REMOTE_ADDR"),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
                access_source="onetime_link",
                source_id=share_link.token,
            )

            # 返回密码信息
            password_serializer = ShareLinkPasswordSerializer(share_link.password_entry)
            return Response(password_serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ShareLinkStatsView(APIView):
    """分享链接统计视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, pk):
        """获取分享链接统计信息"""
        share_link = get_object_or_404(OneTimeLink, pk=pk, created_by=request.user)

        # 获取访问日志统计
        access_logs = PasswordAccessLog.objects.filter(
            access_source="onetime_link", source_id=share_link.token
        )

        stats = {
            "status": share_link.status,
            "is_expired": share_link.is_expired,
            "is_valid": share_link.is_valid,
            "created_at": share_link.created_at,
            "expires_at": share_link.expires_at,
            "accessed_at": share_link.accessed_at,
            "recent_accesses": [
                {
                    "timestamp": log.timestamp,
                    "ip_address": log.ip_address,
                    "user_agent": log.user_agent,
                }
                for log in access_logs.order_by("-timestamp")[:10]
            ],
        }

        return Response(stats)


class AuthenticatedShareLinkAccessView(APIView):
    """已认证用户分享链接访问视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, token):
        """已认证用户访问分享链接获取密码"""
        share_link = get_object_or_404(OneTimeLink, token=token)

        # 使用专门的序列化器验证
        serializer = AuthenticatedShareLinkAccessSerializer(
            data={}, context={"share_link": share_link, "request": request}
        )

        if serializer.is_valid():
            # 标记为已使用
            share_link.mark_as_used(
                ip_address=request.META.get("REMOTE_ADDR"),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
            )

            # 记录访问日志（已认证用户）
            PasswordAccessLog.objects.create(
                password_entry=share_link.password_entry,
                user=request.user,
                access_type="view",
                ip_address=request.META.get("REMOTE_ADDR"),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
                access_source="onetime_link_authenticated",
                source_id=share_link.token,
            )

            # 返回密码信息
            password_serializer = ShareLinkPasswordSerializer(share_link.password_entry)
            return Response(password_serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
