import re
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


def validate_password_strength(password):
    """
    验证密码强度
    """
    if len(password) < 8:
        raise ValidationError(_('密码长度至少为8位'))
    
    if not re.search(r'[A-Z]', password):
        raise ValidationError(_('密码必须包含至少一个大写字母'))
    
    if not re.search(r'[a-z]', password):
        raise ValidationError(_('密码必须包含至少一个小写字母'))
    
    if not re.search(r'\d', password):
        raise ValidationError(_('密码必须包含至少一个数字'))
    
    if not re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]', password):
        raise ValidationError(_('密码必须包含至少一个特殊字符'))


def validate_phone_number(phone):
    """
    验证手机号格式
    """
    pattern = r'^1[3-9]\d{9}$'
    if not re.match(pattern, phone):
        raise ValidationError(_('请输入有效的手机号码'))


def validate_url(url):
    """
    验证URL格式
    """
    pattern = r'^https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:\w*))?)?$'
    if not re.match(pattern, url):
        raise ValidationError(_('请输入有效的URL地址'))


def validate_domain(domain):
    """
    验证域名格式
    """
    pattern = r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+'r'[a-zA-Z]{2,}$'
    if not re.match(pattern, domain):
        raise ValidationError(_('请输入有效的域名'))


def validate_ip_address(ip):
    """
    验证IP地址格式
    """
    pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
    if not re.match(pattern, ip):
        raise ValidationError(_('请输入有效的IP地址'))