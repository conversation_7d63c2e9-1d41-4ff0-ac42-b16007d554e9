# Generated by Django 5.2.4 on 2025-07-30 16:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("passwords", "0010_remove_passwordentry_is_compromised"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="passwordentry",
            name="deleted_at",
            field=models.DateTimeField(blank=True, null=True, verbose_name="删除时间"),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="deleted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="deleted_passwords",
                to=settings.AUTH_USER_MODEL,
                verbose_name="删除者",
            ),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="is_deleted",
            field=models.BooleanField(default=False, verbose_name="是否已删除"),
        ),
    ]
