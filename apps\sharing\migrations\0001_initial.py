# Generated by Django 5.2.4 on 2025-07-28 13:17

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('passwords', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OneTimeLink',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('token', models.CharField(max_length=64, unique=True, verbose_name='访问令牌')),
                ('status', models.CharField(choices=[('active', '活跃'), ('used', '已使用'), ('expired', '已过期'), ('revoked', '已撤销')], default='active', max_length=10, verbose_name='状态')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
                ('require_password', models.<PERSON><PERSON>an<PERSON>ield(default=False, verbose_name='需要密码')),
                ('access_password', models.CharField(blank=True, max_length=128, verbose_name='访问密码')),
                ('accessed_at', models.DateTimeField(blank=True, null=True, verbose_name='访问时间')),
                ('accessed_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='访问IP')),
                ('accessed_user_agent', models.TextField(blank=True, verbose_name='访问用户代理')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('password_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='onetime_links', to='passwords.passwordentry', verbose_name='密码条目')),
            ],
            options={
                'verbose_name': '一次性链接',
                'verbose_name_plural': '一次性链接',
                'db_table': 'onetime_links',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ShareRecord',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('share_type', models.CharField(choices=[('view', '查看'), ('copy', '复制'), ('edit', '编辑')], default='view', max_length=10, verbose_name='分享类型')),
                ('status', models.CharField(choices=[('active', '活跃'), ('expired', '已过期'), ('revoked', '已撤销'), ('used', '已使用')], default='active', max_length=10, verbose_name='状态')),
                ('can_reshare', models.BooleanField(default=False, verbose_name='允许再次分享')),
                ('require_password', models.BooleanField(default=False, verbose_name='需要密码')),
                ('access_password', models.CharField(blank=True, max_length=128, verbose_name='访问密码')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='过期时间')),
                ('max_access_count', models.IntegerField(blank=True, null=True, verbose_name='最大访问次数')),
                ('access_count', models.IntegerField(default=0, verbose_name='访问次数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('last_accessed', models.DateTimeField(blank=True, null=True, verbose_name='最后访问时间')),
                ('password_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shares', to='passwords.passwordentry', verbose_name='密码条目')),
                ('shared_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shared_passwords', to=settings.AUTH_USER_MODEL, verbose_name='分享者')),
                ('shared_with', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='received_passwords', to=settings.AUTH_USER_MODEL, verbose_name='接收者')),
            ],
            options={
                'verbose_name': '分享记录',
                'verbose_name_plural': '分享记录',
                'db_table': 'password_shares',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ShareAccessLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('view', '查看'), ('copy_username', '复制用户名'), ('copy_password', '复制密码'), ('copy_url', '复制网址'), ('download_attachment', '下载附件')], max_length=20, verbose_name='操作类型')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='访问时间')),
                ('accessed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='访问用户')),
                ('onetime_link', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='access_logs', to='sharing.onetimelink', verbose_name='一次性链接')),
                ('share_record', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='access_logs', to='sharing.sharerecord', verbose_name='分享记录')),
            ],
            options={
                'verbose_name': '分享访问日志',
                'verbose_name_plural': '分享访问日志',
                'db_table': 'share_access_logs',
                'ordering': ['-created_at'],
            },
        ),
    ]
