#!/usr/bin/env python
"""
查找代码中所有team字段的引用
"""
import os
import re

def search_team_references():
    """搜索team字段引用"""
    print("🔍 搜索代码中的team字段引用")
    print("=" * 60)
    
    # 要搜索的目录和文件
    search_paths = [
        "apps/users/",
        "apps/audit/",
        "apps/passwords/",
        "config/",
    ]
    
    # 要搜索的模式
    patterns = [
        r'\.team\b',           # .team字段访问
        r'team_id',            # team_id字段
        r'team__',             # team__相关查询
        r'select_related.*team', # select_related中的team
        r'prefetch_related.*team', # prefetch_related中的team
        r'filter.*team',       # filter中的team
        r'exclude.*team',      # exclude中的team
        r'order_by.*team',     # order_by中的team
        r'Team\.',             # Team模型引用
        r'from.*Team',         # Team模型导入
        r'import.*Team',       # Team模型导入
    ]
    
    found_references = []
    
    for search_path in search_paths:
        if os.path.exists(search_path):
            for root, dirs, files in os.walk(search_path):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                lines = content.split('\n')
                                
                                for i, line in enumerate(lines, 1):
                                    # 跳过注释行
                                    if line.strip().startswith('#'):
                                        continue
                                    
                                    for pattern in patterns:
                                        if re.search(pattern, line, re.IGNORECASE):
                                            found_references.append({
                                                'file': file_path,
                                                'line': i,
                                                'content': line.strip(),
                                                'pattern': pattern
                                            })
                        except Exception as e:
                            print(f"   ⚠️ 无法读取文件 {file_path}: {e}")
    
    if found_references:
        print(f"❌ 找到 {len(found_references)} 个team字段引用:")
        
        # 按文件分组显示
        files_with_refs = {}
        for ref in found_references:
            file_path = ref['file']
            if file_path not in files_with_refs:
                files_with_refs[file_path] = []
            files_with_refs[file_path].append(ref)
        
        for file_path, refs in files_with_refs.items():
            print(f"\n📄 {file_path}:")
            for ref in refs:
                print(f"   Line {ref['line']}: {ref['content']}")
                print(f"   Pattern: {ref['pattern']}")
    else:
        print("✅ 没有找到team字段引用")
    
    return found_references

def check_specific_files():
    """检查特定可能有问题的文件"""
    print("\n🔍 检查特定文件")
    print("=" * 60)
    
    files_to_check = [
        "apps/users/views.py",
        "apps/users/serializers.py",
        "apps/users/models.py",
        "apps/users/admin.py",
        "apps/audit/views.py",
        "apps/audit/models.py",
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n📄 检查 {file_path}:")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 检查是否包含team相关内容
                if 'team' in content.lower():
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if 'team' in line.lower() and not line.strip().startswith('#'):
                            print(f"   Line {i}: {line.strip()}")
                else:
                    print("   ✅ 没有发现team引用")
                    
            except Exception as e:
                print(f"   ❌ 读取文件失败: {e}")
        else:
            print(f"   ⚠️ 文件不存在: {file_path}")

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 建议的解决方案")
    print("=" * 60)
    
    solutions = [
        "1. 重启Django开发服务器",
        "2. 清除Django缓存 (如果使用了缓存)",
        "3. 检查是否有自定义查询集或管理器引用了team字段",
        "4. 检查是否有信号处理器引用了team字段",
        "5. 尝试重新加载Python模块",
        "6. 检查是否有其他应用引用了User.team字段",
        "7. 检查数据库连接是否指向正确的数据库文件",
    ]
    
    for solution in solutions:
        print(f"   {solution}")
    
    print("\n🔧 立即尝试的修复命令:")
    print("   1. 重启服务器: Ctrl+C 然后重新运行 python manage.py runserver")
    print("   2. 清除缓存: python manage.py shell -c \"from django.core.cache import cache; cache.clear()\"")
    print("   3. 重新迁移: python manage.py migrate --run-syncdb")

def create_test_script():
    """创建测试脚本"""
    print("\n📝 创建测试脚本")
    print("=" * 60)
    
    test_script = '''#!/usr/bin/env python
"""
测试用户查询是否正常
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def test_user_queries():
    """测试各种用户查询"""
    from apps.users.models import User
    
    try:
        print("测试1: 基本查询")
        users = User.objects.all()
        print(f"✅ 成功，找到 {users.count()} 个用户")
        
        print("测试2: select_related查询")
        users = User.objects.select_related('department', 'role')
        print(f"✅ 成功，查询集创建正常")
        
        print("测试3: 获取第一个用户")
        user = users.first()
        if user:
            print(f"✅ 成功，用户: {user.username}")
            print(f"   部门: {user.department}")
            print(f"   角色: {user.role}")
        
        print("测试4: 过滤查询")
        active_users = User.objects.filter(is_active=True)
        print(f"✅ 成功，活跃用户: {active_users.count()}")
        
        print("测试5: 复杂查询")
        users_with_dept = User.objects.filter(department__isnull=False)
        print(f"✅ 成功，有部门的用户: {users_with_dept.count()}")
        
        print("\\n🎉 所有查询测试通过！")
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_user_queries()
'''
    
    with open('debug/test_user_queries.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 测试脚本已创建: debug/test_user_queries.py")
    print("   运行: python debug/test_user_queries.py")

def main():
    """主函数"""
    print("🚀 开始查找team字段引用")
    print("=" * 80)
    
    # 1. 搜索team引用
    references = search_team_references()
    
    # 2. 检查特定文件
    check_specific_files()
    
    # 3. 建议解决方案
    suggest_solutions()
    
    # 4. 创建测试脚本
    create_test_script()
    
    print("\n" + "=" * 80)
    print("📋 分析总结")
    print("=" * 80)
    
    if references:
        print(f"❌ 找到 {len(references)} 个可能的team字段引用")
        print("💡 建议: 清理这些引用后重试")
    else:
        print("✅ 没有找到明显的team字段引用")
        print("💡 建议: 尝试重启Django服务器或清除缓存")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
