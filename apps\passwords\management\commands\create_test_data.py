"""
创建测试数据的Django管理命令
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.passwords.models import (
    PasswordEntry,
    PasswordEntryGroup,
    PasswordPermission,
    UserGroupMembership,
    Category,
)
from utils.encryption import encrypt_data
import uuid

User = get_user_model()


class Command(BaseCommand):
    help = "创建测试数据用于权限验证"

    def handle(self, *args, **options):
        self.stdout.write("开始创建测试数据...")

        # 创建测试用户
        self.create_users()

        # 创建密码组
        self.create_password_groups()

        # 创建分类
        self.create_categories()

        # 创建密码条目
        self.create_password_entries()

        # 创建权限分配
        self.create_permissions()

        self.stdout.write(self.style.SUCCESS("测试数据创建完成！"))

    def create_users(self):
        """创建测试用户"""
        self.stdout.write("创建测试用户...")

        # 创建普通用户
        users_data = [
            {
                "username": "user1",
                "email": "<EMAIL>",
                "password": "password123",
            },
            {
                "username": "user2",
                "email": "<EMAIL>",
                "password": "password123",
            },
            {
                "username": "user3",
                "email": "<EMAIL>",
                "password": "password123",
            },
            {
                "username": "manager1",
                "email": "<EMAIL>",
                "password": "password123",
            },
            {
                "username": "viewer1",
                "email": "<EMAIL>",
                "password": "password123",
            },
        ]

        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data["username"],
                defaults={
                    "email": user_data["email"],
                    "is_active": True,
                },
            )
            if created:
                user.set_password(user_data["password"])
                user.save()
                self.stdout.write(f"  创建用户: {user.username}")

    def create_password_groups(self):
        """创建密码组"""
        self.stdout.write("创建密码组...")

        admin_user = User.objects.get(username="admin")

        groups_data = [
            {"name": "开发组", "description": "开发人员密码组"},
            {"name": "测试组", "description": "测试人员密码组"},
            {"name": "运维组", "description": "运维人员密码组"},
        ]

        for group_data in groups_data:
            group, created = PasswordEntryGroup.objects.get_or_create(
                name=group_data["name"],
                defaults={
                    "description": group_data["description"],
                    "created_by": admin_user,
                },
            )
            if created:
                self.stdout.write(f"  创建密码组: {group.name}")

        # 添加用户到组
        dev_group = PasswordEntryGroup.objects.get(name="开发组")
        test_group = PasswordEntryGroup.objects.get(name="测试组")
        ops_group = PasswordEntryGroup.objects.get(name="运维组")

        # 用户组成员关系
        memberships = [
            (User.objects.get(username="user1"), dev_group),
            (User.objects.get(username="user2"), dev_group),
            (User.objects.get(username="user3"), test_group),
            (User.objects.get(username="manager1"), ops_group),
        ]

        for user, group in memberships:
            membership, created = UserGroupMembership.objects.get_or_create(
                user=user, group=group, defaults={"added_by": admin_user}
            )
            if created:
                self.stdout.write(f"  添加 {user.username} 到 {group.name}")

    def create_categories(self):
        """创建分类"""
        self.stdout.write("创建分类...")

        admin_user = User.objects.get(username="admin")

        categories_data = [
            {"name": "数据库", "description": "数据库相关密码"},
            {"name": "服务器", "description": "服务器登录密码"},
            {"name": "应用系统", "description": "应用系统密码"},
            {"name": "第三方服务", "description": "第三方服务API密钥"},
        ]

        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data["name"],
                user=admin_user,
                defaults={"description": cat_data["description"]},
            )
            if created:
                self.stdout.write(f"  创建分类: {category.name}")

    def create_password_entries(self):
        """创建密码条目"""
        self.stdout.write("创建密码条目...")

        # 获取用户和分类
        admin = User.objects.get(username="admin")
        user1 = User.objects.get(username="user1")
        user2 = User.objects.get(username="user2")
        manager1 = User.objects.get(username="manager1")

        db_category = Category.objects.get(name="数据库")
        server_category = Category.objects.get(name="服务器")
        app_category = Category.objects.get(name="应用系统")

        # 密码条目数据
        passwords_data = [
            {
                "title": "MySQL生产数据库",
                "username": "root",
                "password": "mysql_prod_2024!",
                "ip_address": "*************",
                "port": 3306,
                "database_name": "production",
                "system_type": "database",
                "database_type": "mysql",
                "os_type": "linux",
                "environment": "production",
                "category": db_category,
                "owner": admin,
                "notes": "生产环境MySQL数据库管理员密码",
            },
            {
                "title": "Redis缓存服务器",
                "username": "redis",
                "password": "redis_cache_2024!",
                "ip_address": "*************",
                "port": 6379,
                "system_type": "cache",
                "os_type": "linux",
                "environment": "production",
                "category": db_category,
                "owner": user1,
                "notes": "Redis缓存服务器密码",
            },
            {
                "title": "应用服务器SSH",
                "username": "ubuntu",
                "password": "ssh_server_2024!",
                "ip_address": "*************",
                "port": 22,
                "system_type": "server",
                "os_type": "linux",
                "environment": "production",
                "category": server_category,
                "owner": user2,
                "notes": "应用服务器SSH登录密码",
            },
            {
                "title": "测试环境数据库",
                "username": "testuser",
                "password": "test_db_2024!",
                "ip_address": "*************",
                "port": 3306,
                "database_name": "testdb",
                "system_type": "database",
                "database_type": "mysql",
                "os_type": "linux",
                "environment": "testing",
                "category": db_category,
                "owner": manager1,
                "notes": "测试环境数据库密码",
            },
            {
                "title": "管理后台系统",
                "username": "admin",
                "password": "admin_panel_2024!",
                "url": "https://admin.example.com",
                "system_type": "web",
                "os_type": "other",
                "environment": "production",
                "category": app_category,
                "owner": admin,
                "notes": "管理后台系统登录密码",
            },
        ]

        for pwd_data in passwords_data:
            # 加密密码
            encrypted_password = encrypt_data(pwd_data["password"])

            password_entry, created = PasswordEntry.objects.get_or_create(
                title=pwd_data["title"],
                defaults={
                    "id": uuid.uuid4(),
                    "username": pwd_data["username"],
                    "password": encrypted_password,
                    "ip_address": pwd_data.get("ip_address"),
                    "port": pwd_data.get("port"),
                    "url": pwd_data.get("url"),
                    "database_name": pwd_data.get("database_name"),
                    "system_type": pwd_data["system_type"],
                    "database_type": pwd_data.get("database_type"),
                    "os_type": pwd_data.get("os_type"),
                    "environment": pwd_data["environment"],
                    "category": pwd_data["category"],
                    "owner": pwd_data["owner"],
                    "notes": pwd_data["notes"],
                    "strength": "strong",
                    "created_at": timezone.now(),
                    "updated_at": timezone.now(),
                },
            )
            if created:
                self.stdout.write(
                    f"  创建密码条目: {password_entry.title} (所有者: {password_entry.owner.username})"
                )

    def create_permissions(self):
        """创建权限分配"""
        self.stdout.write("创建权限分配...")

        # 获取用户和密码条目
        admin = User.objects.get(username="admin")
        user1 = User.objects.get(username="user1")
        user2 = User.objects.get(username="user2")
        user3 = User.objects.get(username="user3")
        viewer1 = User.objects.get(username="viewer1")

        mysql_pwd = PasswordEntry.objects.get(title="MySQL生产数据库")
        redis_pwd = PasswordEntry.objects.get(title="Redis缓存服务器")
        ssh_pwd = PasswordEntry.objects.get(title="应用服务器SSH")
        admin_panel = PasswordEntry.objects.get(title="管理后台系统")

        dev_group = PasswordEntryGroup.objects.get(name="开发组")
        test_group = PasswordEntryGroup.objects.get(name="测试组")

        # 权限分配数据
        permissions_data = [
            # MySQL生产数据库权限 (admin是owner，不需要额外权限)
            (mysql_pwd, "user", user1.id, "read", admin),  # user1可以读取
            (mysql_pwd, "user", user2.id, "browse", admin),  # user2只能浏览
            (mysql_pwd, "group", dev_group.id, "browse", admin),  # 开发组可以浏览
            # Redis缓存服务器权限 (user1是owner)
            (redis_pwd, "user", admin.id, "write", user1),  # admin可以编辑
            (redis_pwd, "user", user2.id, "read", user1),  # user2可以读取
            (redis_pwd, "group", dev_group.id, "read", user1),  # 开发组可以读取
            # SSH服务器权限 (user2是owner)
            (ssh_pwd, "user", user1.id, "write", user2),  # user1可以编辑
            (ssh_pwd, "user", viewer1.id, "browse", user2),  # viewer1只能浏览
            (ssh_pwd, "group", test_group.id, "browse", user2),  # 测试组可以浏览
            # 管理后台权限 (admin是owner)
            (admin_panel, "user", user1.id, "write", admin),  # user1可以编辑
            (admin_panel, "user", user3.id, "read", admin),  # user3可以读取
            (admin_panel, "user", viewer1.id, "browse", admin),  # viewer1只能浏览
        ]

        for password, perm_type, target_id, level, granted_by in permissions_data:
            permission, created = PasswordPermission.objects.get_or_create(
                password=password,
                permission_type=perm_type,
                target_id=target_id,
                defaults={
                    "permission_level": level,
                    "granted_by": granted_by,
                    "is_active": True,
                },
            )
            if created:
                target_name = (
                    f"用户组{target_id}" if perm_type == "group" else f"用户{target_id}"
                )
                self.stdout.write(
                    f"  授予权限: {password.title} -> {target_name} ({level})"
                )

        self.stdout.write("权限分配完成！")

        # 输出权限总结
        self.stdout.write("\n=== 权限分配总结 ===")
        self.stdout.write("用户密码所有权:")
        for password in PasswordEntry.objects.all():
            self.stdout.write(
                f"  {password.title} -> {password.owner.username} (owner)"
            )

        self.stdout.write("\n权限分配:")
        for permission in PasswordPermission.objects.all():
            target_obj = permission.get_target_object()
            target_name = getattr(target_obj, "username", None) or getattr(
                target_obj, "name", f"ID:{permission.target_id}"
            )
            self.stdout.write(
                f"  {permission.password.title} -> {target_name} ({permission.permission_level})"
            )
