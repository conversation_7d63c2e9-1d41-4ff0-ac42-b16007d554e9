"""
密码权限管理API视图
提供权限授予、撤销、转移等功能
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from .models import PasswordEntry, PasswordEntryGroup, PasswordPermission
from utils.password_permissions import password_permission_service
from utils.password_decorators import require_password_permission_drf, check_password_owner
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class PasswordPermissionCheckView(APIView):
    """检查用户对密码的权限"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, password_id):
        """
        获取用户对指定密码的权限信息
        
        Returns:
            {
                "permission_level": "admin|write|read|browse|none",
                "can_browse": bool,
                "can_read": bool,
                "can_write": bool,
                "can_admin": bool,
                "is_owner": bool
            }
        """
        try:
            # 检查密码是否存在
            password = get_object_or_404(PasswordEntry, id=password_id, is_deleted=False)
            user_id = request.user.id
            
            # 获取权限信息
            permission_level = password_permission_service.get_user_permission(password_id, user_id)
            is_owner = password_permission_service.is_owner(password_id, user_id)
            
            return Response({
                "permission_level": permission_level,
                "can_browse": password_permission_service.can_browse(password_id, user_id),
                "can_read": password_permission_service.can_read(password_id, user_id),
                "can_write": password_permission_service.can_write(password_id, user_id),
                "can_admin": password_permission_service.can_admin(password_id, user_id),
                "is_owner": is_owner,
                "password_title": password.title
            })
            
        except Exception as e:
            logger.error(f"检查权限失败: {e}")
            return Response({
                "error": "权限检查失败",
                "detail": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PasswordPermissionGrantView(APIView):
    """授予密码权限"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @require_password_permission_drf('admin')
    def post(self, request, password_id):
        """
        授予权限
        
        Body:
            {
                "target_type": "user|group",
                "target_id": int,
                "permission_level": "browse|read|write",
                "expires_at": "2024-12-31T23:59:59Z" (optional)
            }
        """
        try:
            data = request.data
            target_type = data.get('target_type')
            target_id = data.get('target_id')
            permission_level = data.get('permission_level')
            expires_at = data.get('expires_at')
            
            # 验证参数
            if not all([target_type, target_id, permission_level]):
                return Response({
                    "error": "缺少必要参数",
                    "required": ["target_type", "target_id", "permission_level"]
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if target_type not in ['user', 'group']:
                return Response({
                    "error": "target_type必须是'user'或'group'"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if permission_level not in ['browse', 'read', 'write']:
                return Response({
                    "error": "permission_level必须是'browse'、'read'或'write'"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证目标是否存在
            if target_type == 'user':
                if not User.objects.filter(id=target_id).exists():
                    return Response({
                        "error": "用户不存在"
                    }, status=status.HTTP_404_NOT_FOUND)
            else:  # group
                if not PasswordEntryGroup.objects.filter(id=target_id).exists():
                    return Response({
                        "error": "用户组不存在"
                    }, status=status.HTTP_404_NOT_FOUND)
            
            # 授予权限
            result = password_permission_service.grant_permission(
                password_id=password_id,
                target_type=target_type,
                target_id=target_id,
                permission_level=permission_level,
                granted_by_user_id=request.user.id,
                expires_at=expires_at
            )
            
            if result['success']:
                return Response({
                    "message": result['message'],
                    "permission_id": result.get('permission_id')
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    "error": result['error']
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"授予权限失败: {e}")
            return Response({
                "error": "授予权限失败",
                "detail": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PasswordPermissionRevokeView(APIView):
    """撤销密码权限"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @require_password_permission_drf('admin')
    def delete(self, request, password_id):
        """
        撤销权限
        
        Body:
            {
                "target_type": "user|group",
                "target_id": int
            }
        """
        try:
            data = request.data
            target_type = data.get('target_type')
            target_id = data.get('target_id')
            
            # 验证参数
            if not all([target_type, target_id]):
                return Response({
                    "error": "缺少必要参数",
                    "required": ["target_type", "target_id"]
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if target_type not in ['user', 'group']:
                return Response({
                    "error": "target_type必须是'user'或'group'"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 撤销权限
            result = password_permission_service.revoke_permission(
                password_id=password_id,
                target_type=target_type,
                target_id=target_id,
                revoked_by_user_id=request.user.id
            )
            
            if result['success']:
                return Response({
                    "message": result['message']
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "error": result['error']
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"撤销权限失败: {e}")
            return Response({
                "error": "撤销权限失败",
                "detail": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PasswordOwnershipTransferView(APIView):
    """转移密码所有权"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @require_password_permission_drf('admin')
    def post(self, request, password_id):
        """
        转移所有权
        
        Body:
            {
                "new_owner_id": int
            }
        """
        try:
            data = request.data
            new_owner_id = data.get('new_owner_id')
            
            # 验证参数
            if not new_owner_id:
                return Response({
                    "error": "缺少new_owner_id参数"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 转移所有权
            result = password_permission_service.transfer_ownership(
                password_id=password_id,
                new_owner_id=new_owner_id,
                current_owner_id=request.user.id
            )
            
            if result['success']:
                return Response({
                    "message": result['message']
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "error": result['error']
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"转移所有权失败: {e}")
            return Response({
                "error": "转移所有权失败",
                "detail": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PasswordPermissionListView(APIView):
    """获取密码的权限列表"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @require_password_permission_drf('admin')
    def get(self, request, password_id):
        """
        获取密码的所有权限分配
        
        Returns:
            {
                "owner": {...},
                "permissions": [
                    {
                        "id": int,
                        "target_type": "user|group",
                        "target_id": int,
                        "target_name": str,
                        "permission_level": str,
                        "granted_by": {...},
                        "granted_at": str,
                        "expires_at": str|null,
                        "is_active": bool
                    }
                ]
            }
        """
        try:
            # 获取密码条目
            password = get_object_or_404(PasswordEntry, id=password_id, is_deleted=False)
            
            # 获取所有权限
            permissions = PasswordPermission.objects.filter(
                password_id=password_id
            ).select_related('granted_by')
            
            permission_list = []
            for perm in permissions:
                target_obj = perm.get_target_object()
                target_name = getattr(target_obj, 'username', None) or getattr(target_obj, 'name', f'ID:{perm.target_id}')
                
                permission_list.append({
                    "id": perm.id,
                    "target_type": perm.permission_type,
                    "target_id": perm.target_id,
                    "target_name": target_name,
                    "permission_level": perm.permission_level,
                    "granted_by": {
                        "id": perm.granted_by.id,
                        "username": perm.granted_by.username
                    },
                    "granted_at": perm.granted_at.isoformat(),
                    "expires_at": perm.expires_at.isoformat() if perm.expires_at else None,
                    "is_active": perm.is_active,
                    "is_expired": perm.is_expired
                })
            
            return Response({
                "owner": {
                    "id": password.owner.id,
                    "username": password.owner.username
                },
                "permissions": permission_list
            })
            
        except Exception as e:
            logger.error(f"获取权限列表失败: {e}")
            return Response({
                "error": "获取权限列表失败",
                "detail": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_user_accessible_passwords(request):
    """获取用户有权限访问的所有密码列表（仅ID和标题）"""
    try:
        user_id = request.user.id
        
        # 获取用户拥有的密码
        owned_passwords = PasswordEntry.objects.filter(
            owner_id=user_id,
            is_deleted=False
        ).values('id', 'title')
        
        # 获取用户有权限的密码
        user_permissions = PasswordPermission.objects.filter(
            permission_type='user',
            target_id=user_id,
            is_active=True
        ).values_list('password_id', flat=True)
        
        # 获取用户通过组权限访问的密码
        user_groups = password_permission_service.UserGroupMembership.objects.filter(
            user_id=user_id,
            is_active=True
        ).values_list('group_id', flat=True)
        
        group_permissions = PasswordPermission.objects.filter(
            permission_type='group',
            target_id__in=user_groups,
            is_active=True
        ).values_list('password_id', flat=True)
        
        # 获取权限密码的详情
        permission_password_ids = list(user_permissions) + list(group_permissions)
        permission_passwords = PasswordEntry.objects.filter(
            id__in=permission_password_ids,
            is_deleted=False
        ).values('id', 'title')
        
        # 合并结果
        all_passwords = list(owned_passwords) + list(permission_passwords)
        
        # 去重
        seen = set()
        unique_passwords = []
        for pwd in all_passwords:
            if pwd['id'] not in seen:
                seen.add(pwd['id'])
                unique_passwords.append(pwd)
        
        return Response({
            "passwords": unique_passwords,
            "total": len(unique_passwords)
        })
        
    except Exception as e:
        logger.error(f"获取用户可访问密码列表失败: {e}")
        return Response({
            "error": "获取密码列表失败",
            "detail": str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
