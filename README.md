# Vault Project Backend

一个功能完整的密码保险库系统后端，基于Django REST Framework开发。

> **项目名称**: vault_project_backend
> **技术栈**: Django + PostgreSQL + JWT
> **用途**: 为Vault密码管理系统提供安全的后端API服务

## 功能特性

### 🔐 认证与授权
- JWT Token认证
- 多因素认证(MFA)
- 密码重置
- 用户权限管理
- 组织架构管理(部门、团队、角色)

### 🔑 密码管理
- 密码条目的CRUD操作
- 密码加密存储
- 密码强度分析
- 密码生成器
- 分类管理
- 自定义字段
- 文件附件

### 🤝 密码分享
- 用户间密码分享
- 分享链接生成
- 访问权限控制
- 分享统计

### 📊 审计日志
- 操作日志记录
- 访问日志记录
- 安全事件监控
- 用户活动分析
- 日志导出

### ⚙️ 系统管理
- 系统设置管理
- 邮件模板管理
- 备份配置
- 系统状态监控
- 系统维护工具

## 技术栈

- **框架**: Django 4.2 + Django REST Framework
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **认证**: JWT Token + MFA
- **加密**: Fernet对称加密
- **缓存**: Redis
- **任务队列**: Celery
- **邮件**: SMTP

## 快速开始

### 1. 环境要求

- Python 3.8+
- pip
- Redis (可选，用于缓存)

### 2. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 3. 环境配置

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量：
```env
# 数据库配置
DATABASE_URL=sqlite:///db.sqlite3

# 安全密钥
SECRET_KEY=your-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here

# 邮件配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379/0
```

### 4. 启动服务器

使用启动脚本（推荐）：
```bash
python start_server.py
```

或手动启动：
```bash
# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 启动服务器
python manage.py runserver
```

### 5. 访问系统

- **API文档**: http://localhost:8000/api/docs/
- **管理后台**: http://localhost:8000/admin/
- **API根路径**: http://localhost:8000/api/

## API文档

### 认证

所有API都需要JWT Token认证（除了登录和公开接口）。

**获取Token**:
```bash
POST /api/auth/login/
{
  "username": "your_username",
  "password": "your_password"
}
```

**使用Token**:
```bash
Authorization: Bearer <your_jwt_token>
```

### 主要API端点

#### 认证相关
- `POST /api/auth/login/` - 用户登录
- `POST /api/auth/logout/` - 用户登出
- `POST /api/auth/token/refresh/` - 刷新Token
- `GET /api/auth/profile/` - 用户资料
- `POST /api/auth/change-password/` - 修改密码
- `POST /api/auth/mfa/setup/` - 设置MFA

#### 密码管理
- `GET /api/passwords/entries/` - 密码列表
- `POST /api/passwords/entries/` - 创建密码
- `GET /api/passwords/entries/{id}/` - 密码详情
- `POST /api/passwords/generator/` - 生成密码
- `GET /api/passwords/security-analysis/` - 安全分析

#### 密码分享
- `GET /api/sharing/share-links/` - 分享链接列表
- `POST /api/sharing/share-links/` - 创建分享链接
- `GET /api/sharing/share/{token}/` - 访问分享链接

#### 审计日志
- `GET /api/audit/operation-logs/` - 操作日志
- `GET /api/audit/access-logs/` - 访问日志
- `GET /api/audit/security-events/` - 安全事件
- `GET /api/audit/stats/` - 审计统计

#### 系统管理
- `GET /api/system/settings/` - 系统设置
- `GET /api/system/status/` - 系统状态
- `POST /api/system/maintenance/` - 系统维护

## 测试

### API测试

运行API测试脚本：
```bash
python test_api.py
```

### 单元测试

```bash
python manage.py test
```

## 部署

### 生产环境配置

1. **数据库**: 使用PostgreSQL
2. **Web服务器**: Nginx + Gunicorn
3. **缓存**: Redis
4. **任务队列**: Celery + Redis
5. **监控**: 日志文件 + 系统监控

### Docker部署

```bash
# 构建镜像
docker build -t password-manager-backend .

# 运行容器
docker run -p 8000:8000 password-manager-backend
```

## 安全特性

- **密码加密**: 使用Fernet对称加密存储密码
- **传输安全**: HTTPS + JWT Token
- **访问控制**: 基于角色的权限管理
- **审计日志**: 完整的操作和访问记录
- **安全事件**: 自动检测和报告安全威胁
- **MFA支持**: TOTP多因素认证

## 项目结构

```
backend/
├── apps/                   # 应用模块
│   ├── users/             # 用户管理
│   ├── passwords/         # 密码管理
│   ├── sharing/           # 密码分享
│   ├── audit/             # 审计日志
│   └── system/            # 系统管理
├── config/                # 项目配置
├── utils/                 # 工具模块
├── templates/             # 模板文件
├── requirements.txt       # 依赖列表
├── manage.py             # Django管理脚本
├── start_server.py       # 启动脚本
└── test_api.py           # API测试脚本
```

## 开发指南

### 添加新功能

1. 在相应的app中创建模型
2. 创建序列化器
3. 创建视图
4. 配置URL路由
5. 添加测试
6. 更新文档

### 代码规范

- 遵循PEP 8代码风格
- 使用类型提示
- 编写文档字符串
- 添加单元测试

## 常见问题

### Q: 如何重置管理员密码？
A: 运行 `python manage.py changepassword admin`

### Q: 如何备份数据？
A: 使用 `python manage.py dumpdata > backup.json`

### Q: 如何查看日志？
A: 日志文件位于 `logs/` 目录下

### Q: 如何配置邮件服务？
A: 在 `.env` 文件中配置SMTP设置

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 联系方式

如有问题，请联系开发团队。