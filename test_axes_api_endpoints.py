#!/usr/bin/env python
"""
测试新创建的Axes API端点
"""
import os
import sys
import django
import requests
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def test_axes_api_endpoints():
    """测试Axes API端点"""
    print("🧪 测试新创建的Axes API端点...")
    
    base_url = 'http://localhost:8001'
    
    # 测试端点列表
    endpoints = [
        {
            'url': '/api/audit/axes/access-attempts/',
            'name': 'AccessAttempt (访问尝试)',
            'description': '所有登录尝试记录'
        },
        {
            'url': '/api/audit/axes/access-failures/',
            'name': 'AccessFailureLog (访问失败)',
            'description': '失败登录尝试记录'
        },
        {
            'url': '/api/audit/axes/access-logs/',
            'name': 'AccessLog (访问日志)',
            'description': '成功登录和登出记录'
        }
    ]
    
    success_count = 0
    
    print("=" * 60)
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint['url']}", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {endpoint['name']}")
                print(f"   URL: {endpoint['url']}")
                print(f"   描述: {endpoint['description']}")
                print(f"   状态码: {response.status_code}")
                print(f"   记录数: {data.get('count', 0)}")
                success_count += 1
                
            elif response.status_code in [401, 403]:
                print(f"🔐 {endpoint['name']}")
                print(f"   URL: {endpoint['url']}")
                print(f"   状态码: {response.status_code} (需要认证，端点存在)")
                success_count += 1
                
            else:
                print(f"❌ {endpoint['name']}")
                print(f"   URL: {endpoint['url']}")
                print(f"   状态码: {response.status_code}")
                print(f"   错误: {response.text[:100]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint['name']}")
            print(f"   URL: {endpoint['url']}")
            print(f"   连接错误: {e}")
        
        print("-" * 40)
    
    print("=" * 60)
    print(f"📊 测试结果: {success_count}/{len(endpoints)} 个端点正常")
    
    if success_count == len(endpoints):
        print("🎉 所有Axes API端点创建成功！")
        return True
    else:
        print("⚠️ 部分端点存在问题，请检查配置")
        return False


def test_url_patterns():
    """测试URL模式配置"""
    print("🧪 测试URL模式配置...")
    
    try:
        from django.urls import reverse
        
        urls_to_test = [
            ('axes_access_attempts', 'AccessAttempt API'),
            ('axes_access_failures', 'AccessFailureLog API'),
            ('axes_access_logs', 'AccessLog API'),
        ]
        
        success_count = 0
        
        for url_name, description in urls_to_test:
            try:
                url = reverse(url_name)
                print(f"✅ {description}: {url}")
                success_count += 1
            except Exception as e:
                print(f"❌ {description}: {e}")
        
        print(f"📊 URL配置测试: {success_count}/{len(urls_to_test)} 个URL正常")
        return success_count == len(urls_to_test)
        
    except Exception as e:
        print(f"❌ URL配置测试失败: {e}")
        return False


def test_model_imports():
    """测试模型导入"""
    print("🧪 测试模型和序列化器导入...")
    
    try:
        # 测试Axes模型导入
        from axes.models import AccessAttempt, AccessFailureLog, AccessLog
        print("✅ Axes模型导入成功")
        
        # 测试序列化器导入
        from apps.audit.serializers import (
            AxesAccessAttemptSerializer,
            AxesAccessFailureLogSerializer, 
            AxesAccessLogSerializer
        )
        print("✅ 新序列化器导入成功")
        
        # 测试视图导入
        from apps.audit.views import (
            LoginAttemptListView,
            AccessFailureLogListView,
            AccessLogListView
        )
        print("✅ 新视图导入成功")
        
        # 检查数据库表
        print(f"📊 数据库记录统计:")
        print(f"   AccessAttempt: {AccessAttempt.objects.count()} 条记录")
        print(f"   AccessFailureLog: {AccessFailureLog.objects.count()} 条记录")
        print(f"   AccessLog: {AccessLog.objects.count()} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试新创建的Axes API端点")
    print("=" * 60)
    
    tests = [
        test_model_imports,
        test_url_patterns,
        test_axes_api_endpoints,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 40)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            print("-" * 40)
    
    print("=" * 60)
    print(f"📊 总体测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Axes API端点创建成功！")
        print("\n📋 新增的API端点:")
        print("   • GET /api/audit/axes/access-attempts/  - 访问尝试记录")
        print("   • GET /api/audit/axes/access-failures/  - 访问失败记录")
        print("   • GET /api/audit/axes/access-logs/      - 访问成功记录")
        print("\n🔧 功能特性:")
        print("   • 支持分页查询")
        print("   • 支持字段过滤")
        print("   • 支持关键词搜索")
        print("   • 支持多字段排序")
        print("   • 需要管理员权限")
        return True
    else:
        print("⚠️ 部分测试失败，请检查实现")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
