# 测试目录结构规范

## 概述

本文档详细说明了Django密码管理系统的测试目录结构规范，确保测试代码的组织性、可维护性和可扩展性。

## 📁 完整目录结构

```
backend/                        # 项目根目录
├── tests/                      # 测试根目录
│   ├── __init__.py
│   ├── conftest.py             # pytest全局配置文件
│   ├── unit/                   # 单元测试目录
│   │   ├── __init__.py
│   │   ├── test_user_crud_automation.py    # 用户CRUD自动化测试
│   │   ├── test_user_crud_api.py           # 用户CRUD API测试
│   │   ├── test_password_models.py         # 密码模型测试
│   │   ├── test_password_views.py          # 密码视图测试
│   │   ├── test_encryption_utils.py        # 加密工具测试
│   │   ├── test_authentication.py          # 认证功能测试
│   │   └── test_*.py                       # 其他单元测试
│   ├── integration/            # 集成测试目录
│   │   ├── __init__.py
│   │   ├── test_user_crud_compatibility.py # 用户CRUD兼容性测试
│   │   ├── test_password_workflow.py       # 密码管理工作流测试
│   │   ├── test_sharing_workflow.py        # 分享功能工作流测试
│   │   ├── test_api_integration.py         # API集成测试
│   │   └── test_*.py                       # 其他集成测试
│   ├── scripts/                # 测试脚本目录
│   │   ├── __init__.py
│   │   ├── run_user_api_tests.py           # 完整测试运行器
│   │   ├── quick_test.py                   # 快速API测试工具
│   │   ├── check_test_environment.py       # 环境检查工具
│   │   ├── performance_test.py             # 性能测试脚本
│   │   └── load_test.py                    # 负载测试脚本
│   └── fixtures/               # 测试数据目录
│       ├── users.json          # 用户测试数据
│       ├── passwords.json      # 密码测试数据
│       ├── departments.json    # 部门测试数据
│       └── test_data.json      # 通用测试数据
├── reports/                    # 测试报告目录
│   ├── test_reports/           # 测试结果报告
│   │   ├── user_api_test_report_*.json
│   │   ├── quick_test_report_*.json
│   │   └── performance_report_*.json
│   └── coverage_reports/       # 覆盖率报告
│       ├── htmlcov/           # HTML覆盖率报告
│       │   └── index.html
│       ├── coverage.xml       # XML覆盖率报告
│       └── .coverage          # 覆盖率数据文件
├── pytest.ini                 # pytest配置文件
├── .coveragerc                 # 覆盖率配置文件
├── run_tests.bat              # Windows测试启动脚本
├── run_tests.sh               # Linux/Mac测试启动脚本
└── ...                        # 其他项目文件
```

## 🗂️ 目录说明

### tests/ - 测试根目录

所有测试相关文件的根目录，包含测试代码、脚本、数据和配置。

**核心文件**:
- `__init__.py`: Python包标识文件
- `conftest.py`: pytest全局配置，包含共享的fixtures和设置

### tests/unit/ - 单元测试目录

包含所有单元测试文件，测试单个函数、方法或类的功能。

**命名规范**:
- 文件名: `test_<模块名>_<功能>.py`
- 类名: `<功能>TestCase` 或 `Test<功能>`
- 方法名: `test_<具体功能>`

**示例文件**:
```python
# test_user_crud_automation.py
class UserCRUDTestCase(TestCase):
    def test_create_user_with_valid_data(self):
        """测试使用有效数据创建用户"""
        pass
    
    def test_create_user_with_invalid_email(self):
        """测试使用无效邮箱创建用户"""
        pass
```

### tests/integration/ - 集成测试目录

包含集成测试文件，测试多个组件之间的交互和工作流。

**测试范围**:
- API端到端测试
- 数据库集成测试
- 第三方服务集成测试
- 完整业务流程测试

**示例文件**:
```python
# test_user_crud_compatibility.py
class UserCRUDCompatibilityTest(TestCase):
    def test_jwt_authentication_integration(self):
        """测试JWT认证集成"""
        pass
    
    def test_audit_log_integration(self):
        """测试审计日志集成"""
        pass
```

### tests/scripts/ - 测试脚本目录

包含测试运行器、工具脚本和自动化测试脚本。

**脚本类型**:
- **测试运行器**: 批量运行测试的脚本
- **环境检查**: 验证测试环境的脚本
- **性能测试**: 专门的性能测试脚本
- **工具脚本**: 测试辅助工具

**主要脚本**:
```python
# run_user_api_tests.py - 完整测试运行器
python tests/scripts/run_user_api_tests.py

# quick_test.py - 快速API测试
python tests/scripts/quick_test.py

# check_test_environment.py - 环境检查
python tests/scripts/check_test_environment.py
```

### tests/fixtures/ - 测试数据目录

包含测试用的固定数据文件，通常为JSON格式。

**数据类型**:
- **用户数据**: 测试用户账户信息
- **业务数据**: 密码、部门、团队等业务对象
- **配置数据**: 测试配置和设置

**使用方式**:
```python
class UserTestCase(TestCase):
    fixtures = ['users.json', 'departments.json']
    
    def setUp(self):
        self.user = User.objects.get(username='testuser')
```

### reports/ - 测试报告目录

存储所有测试执行结果和报告。

**报告类型**:
- **测试结果报告**: JSON格式的详细测试结果
- **覆盖率报告**: HTML和XML格式的代码覆盖率报告
- **性能报告**: 性能测试结果和基准数据

## 📋 文件命名规范

### 测试文件命名

```python
# 单元测试文件
test_<模块名>_<功能>.py
test_user_models.py              # 用户模型测试
test_password_encryption.py     # 密码加密测试
test_authentication_utils.py    # 认证工具测试

# 集成测试文件
test_<功能>_<类型>.py
test_user_api_integration.py    # 用户API集成测试
test_password_workflow.py       # 密码工作流测试
test_sharing_compatibility.py   # 分享兼容性测试

# 测试脚本文件
<功能>_test.py 或 <功能>_<类型>.py
run_user_api_tests.py          # 用户API测试运行器
quick_test.py                  # 快速测试工具
performance_test.py            # 性能测试脚本
```

### 测试类和方法命名

```python
# 测试类命名
class <功能>TestCase(TestCase):
class UserCRUDTestCase(TestCase):        # 用户CRUD测试
class PasswordEncryptionTest(TestCase):  # 密码加密测试
class APIIntegrationTest(TestCase):      # API集成测试

# 测试方法命名
def test_<具体功能>(self):
def test_create_user_successfully(self):           # 成功创建用户
def test_create_user_with_duplicate_email(self):   # 重复邮箱创建用户
def test_unauthorized_access_returns_401(self):    # 未授权访问返回401
```

## ⚙️ 配置文件说明

### pytest.ini - pytest配置

```ini
[tool:pytest]
DJANGO_SETTINGS_MODULE = config.settings
python_files = test_*.py *_test.py *_tests.py
python_classes = Test* *Test *TestCase
python_functions = test_*
testpaths = tests
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --reuse-db
    --nomigrations
markers =
    unit: 单元测试
    integration: 集成测试
    api: API测试
    slow: 慢速测试
    security: 安全测试
    performance: 性能测试
```

### conftest.py - 测试配置

```python
import os
import sys
import django
from django.conf import settings

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# 全局测试配置
TEST_CONFIG = {
    'API_BASE_URL': 'http://localhost:8001',
    'ADMIN_USERNAME': 'admin',
    'ADMIN_PASSWORD': 'admin123',
    'TEST_TIMEOUT': 30,
}

# 共享fixtures
@pytest.fixture
def api_client():
    from rest_framework.test import APIClient
    return APIClient()

@pytest.fixture
def admin_user():
    from apps.users.models import User
    return User.objects.create_user(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        is_staff=True
    )
```

## 🚀 使用指南

### 运行测试的多种方式

```bash
# 1. 使用便捷脚本
./run_tests.sh                    # Linux/Mac
run_tests.bat                     # Windows

# 2. 使用测试脚本
python tests/scripts/quick_test.py
python tests/scripts/run_user_api_tests.py

# 3. 使用Django命令
python manage.py test tests
python manage.py test tests.unit
python manage.py test tests.integration

# 4. 使用pytest
pytest                            # 运行所有测试
pytest tests/unit/               # 运行单元测试
pytest tests/integration/        # 运行集成测试
pytest -m unit                   # 运行标记为unit的测试
pytest -v                        # 详细输出
```

### 添加新测试

1. **确定测试类型**: 单元测试还是集成测试
2. **选择合适目录**: `tests/unit/` 或 `tests/integration/`
3. **遵循命名规范**: 文件名、类名、方法名
4. **添加适当标记**: 使用pytest标记分类测试
5. **更新文档**: 在相关文档中说明新测试

### 测试数据管理

```python
# 使用fixtures
class UserTestCase(TestCase):
    fixtures = ['users.json']

# 使用Factory
from factory import django, Faker

class UserFactory(django.DjangoModelFactory):
    class Meta:
        model = User
    
    username = Faker('user_name')
    email = Faker('email')

# 在测试中使用
def test_user_creation(self):
    user = UserFactory()
    self.assertTrue(user.is_active)
```

## 📊 测试报告

### 报告格式

测试完成后会生成以下报告：

1. **JSON测试报告**: 详细的测试结果数据
2. **HTML覆盖率报告**: 可视化的代码覆盖率
3. **XML覆盖率报告**: 用于CI/CD集成
4. **控制台输出**: 实时测试状态

### 报告位置

```
reports/
├── test_reports/
│   ├── user_api_test_report_20240804_153000.json
│   ├── quick_test_report_20240804_153000.json
│   └── performance_report_20240804_153000.json
└── coverage_reports/
    ├── htmlcov/index.html        # 打开查看覆盖率
    ├── coverage.xml              # CI/CD使用
    └── .coverage                 # 原始数据
```

## 🔧 最佳实践

### 1. 测试组织
- 按功能模块组织测试文件
- 单元测试和集成测试分离
- 使用清晰的命名规范

### 2. 测试数据
- 使用fixtures管理测试数据
- 避免硬编码测试数据
- 保持测试数据的独立性

### 3. 测试运行
- 提供多种运行方式
- 支持分类和标记运行
- 生成详细的测试报告

### 4. 持续集成
- 集成到CI/CD流水线
- 自动运行测试和生成报告
- 监控测试覆盖率和质量

这个规范化的测试目录结构确保了测试代码的组织性、可维护性和可扩展性，为项目的长期发展提供了坚实的测试基础。
