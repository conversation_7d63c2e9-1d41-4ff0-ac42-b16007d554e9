#!/usr/bin/env python
"""
简单测试修复后的功能
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

import requests
import json

def test_group_member():
    """测试密码组添加成员功能"""
    base_url = "http://localhost:8001/api"
    
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    print("=== 简单测试密码组添加成员功能 ===")
    
    try:
        login_response = requests.post(f"{base_url}/auth/login/", json=login_data)
        if login_response.status_code != 200:
            print(f"登录失败: {login_response.text}")
            return
        
        token = login_response.json().get("access_token")
        headers = {"Authorization": f"Bearer {token}"}
        
        print("✅ 登录成功")
        
        # 1. 创建测试组
        print("\n1. 创建测试组")
        test_group_data = {
            "name": "简单测试组",
            "description": "用于简单测试的组"
        }
        
        create_response = requests.post(f"{base_url}/passwords/groups/", json=test_group_data, headers=headers)
        if create_response.status_code == 201:
            test_group = create_response.json()
            print(f"✅ 测试组创建成功，ID: {test_group['id']}")
            
            # 2. 获取当前用户信息
            profile_response = requests.get(f"{base_url}/auth/profile/", headers=headers)
            if profile_response.status_code == 200:
                user_info = profile_response.json()
                current_user_id = user_info['id']
                print(f"✅ 获取用户信息成功，用户ID: {current_user_id}")
                
                # 3. 测试添加用户到组
                print("\n3. 测试添加用户到组")
                add_user_data = {
                    "action": "add_user",
                    "user_id": current_user_id,  # 添加自己到组
                    "permission": "edit"
                }
                
                add_response = requests.post(
                    f"{base_url}/passwords/groups/{test_group['id']}/manage/", 
                    json=add_user_data, 
                    headers=headers
                )
                
                print(f"添加用户响应状态码: {add_response.status_code}")
                if add_response.status_code == 200:
                    print("✅ 添加用户到组成功")
                    print(f"响应内容: {add_response.text}")
                else:
                    print(f"❌ 添加用户到组失败: {add_response.text}")
                    
            else:
                print(f"❌ 获取用户信息失败: {profile_response.text}")
        else:
            print(f"❌ 测试组创建失败: {create_response.text}")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_group_member()
