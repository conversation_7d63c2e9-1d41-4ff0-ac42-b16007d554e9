# Generated by Django 5.2.4 on 2025-08-01 17:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("passwords", "0017_alter_passwordentry_url"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="passwordentry",
            name="database_name",
            field=models.CharField(
                blank=True, max_length=100, null=True, verbose_name="数据库名"
            ),
        ),
        migrations.AlterField(
            model_name="passwordentry",
            name="database_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("gaussdb", "GaussDB"),
                    ("mysql", "MySQL"),
                    ("postgresql", "PostgreSQL"),
                    ("oracle", "Oracle"),
                    ("other", "其他"),
                ],
                max_length=20,
                null=True,
                verbose_name="数据库类型",
            ),
        ),
        migrations.AlterField(
            model_name="passwordentry",
            name="ip_address",
            field=models.Char<PERSON><PERSON>(
                blank=True, max_length=255, null=True, verbose_name="IP地址"
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="passwordentry",
            name="mdw_type",
            field=models.Char<PERSON><PERSON>(
                blank=True,
                choices=[
                    ("bes", "BES"),
                    ("was", "WAS"),
                    ("redis", "Redis"),
                    ("other", "其他"),
                ],
                max_length=20,
                null=True,
                verbose_name="中间件类型",
            ),
        ),
        migrations.AlterField(
            model_name="passwordentry",
            name="os_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("kylin", "Kylin"),
                    ("suse", "SuSE"),
                    ("windows", "Windows"),
                    ("uos", "UOS"),
                    ("other", "其他"),
                ],
                max_length=20,
                null=True,
                verbose_name="操作系统类型",
            ),
        ),
    ]
