from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth.models import Group
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from .models import User, Department, Role
import pyotp
import logging


# ============ 部门序列化器 ============


class DepartmentListSerializer(serializers.ModelSerializer):
    """部门列表序列化器"""

    parent_name = serializers.CharField(source="parent.name", read_only=True)
    users_count = serializers.SerializerMethodField()

    class Meta:
        model = Department
        fields = [
            "id",
            "name",
            "description",
            "parent",
            "parent_name",
            "users_count",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_users_count(self, obj):
        """获取部门下的用户数量"""
        return obj.user_set.count()


class DepartmentDetailSerializer(serializers.ModelSerializer):
    """部门详情序列化器"""

    parent_name = serializers.CharField(source="parent.name", read_only=True)
    users = serializers.SerializerMethodField()
    children = serializers.SerializerMethodField()

    class Meta:
        model = Department
        fields = [
            "id",
            "name",
            "description",
            "parent",
            "parent_name",
            "users",
            "children",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_users(self, obj):
        """获取部门下的用户"""
        users = obj.user_set.filter(is_active=True)
        return [
            {"id": user.id, "username": user.username, "name": user.name}
            for user in users
        ]

    def get_children(self, obj):
        """获取子部门"""
        children = obj.department_set.all()
        return [{"id": child.id, "name": child.name} for child in children]


class DepartmentCreateUpdateSerializer(serializers.ModelSerializer):
    """部门创建/更新序列化器"""

    class Meta:
        model = Department
        fields = ["name", "description", "parent"]

    def validate_parent(self, value):
        """验证上级部门"""
        if value and self.instance:
            # 检查是否会形成循环引用
            if value == self.instance:
                raise serializers.ValidationError("部门不能设置自己为上级部门")

            # 检查是否设置子部门为上级部门
            def check_circular_reference(dept, target):
                if dept == target:
                    return True
                for child in dept.department_set.all():
                    if check_circular_reference(child, target):
                        return True
                return False

            if check_circular_reference(self.instance, value):
                raise serializers.ValidationError("不能设置子部门为上级部门")

        return value


# ============ 角色序列化器 ============


class RoleListSerializer(serializers.ModelSerializer):
    """角色列表序列化器"""

    name_display = serializers.CharField(source="get_name_display", read_only=True)
    users_count = serializers.SerializerMethodField()

    class Meta:
        model = Role
        fields = [
            "id",
            "name",
            "name_display",
            "description",
            "permissions",
            "users_count",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_users_count(self, obj):
        """获取角色下的用户数量"""
        return obj.user_set.count()


class RoleDetailSerializer(serializers.ModelSerializer):
    """角色详情序列化器"""

    name_display = serializers.CharField(source="get_name_display", read_only=True)
    users = serializers.SerializerMethodField()

    class Meta:
        model = Role
        fields = [
            "id",
            "name",
            "name_display",
            "description",
            "permissions",
            "users",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_users(self, obj):
        """获取角色下的用户"""
        users = obj.user_set.filter(is_active=True)
        return [
            {"id": user.id, "username": user.username, "name": user.name}
            for user in users
        ]


class RoleCreateUpdateSerializer(serializers.ModelSerializer):
    """角色创建/更新序列化器"""

    class Meta:
        model = Role
        fields = ["name", "description", "permissions"]

    def validate_permissions(self, value):
        """验证权限列表"""
        if not isinstance(value, list):
            raise serializers.ValidationError("权限必须是列表格式")
        return value


# ============ 保持原有的序列化器 ============


class DepartmentSerializer(serializers.ModelSerializer):
    """部门序列化器（保持向后兼容）"""

    class Meta:
        model = Department
        fields = ["id", "name", "description", "parent", "created_at", "updated_at"]
        read_only_fields = ["id", "created_at", "updated_at"]


class RoleSerializer(serializers.ModelSerializer):
    """角色序列化器"""

    class Meta:
        model = Role
        fields = [
            "id",
            "name",
            "description",
            "permissions",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class UserListSerializer(serializers.ModelSerializer):
    """用户列表序列化器"""

    department_name = serializers.CharField(source="department.name", read_only=True)
    role_name = serializers.CharField(source="role.get_name_display", read_only=True)
    groups_count = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "name",
            "department",
            "department_name",
            "role",
            "role_name",
            "groups_count",
            "is_active",
            "is_staff",
            "date_joined",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "date_joined", "created_at", "updated_at"]

    def get_groups_count(self, obj):
        """获取用户所属组数量"""
        return obj.groups.count()


class UserDetailSerializer(serializers.ModelSerializer):
    """用户详情序列化器"""

    department_name = serializers.CharField(source="department.name", read_only=True)
    role_name = serializers.CharField(source="role.get_name_display", read_only=True)
    groups = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "name",
            "phone",
            "avatar",
            "department",
            "department_name",
            "role",
            "role_name",
            "groups",
            "home_path",
            "is_mfa_enabled",
            "last_password_change",
            "is_active",
            "is_staff",
            "is_superuser",
            "date_joined",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "date_joined",
            "created_at",
            "updated_at",
            "last_password_change",
        ]

    def get_groups(self, obj):
        """获取用户所属组"""
        groups = obj.groups.all()
        return [{"id": group.id, "name": group.name} for group in groups]


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器（保持向后兼容）"""

    department_name = serializers.CharField(source="department.name", read_only=True)
    role_name = serializers.CharField(source="role.get_name_display", read_only=True)
    homePath = serializers.CharField(source="home_path", read_only=True)

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "name",
            "phone",
            "avatar",
            "department",
            "department_name",
            "role",
            "role_name",
            "homePath",
            "is_mfa_enabled",
            "last_password_change",
            "is_active",
            "is_staff",
            "date_joined",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "date_joined",
            "created_at",
            "updated_at",
            "last_password_change",
        ]


class UserCreateSerializer(serializers.ModelSerializer):
    """用户创建序列化器"""

    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    groups = serializers.PrimaryKeyRelatedField(
        queryset=Group.objects.all(),
        many=True,
        required=False,
        help_text="用户所属组ID列表",
    )

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "name",
            "password",
            "password_confirm",
            "phone",
            "department",
            "role",
            "groups",
        ]
        read_only_fields = ["id"]

    def validate(self, attrs):
        if attrs["password"] != attrs["password_confirm"]:
            raise serializers.ValidationError(_("两次输入的密码不一致"))
        return attrs

    def create(self, validated_data):
        groups = validated_data.pop("groups", [])
        validated_data.pop("password_confirm")
        password = validated_data.pop("password")

        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.last_password_change = timezone.now()

        # 设置首次登录标志
        user.is_first_login = True
        user.password_must_change = True
        user.save()

        # 设置用户组
        if groups:
            user.groups.set(groups)

        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """用户更新序列化器"""

    groups = serializers.PrimaryKeyRelatedField(
        queryset=Group.objects.all(),
        many=True,
        required=False,
        help_text="用户所属组ID列表",
    )

    class Meta:
        model = User
        fields = [
            "name",
            "phone",
            "avatar",
            "department",
            "role",
            "groups",
            "is_active",
            "is_staff",
        ]

    def update(self, instance, validated_data):
        groups = validated_data.pop("groups", None)

        # 更新基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # 更新用户组
        if groups is not None:
            instance.groups.set(groups)

        return instance


logger = logging.getLogger(__name__)


class LoginSerializer(serializers.Serializer):
    """登录序列化器"""

    username = serializers.CharField(required=True)
    password = serializers.CharField(write_only=True)
    mfa_code = serializers.CharField(max_length=6, required=False, allow_blank=True)

    def validate(self, attrs):
        username = attrs.get("username")
        password = attrs.get("password")
        mfa_code = attrs.get("mfa_code")

        logger.debug(f"开始验证用户登录: username={username}")

        if password:
            # 使用用户名和密码进行认证，必须传递request参数给AxesBackend
            user = authenticate(
                request=self.context.get("request"),
                username=username,
                password=password,
            )

            if not user:
                logger.warning(f"用户认证失败: username={username}")
                # 尝试增加失败登录次数
                try:
                    user_obj = User.objects.get(username=username)
                    user_obj.increment_failed_attempts()
                    logger.info(f"增加失败登录次数: username={username}")
                except User.DoesNotExist:
                    logger.warning(f"用户不存在: username={username}")
                    pass
                raise serializers.ValidationError(_("用户名或密码错误"))

            if not user.is_active:
                logger.warning(f"禁用用户尝试登录: username={username}")
                raise serializers.ValidationError(_("用户账户已被禁用"))

            if user.is_locked:
                logger.warning(f"锁定用户尝试登录: username={username}")
                raise serializers.ValidationError(_("账户已被锁定，请稍后再试"))

            # 检查MFA
            if user.is_mfa_enabled:
                logger.debug(f"开始MFA验证: username={username}")
                if not mfa_code:
                    logger.warning(f"未提供MFA验证码: username={username}")
                    raise serializers.ValidationError(_("请输入多因素认证码"))

                totp = pyotp.TOTP(user.mfa_secret)
                if not totp.verify(mfa_code):
                    logger.warning(f"MFA验证失败: username={username}")
                    user.increment_failed_attempts()
                    raise serializers.ValidationError(_("多因素认证码错误"))
                logger.debug(f"MFA验证成功: username={username}")

            # 登录成功，重置失败次数
            user.reset_failed_attempts()
            logger.info(f"用户登录成功: username={username}")
            attrs["user"] = user
            return attrs
        else:
            logger.warning("登录请求未提供密码")
            raise serializers.ValidationError(_("必须提供密码"))


class PasswordChangeSerializer(serializers.Serializer):
    """密码修改序列化器"""

    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(
        write_only=True, validators=[validate_password]
    )
    new_password_confirm = serializers.CharField(write_only=True)

    def validate_old_password(self, value):
        user = self.context["request"].user
        if not user.check_password(value):
            raise serializers.ValidationError(_("原密码错误"))
        return value

    def validate(self, attrs):
        if attrs["new_password"] != attrs["new_password_confirm"]:
            raise serializers.ValidationError(_("两次输入的新密码不一致"))
        return attrs

    def save(self):
        user = self.context["request"].user
        user.set_password(self.validated_data["new_password"])
        user.last_password_change = timezone.now()
        user.save()
        return user


class PasswordResetSerializer(serializers.Serializer):
    """密码重置序列化器"""

    email = serializers.EmailField()

    def validate_email(self, value):
        try:
            user = User.objects.get(email=value, is_active=True)
            self.user = user
        except User.DoesNotExist:
            raise serializers.ValidationError(_("该邮箱未注册或用户已被禁用"))
        return value


class PasswordResetConfirmSerializer(serializers.Serializer):
    """密码重置确认序列化器"""

    token = serializers.CharField()
    new_password = serializers.CharField(
        write_only=True, validators=[validate_password]
    )
    new_password_confirm = serializers.CharField(write_only=True)

    def validate(self, attrs):
        if attrs["new_password"] != attrs["new_password_confirm"]:
            raise serializers.ValidationError(_("两次输入的密码不一致"))
        return attrs


class FirstLoginPasswordChangeSerializer(serializers.Serializer):
    """首次登录密码修改序列化器"""

    temp_token = serializers.CharField(write_only=True)
    new_password = serializers.CharField(
        write_only=True, validators=[validate_password]
    )
    new_password_confirm = serializers.CharField(write_only=True)
    user_id = serializers.IntegerField(write_only=True)

    def validate(self, attrs):
        if attrs["new_password"] != attrs["new_password_confirm"]:
            raise serializers.ValidationError(_("两次输入的密码不一致"))

        # 验证临时令牌
        try:
            user = User.objects.get(id=attrs["user_id"])
            if (
                not user.temp_password_token
                or user.temp_password_token != attrs["temp_token"]
            ):
                raise serializers.ValidationError(_("无效的临时令牌"))

            if not user.requires_password_change:
                raise serializers.ValidationError(_("当前用户不需要修改密码"))

            self.user = user
        except User.DoesNotExist:
            raise serializers.ValidationError(_("用户不存在"))

        return attrs

    def save(self):
        """保存新密码并完成首次登录流程"""
        user = self.user
        user.set_password(self.validated_data["new_password"])
        user.last_password_change = timezone.now()

        # 设置密码过期时间（90天后）
        user.set_password_expiry(90)

        # 标记首次登录完成（这个方法内部会调用save()）
        user.mark_first_login_complete()

        return user


class MFASetupSerializer(serializers.Serializer):
    """MFA设置序列化器"""

    enable = serializers.BooleanField()
    mfa_code = serializers.CharField(max_length=6, required=False)

    def validate(self, attrs):
        user = self.context["request"].user
        enable = attrs.get("enable")
        mfa_code = attrs.get("mfa_code")

        if enable:
            if not mfa_code:
                raise serializers.ValidationError(_("启用MFA时必须提供验证码"))

            # 生成新的密钥或使用现有密钥
            if not user.mfa_secret:
                user.mfa_secret = pyotp.random_base32()

            totp = pyotp.TOTP(user.mfa_secret)
            if not totp.verify(mfa_code):
                raise serializers.ValidationError(_("验证码错误"))

        return attrs

    def save(self):
        user = self.context["request"].user
        enable = self.validated_data["enable"]

        if enable:
            if not user.mfa_secret:
                user.mfa_secret = pyotp.random_base32()
            user.is_mfa_enabled = True
        else:
            user.is_mfa_enabled = False
            user.mfa_secret = ""

        user.save()
        return user


class MFAQRCodeSerializer(serializers.Serializer):
    """MFA二维码序列化器"""

    def to_representation(self, instance):
        user = self.context["request"].user

        # 如果用户没有MFA密钥，生成一个临时的
        if not user.mfa_secret:
            secret = pyotp.random_base32()
        else:
            secret = user.mfa_secret

        totp = pyotp.TOTP(secret)
        provisioning_uri = totp.provisioning_uri(
            name=user.email, issuer_name="Password Locker"
        )

        return {"secret": secret, "qr_code_url": provisioning_uri}


# ============ 用户组序列化器 ============


class GroupListSerializer(serializers.ModelSerializer):
    """用户组列表序列化器"""

    users_count = serializers.SerializerMethodField()
    permissions_count = serializers.SerializerMethodField()

    class Meta:
        model = Group
        fields = ["id", "name", "users_count", "permissions_count"]

    def get_users_count(self, obj):
        """获取组内用户数量"""
        return obj.user_set.count()

    def get_permissions_count(self, obj):
        """获取组权限数量"""
        return obj.permissions.count()


class GroupDetailSerializer(serializers.ModelSerializer):
    """用户组详情序列化器"""

    users = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()

    class Meta:
        model = Group
        fields = ["id", "name", "users", "permissions"]

    def get_users(self, obj):
        """获取组内用户"""
        users = obj.user_set.filter(is_active=True)
        return [
            {
                "id": user.id,
                "username": user.username,
                "name": user.name,
                "email": user.email,
                "is_active": user.is_active,
            }
            for user in users
        ]

    def get_permissions(self, obj):
        """获取组权限"""
        permissions = obj.permissions.all()
        return [
            {
                "id": perm.id,
                "name": perm.name,
                "codename": perm.codename,
                "content_type": perm.content_type.name if perm.content_type else None,
            }
            for perm in permissions
        ]


class GroupCreateUpdateSerializer(serializers.ModelSerializer):
    """用户组创建/更新序列化器"""

    users = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.filter(is_active=True),
        many=True,
        required=False,
        help_text="组内用户ID列表",
    )
    permissions = serializers.PrimaryKeyRelatedField(
        queryset=Group.objects.none(),  # 将在__init__中设置
        many=True,
        required=False,
        help_text="权限ID列表",
    )

    class Meta:
        model = Group
        fields = ["name", "users", "permissions"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 动态设置permissions的queryset
        from django.contrib.auth.models import Permission

        self.fields["permissions"].queryset = Permission.objects.all()

    def create(self, validated_data):
        """创建用户组"""
        users = validated_data.pop("users", [])
        permissions = validated_data.pop("permissions", [])

        group = Group.objects.create(**validated_data)

        # 设置用户
        if users:
            group.user_set.set(users)

        # 设置权限
        if permissions:
            group.permissions.set(permissions)

        return group

    def update(self, instance, validated_data):
        """更新用户组"""
        users = validated_data.pop("users", None)
        permissions = validated_data.pop("permissions", None)

        # 更新基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # 更新用户
        if users is not None:
            instance.user_set.set(users)

        # 更新权限
        if permissions is not None:
            instance.permissions.set(permissions)

        return instance
