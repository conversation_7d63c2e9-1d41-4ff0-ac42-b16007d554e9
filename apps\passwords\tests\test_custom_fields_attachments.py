"""
自定义字段和附件相关视图测试
"""
from rest_framework import status
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from apps.passwords.models import CustomField, Attachment
from apps.audit.models import BusinessOperationLog
from .base import BasePasswordAPITestCase, BasePasswordPermissionTestCase
from .factories import CustomFieldFactory, AttachmentFactory


class CustomFieldListCreateViewTest(BasePasswordPermissionTestCase):
    """自定义字段列表和创建视图测试"""
    
    def test_list_custom_fields_own_password(self):
        """测试获取自己密码的自定义字段列表"""
        # 创建自定义字段
        field1 = CustomFieldFactory(password_entry=self.password_entry)
        field2 = CustomFieldFactory(password_entry=self.password_entry)
        
        self.authenticate_user()
        
        url = reverse("custom_field_list_create", kwargs={
            "password_entry_id": self.password_entry.id
        })
        
        response = self.client.get(url)
        data = self.assert_response_success(response)
        
        # 验证返回数据
        self.assertEqual(len(data), 2)
        field_ids = [item["id"] for item in data]
        self.assertIn(field1.id, field_ids)
        self.assertIn(field2.id, field_ids)
    
    def test_list_custom_fields_others_password_no_permission(self):
        """测试获取他人密码的自定义字段（无权限）"""
        CustomFieldFactory(password_entry=self.other_password_entry)
        
        self.authenticate_user()
        
        url = reverse("custom_field_list_create", kwargs={
            "password_entry_id": self.other_password_entry.id
        })
        
        response = self.client.get(url)
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)
    
    def test_list_custom_fields_others_password_with_permission(self):
        """测试获取他人密码的自定义字段（有权限）"""
        field = CustomFieldFactory(password_entry=self.other_password_entry)
        
        # 授予读权限
        self.grant_password_permission(
            self.user, 
            self.other_password_entry, 
            "read"
        )
        
        self.authenticate_user()
        
        url = reverse("custom_field_list_create", kwargs={
            "password_entry_id": self.other_password_entry.id
        })
        
        response = self.client.get(url)
        data = self.assert_response_success(response)
        
        # 验证返回数据
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]["id"], field.id)
    
    def test_create_custom_field_success(self):
        """测试成功创建自定义字段"""
        self.authenticate_user()
        self.clear_logs()
        
        url = reverse("custom_field_list_create", kwargs={
            "password_entry_id": self.password_entry.id
        })
        data = {
            "field_name": "API Key",
            "field_value": "sk-1234567890abcdef",
            "field_type": "text",
            "is_encrypted": True
        }
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response, status.HTTP_201_CREATED)
        
        # 验证自定义字段已创建
        custom_field = CustomField.objects.get(id=response_data["id"])
        self.assertEqual(custom_field.field_name, data["field_name"])
        self.assertEqual(custom_field.field_type, data["field_type"])
        self.assertEqual(custom_field.is_encrypted, data["is_encrypted"])
        self.assertEqual(custom_field.password_entry, self.password_entry)
        
        # 验证操作日志
        self.assert_operation_logged("custom_field_create", "custom_field")
    
    def test_create_custom_field_others_password_no_permission(self):
        """测试为他人密码创建自定义字段（无权限）"""
        self.authenticate_user()
        
        url = reverse("custom_field_list_create", kwargs={
            "password_entry_id": self.other_password_entry.id
        })
        data = {
            "field_name": "Hacked Field",
            "field_value": "hacked_value",
            "field_type": "text"
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)
    
    def test_create_custom_field_others_password_with_write_permission(self):
        """测试为他人密码创建自定义字段（有写权限）"""
        # 授予写权限
        self.grant_password_permission(
            self.user, 
            self.other_password_entry, 
            "write"
        )
        
        self.authenticate_user()
        self.clear_logs()
        
        url = reverse("custom_field_list_create", kwargs={
            "password_entry_id": self.other_password_entry.id
        })
        data = {
            "field_name": "Authorized Field",
            "field_value": "authorized_value",
            "field_type": "text"
        }
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response, status.HTTP_201_CREATED)
        
        # 验证自定义字段已创建
        custom_field = CustomField.objects.get(id=response_data["id"])
        self.assertEqual(custom_field.password_entry, self.other_password_entry)
        
        # 验证操作日志
        self.assert_operation_logged("custom_field_create", "custom_field")
    
    def test_create_custom_field_invalid_data(self):
        """测试创建自定义字段时数据无效"""
        self.authenticate_user()
        
        url = reverse("custom_field_list_create", kwargs={
            "password_entry_id": self.password_entry.id
        })
        data = {
            "field_name": "",  # 字段名不能为空
            "field_value": "some_value",
            "field_type": "invalid_type"  # 无效的字段类型
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_create_custom_field_missing_required_fields(self):
        """测试创建自定义字段时缺少必需字段"""
        self.authenticate_user()
        
        url = reverse("custom_field_list_create", kwargs={
            "password_entry_id": self.password_entry.id
        })
        data = {
            "field_value": "some_value"
            # 缺少field_name
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_create_custom_field_different_types(self):
        """测试创建不同类型的自定义字段"""
        self.authenticate_user()
        
        url = reverse("custom_field_list_create", kwargs={
            "password_entry_id": self.password_entry.id
        })
        
        # 测试不同字段类型
        field_types = ["text", "password", "url", "email", "number"]
        
        for field_type in field_types:
            data = {
                "field_name": f"Test {field_type} Field",
                "field_value": f"test_{field_type}_value",
                "field_type": field_type
            }
            
            response = self.client.post(url, data, format="json")
            response_data = self.assert_response_success(response, status.HTTP_201_CREATED)
            
            # 验证字段类型正确
            custom_field = CustomField.objects.get(id=response_data["id"])
            self.assertEqual(custom_field.field_type, field_type)


class AttachmentListCreateViewTest(BasePasswordPermissionTestCase):
    """附件列表和创建视图测试"""
    
    def test_list_attachments_own_password(self):
        """测试获取自己密码的附件列表"""
        # 创建附件
        attachment1 = AttachmentFactory(password_entry=self.password_entry)
        attachment2 = AttachmentFactory(password_entry=self.password_entry)
        
        self.authenticate_user()
        
        url = reverse("attachment_list_create", kwargs={
            "password_entry_id": self.password_entry.id
        })
        
        response = self.client.get(url)
        data = self.assert_response_success(response)
        
        # 验证返回数据
        self.assertEqual(len(data), 2)
        attachment_ids = [item["id"] for item in data]
        self.assertIn(attachment1.id, attachment_ids)
        self.assertIn(attachment2.id, attachment_ids)
    
    def test_list_attachments_others_password_no_permission(self):
        """测试获取他人密码的附件（无权限）"""
        AttachmentFactory(password_entry=self.other_password_entry)
        
        self.authenticate_user()
        
        url = reverse("attachment_list_create", kwargs={
            "password_entry_id": self.other_password_entry.id
        })
        
        response = self.client.get(url)
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)
    
    def test_create_attachment_success(self):
        """测试成功创建附件"""
        self.authenticate_user()
        self.clear_logs()
        
        # 创建测试文件
        test_file = SimpleUploadedFile(
            "test.txt",
            b"This is a test file content",
            content_type="text/plain"
        )
        
        url = reverse("attachment_list_create", kwargs={
            "password_entry_id": self.password_entry.id
        })
        data = {
            "file": test_file,
            "description": "Test attachment"
        }
        
        response = self.client.post(url, data, format="multipart")
        response_data = self.assert_response_success(response, status.HTTP_201_CREATED)
        
        # 验证附件已创建
        attachment = Attachment.objects.get(id=response_data["id"])
        self.assertEqual(attachment.description, data["description"])
        self.assertEqual(attachment.password_entry, self.password_entry)
        self.assertEqual(attachment.content_type, "text/plain")
        
        # 验证操作日志
        self.assert_operation_logged("attachment_create", "attachment")
    
    def test_create_attachment_others_password_no_permission(self):
        """测试为他人密码创建附件（无权限）"""
        self.authenticate_user()
        
        test_file = SimpleUploadedFile(
            "hack.txt",
            b"Hacked content",
            content_type="text/plain"
        )
        
        url = reverse("attachment_list_create", kwargs={
            "password_entry_id": self.other_password_entry.id
        })
        data = {
            "file": test_file,
            "description": "Hacked attachment"
        }
        
        response = self.client.post(url, data, format="multipart")
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)
    
    def test_create_attachment_no_file(self):
        """测试创建附件时未提供文件"""
        self.authenticate_user()
        
        url = reverse("attachment_list_create", kwargs={
            "password_entry_id": self.password_entry.id
        })
        data = {
            "description": "Attachment without file"
            # 缺少file字段
        }
        
        response = self.client.post(url, data, format="multipart")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_create_attachment_large_file(self):
        """测试创建大文件附件"""
        self.authenticate_user()
        
        # 创建大文件（假设有文件大小限制）
        large_content = b"x" * (10 * 1024 * 1024)  # 10MB
        large_file = SimpleUploadedFile(
            "large.txt",
            large_content,
            content_type="text/plain"
        )
        
        url = reverse("attachment_list_create", kwargs={
            "password_entry_id": self.password_entry.id
        })
        data = {
            "file": large_file,
            "description": "Large file attachment"
        }
        
        response = self.client.post(url, data, format="multipart")
        
        # 根据业务逻辑，可能会拒绝大文件
        # 这里假设有5MB的限制
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_create_attachment_different_file_types(self):
        """测试创建不同类型的附件"""
        self.authenticate_user()
        
        url = reverse("attachment_list_create", kwargs={
            "password_entry_id": self.password_entry.id
        })
        
        # 测试不同文件类型
        file_types = [
            ("test.txt", b"Text content", "text/plain"),
            ("test.json", b'{"key": "value"}', "application/json"),
            ("test.csv", b"col1,col2\nval1,val2", "text/csv"),
        ]
        
        for filename, content, content_type in file_types:
            test_file = SimpleUploadedFile(filename, content, content_type=content_type)
            
            data = {
                "file": test_file,
                "description": f"Test {filename} attachment"
            }
            
            response = self.client.post(url, data, format="multipart")
            response_data = self.assert_response_success(response, status.HTTP_201_CREATED)
            
            # 验证文件类型正确
            attachment = Attachment.objects.get(id=response_data["id"])
            self.assertEqual(attachment.content_type, content_type)
