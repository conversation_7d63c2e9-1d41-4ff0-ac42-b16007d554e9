from rest_framework_simplejwt.authentication import (
    JWTAuthentication as BaseJWTAuthentication,
)
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework import exceptions
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class JWTAuthentication(BaseJWTAuthentication):
    """
    自定义JWT认证类
    """

    def authenticate(self, request):
        header = self.get_header(request)
        if header is None:
            return None

        raw_token = self.get_raw_token(header)
        if raw_token is None:
            return None

        validated_token = self.get_validated_token(raw_token)
        user = self.get_user(validated_token)

        # 检查用户是否被锁定
        if hasattr(user, "locked_until") and user.locked_until:
            from django.utils import timezone

            if timezone.now() < user.locked_until:
                raise exceptions.AuthenticationFailed(_("账户已被锁定，请稍后再试"))

        return user, validated_token

    def get_user(self, validated_token):
        """
        获取用户实例
        """
        try:
            user_id = validated_token["user_id"]
        except KeyError:
            raise InvalidToken(_("Token中不包含用户ID"))

        try:
            user = User.objects.get(pk=user_id)
        except User.DoesNotExist:
            raise exceptions.AuthenticationFailed(_("用户不存在"))

        if not user.is_active:
            raise exceptions.AuthenticationFailed(_("用户已被禁用"))

        return user
