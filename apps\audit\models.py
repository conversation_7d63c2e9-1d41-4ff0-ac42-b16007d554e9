from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from auditlog.registry import auditlog
import uuid

User = get_user_model()


class BusinessOperationLog(models.Model):
    """业务操作日志模型 - 专注于业务级别的操作记录"""

    ACTION_TYPES = [
        # 用户操作
        ("user_login", _("用户登录")),
        ("user_logout", _("用户登出")),
        ("first_login", _("首次登录")),
        ("password_expired_login", _("密码过期登录")),
        ("first_login_password_change", _("首次登录密码修改")),
        ("user_create", _("创建用户")),
        ("user_update", _("更新用户")),
        ("user_delete", _("删除用户")),
        ("password_change", _("密码修改")),
        # 密码相关操作
        ("password_view", _("查看密码")),
        ("password_copy", _("复制密码")),
        ("password_export", _("导出密码")),
        ("password_import", _("导入密码")),
        # 分享操作
        ("password_share", _("分享密码")),
        ("share_revoke", _("撤销分享")),
        ("onetime_link_create", _("创建一次性链接")),
        ("onetime_link_access", _("访问一次性链接")),
        # 系统操作
        ("system_backup", _("系统备份")),
        ("system_restore", _("系统恢复")),
        ("system_setting_update", _("系统设置更新")),
        # 安全操作
        ("mfa_enable", _("启用多因素认证")),
        ("mfa_disable", _("禁用多因素认证")),
        ("bulk_operation", _("批量操作")),
        ("data_export", _("数据导出")),
    ]

    RESULT_TYPES = [
        ("success", _("成功")),
        ("failed", _("失败")),
        ("warning", _("警告")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("操作用户"),
    )
    action_type = models.CharField(
        max_length=30, choices=ACTION_TYPES, verbose_name=_("操作类型")
    )
    result = models.CharField(
        max_length=10,
        choices=RESULT_TYPES,
        default="success",
        verbose_name=_("操作结果"),
    )

    # 操作详情
    description = models.TextField(blank=True, verbose_name=_("操作描述"))
    target_type = models.CharField(
        max_length=50, blank=True, verbose_name=_("目标类型")
    )
    target_id = models.CharField(max_length=100, blank=True, verbose_name=_("目标ID"))
    target_name = models.CharField(
        max_length=200, blank=True, verbose_name=_("目标名称")
    )

    # 请求信息
    ip_address = models.GenericIPAddressField(verbose_name=_("IP地址"))
    user_agent = models.TextField(blank=True, verbose_name=_("用户代理"))
    request_method = models.CharField(
        max_length=10, blank=True, verbose_name=_("请求方法")
    )
    request_path = models.CharField(
        max_length=500, blank=True, verbose_name=_("请求路径")
    )

    # 额外数据
    extra_data = models.JSONField(default=dict, blank=True, verbose_name=_("额外数据"))

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("操作时间"))

    class Meta:
        verbose_name = _("业务操作日志")
        verbose_name_plural = _("业务操作日志")
        db_table = "business_operation_logs"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["action_type", "-created_at"]),
            models.Index(fields=["ip_address", "-created_at"]),
            models.Index(fields=["-created_at"]),
        ]

    def __str__(self):
        username = self.user.username if self.user else "Anonymous"
        return f"{username} - {self.get_action_type_display()} - {self.created_at}"


class PasswordAccessLog(models.Model):
    """密码访问日志模型"""

    ACCESS_TYPES = [
        ("view", _("查看")),
        ("copy_username", _("复制用户名")),
        ("copy_password", _("复制密码")),
        ("copy_url", _("复制网址")),
        ("edit", _("编辑")),
        ("delete", _("删除")),
        ("share", _("分享")),
        ("export", _("导出")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    password_entry = models.ForeignKey(
        "passwords.PasswordEntry",
        on_delete=models.CASCADE,
        related_name="access_logs",
        verbose_name=_("密码条目"),
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("访问用户"),
    )
    access_type = models.CharField(
        max_length=20, choices=ACCESS_TYPES, verbose_name=_("访问类型")
    )

    # 访问信息
    ip_address = models.GenericIPAddressField(verbose_name=_("IP地址"))
    user_agent = models.TextField(blank=True, verbose_name=_("用户代理"))

    # 访问来源
    access_source = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("访问来源"),
        help_text=_("direct, share, onetime_link"),
    )
    source_id = models.CharField(max_length=100, blank=True, verbose_name=_("来源ID"))

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("访问时间"))

    class Meta:
        verbose_name = _("密码访问日志")
        verbose_name_plural = _("密码访问日志")
        db_table = "password_access_logs"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["password_entry", "-created_at"]),
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["access_type", "-created_at"]),
            models.Index(fields=["-created_at"]),
        ]

    def __str__(self):
        username = self.user.username if self.user else "Anonymous"
        return f"{self.password_entry.title} - {username} - {self.get_access_type_display()}"


class SecurityEvent(models.Model):
    """安全事件模型"""

    EVENT_TYPES = [
        ("suspicious_login", _("可疑登录")),
        ("multiple_failed_logins", _("多次登录失败")),
        ("account_locked", _("账户锁定")),
        ("password_breach", _("密码泄露")),
        ("unusual_access", _("异常访问")),
        ("data_export", _("数据导出")),
        ("bulk_operation", _("批量操作")),
        ("privilege_escalation", _("权限提升")),
        ("unauthorized_access", _("未授权访问")),
    ]

    SEVERITY_LEVELS = [
        ("low", _("低")),
        ("medium", _("中")),
        ("high", _("高")),
        ("critical", _("严重")),
    ]

    STATUS_CHOICES = [
        ("open", _("开放")),
        ("investigating", _("调查中")),
        ("resolved", _("已解决")),
        ("false_positive", _("误报")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    event_type = models.CharField(
        max_length=30, choices=EVENT_TYPES, verbose_name=_("事件类型")
    )
    severity = models.CharField(
        max_length=10,
        choices=SEVERITY_LEVELS,
        default="medium",
        verbose_name=_("严重程度"),
    )
    status = models.CharField(
        max_length=15, choices=STATUS_CHOICES, default="open", verbose_name=_("状态")
    )

    # 事件详情
    title = models.CharField(max_length=200, verbose_name=_("事件标题"))
    description = models.TextField(verbose_name=_("事件描述"))

    # 相关用户和资源
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("相关用户"),
    )
    affected_resources = models.JSONField(
        default=list, blank=True, verbose_name=_("受影响资源")
    )

    # 事件数据
    event_data = models.JSONField(default=dict, blank=True, verbose_name=_("事件数据"))

    # 处理信息
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="assigned_security_events",
        verbose_name=_("分配给"),
    )
    resolution_notes = models.TextField(blank=True, verbose_name=_("解决备注"))
    resolved_at = models.DateTimeField(
        null=True, blank=True, verbose_name=_("解决时间")
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("安全事件")
        verbose_name_plural = _("安全事件")
        db_table = "security_events"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["event_type", "-created_at"]),
            models.Index(fields=["severity", "-created_at"]),
            models.Index(fields=["status", "-created_at"]),
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["-created_at"]),
        ]

    def __str__(self):
        return f"{self.title} - {self.get_severity_display()}"


# 别名已清理，不再需要兼容性别名
