#!/usr/bin/env python
"""
简单的admin登录测试
"""
import os
import sys
import django

# 设置Django环境
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.test import Client

def test_admin():
    client = Client()
    
    print("测试admin登录页面...")
    response = client.get('/admin/login/')
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ admin登录页面正常")
    else:
        print("❌ admin登录页面异常")
        print(f"响应内容: {response.content[:200]}")

if __name__ == '__main__':
    test_admin()
