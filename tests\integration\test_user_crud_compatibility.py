"""
CRUD API兼容性测试
测试CRUD API与现有JWT认证、权限系统的兼容性
"""
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth.models import Group
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from .models import User, Department, Team, Role
from apps.audit.models import OperationLog


class CRUDCompatibilityTest(TestCase):
    """CRUD API兼容性测试"""

    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True
        )
        
        # 创建普通用户
        self.normal_user = User.objects.create_user(
            username='user',
            email='<EMAIL>',
            password='user123'
        )
        
        # 创建测试数据
        self.department = Department.objects.create(
            name='技术部',
            description='技术开发部门'
        )
        
        self.team = Team.objects.create(
            name='后端团队',
            description='后端开发团队',
            department=self.department
        )
        
        self.role = Role.objects.create(
            name='developer',
            description='开发人员'
        )
        
        self.group = Group.objects.create(name='开发组')

    def get_jwt_token(self, user):
        """获取JWT Token"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)

    def test_jwt_authentication_works(self):
        """测试JWT认证是否正常工作"""
        # 获取token
        token = self.get_jwt_token(self.admin_user)
        
        # 设置认证头
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 测试API调用
        url = reverse('user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

    def test_permission_control_works(self):
        """测试权限控制是否正常工作"""
        # 普通用户token
        token = self.get_jwt_token(self.normal_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 尝试创建用户（应该被拒绝）
        url = reverse('user-list')
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'password123',
            'password_confirm': 'password123'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_operation_logging_works(self):
        """测试操作日志记录是否正常工作"""
        # 管理员token
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 记录操作前的日志数量
        initial_log_count = OperationLog.objects.count()
        
        # 创建用户
        url = reverse('user-list')
        data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'name': '测试用户',
            'password': 'TestPassword123!',
            'password_confirm': 'TestPassword123!',
            'department': self.department.id,
            'team': self.team.id,
            'role': self.role.id,
            'groups': [self.group.id]
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 检查是否记录了操作日志
        final_log_count = OperationLog.objects.count()
        self.assertEqual(final_log_count, initial_log_count + 1)
        
        # 检查日志内容
        latest_log = OperationLog.objects.latest('created_at')
        self.assertEqual(latest_log.user, self.admin_user)
        self.assertEqual(latest_log.action_type, 'user_create')
        self.assertEqual(latest_log.target_type, 'user')
        self.assertIsNotNone(latest_log.ip_address)

    def test_user_can_view_own_profile(self):
        """测试用户可以查看自己的信息"""
        # 普通用户token
        token = self.get_jwt_token(self.normal_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 查看自己的信息
        url = reverse('user-detail', kwargs={'pk': self.normal_user.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['username'], 'user')

    def test_user_cannot_view_others_profile(self):
        """测试普通用户不能查看其他用户信息"""
        # 普通用户token
        token = self.get_jwt_token(self.normal_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 尝试查看管理员信息
        url = reverse('user-detail', kwargs={'pk': self.admin_user.id})
        response = self.client.get(url)
        
        # 应该返回404（因为查询集被过滤了）
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_group_management_works(self):
        """测试用户组管理功能"""
        # 管理员token
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 创建用户组
        url = reverse('group-list')
        data = {
            'name': '测试组',
            'users': [self.normal_user.id],
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        group_id = response.data['data']['id']
        
        # 向组添加用户
        url = reverse('group-add-users', kwargs={'pk': group_id})
        data = {'user_ids': [self.admin_user.id]}
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['added_count'], 1)

    def test_department_hierarchy_works(self):
        """测试部门层级功能"""
        # 管理员token
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 创建子部门
        url = reverse('department-list')
        data = {
            'name': '技术一部',
            'description': '技术部下属部门',
            'parent': self.department.id
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 获取部门详情，检查层级关系
        url = reverse('department-detail', kwargs={'pk': self.department.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(len(response.data['data']['children']) > 0)

    def test_pagination_works(self):
        """测试分页功能"""
        # 创建多个用户
        for i in range(25):
            User.objects.create_user(
                username=f'user{i}',
                email=f'user{i}@example.com',
                password='password123'
            )
        
        # 管理员token
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 测试分页
        url = reverse('user-list')
        response = self.client.get(f'{url}?page=1&page_size=10')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 10)
        self.assertIn('total', response.data)
        self.assertIn('page', response.data)

    def test_search_and_filter_works(self):
        """测试搜索和过滤功能"""
        # 管理员token
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 测试搜索
        url = reverse('user-list')
        response = self.client.get(f'{url}?search=admin')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # 测试过滤
        response = self.client.get(f'{url}?is_staff=true')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

    def test_error_handling_works(self):
        """测试错误处理"""
        # 管理员token
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 尝试创建重复用户名的用户
        url = reverse('user-list')
        data = {
            'username': 'admin',  # 重复的用户名
            'email': '<EMAIL>',
            'password': 'password123',
            'password_confirm': 'password123'
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('errors', response.data)

    def test_soft_delete_works(self):
        """测试软删除功能"""
        # 管理员token
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # 删除用户
        url = reverse('user-detail', kwargs={'pk': self.normal_user.id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # 检查用户是否被软删除
        self.normal_user.refresh_from_db()
        self.assertFalse(self.normal_user.is_active)
