#!/usr/bin/env python3
"""
创建简单测试数据
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
django.setup()

from django.contrib.auth import get_user_model
from apps.passwords.models import PasswordEntry, PasswordEntryGroup, GroupPermission, PasswordEntryGroupMembership
from utils.encryption import encrypt_data

User = get_user_model()

def create_test_data():
    """创建测试数据"""
    print("=== 创建测试数据 ===")
    
    # 1. 创建测试用户
    print("\n1. 创建测试用户")
    user1, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    if created:
        user1.set_password('testpass123')
        user1.save()
        print(f"✅ 创建用户: {user1.username}")
    else:
        print(f"✅ 用户已存在: {user1.username}")
    
    # 2. 创建个人密码
    print("\n2. 创建个人密码")
    personal_passwords = [
        {
            'title': '个人MySQL数据库',
            'username': 'root',
            'password': 'personal123',
            'system_type': 'database',
            'database_type': 'mysql',
            'project_name': '个人项目',
            'environment': 'dev'
        },
        {
            'title': '个人Redis缓存',
            'username': 'admin',
            'password': 'redis456',
            'system_type': 'cache',
            'project_name': '个人项目',
            'environment': 'prod'
        }
    ]
    
    for pwd_data in personal_passwords:
        pwd, created = PasswordEntry.objects.get_or_create(
            title=pwd_data['title'],
            owner=user1,
            defaults={
                **pwd_data,
                'password': encrypt_data(pwd_data['password'])
            }
        )
        if created:
            print(f"✅ 创建个人密码: {pwd.title}")
        else:
            print(f"✅ 个人密码已存在: {pwd.title}")
    
    # 设置一个为收藏
    try:
        fav_pwd = PasswordEntry.objects.filter(owner=user1).first()
        if fav_pwd:
            fav_pwd.is_favorite = True
            fav_pwd.save()
            print(f"✅ 设置收藏密码: {fav_pwd.title}")
    except Exception as e:
        print(f"❌ 设置收藏失败: {e}")
    
    print("\n=== 测试数据创建完成 ===")
    print(f"用户: {user1.username} (密码: testpass123)")

if __name__ == "__main__":
    create_test_data()
