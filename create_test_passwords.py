#!/usr/bin/env python
import os
import sys
import django
import random
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from apps.passwords.models import PasswordEntry
from apps.users.models import User
from django.utils import timezone

def create_test_passwords():
    """创建100个测试密码"""
    print("=== 开始创建测试密码 ===")
    
    # 获取admin用户
    try:
        admin_user = User.objects.get(username='admin')
        print(f"找到admin用户: {admin_user.username} (ID: {admin_user.id})")
    except User.DoesNotExist:
        print("❌ Admin用户不存在")
        return
    
    # 定义测试数据模板
    system_types = ['os', 'database', 'middleware', 'ftp', 'sftp', 'website', 'network_device', 'other']
    os_types = ['windows', 'linux', 'unix', 'macos', 'other']
    database_types = ['mysql', 'postgresql', 'oracle', 'gaussdb', 'other']
    mdw_types = ['bes', 'was', 'redis', 'other']
    environments = ['development', 'testing', 'staging', 'production']
    
    # 项目名称列表
    projects = ['电商平台', '用户管理系统', '订单处理系统', '支付网关', '数据分析平台', 
               '监控系统', '日志系统', '缓存服务', '消息队列', '文件存储',
               'API网关', '认证服务', '配置中心', '服务注册', '负载均衡器',
               '数据库集群', 'Web服务器', '应用服务器', '反向代理', '防火墙']
    
    # 服务器名称前缀
    server_prefixes = ['web', 'app', 'db', 'cache', 'mq', 'api', 'auth', 'file', 'log', 'monitor']
    
    # 用户名列表
    usernames = ['admin', 'root', 'user', 'service', 'app', 'db', 'web', 'api', 'system', 'operator']
    
    created_count = 0
    
    for i in range(1, 101):
        try:
            # 随机选择系统类型
            system_type = random.choice(system_types)
            
            # 根据系统类型生成相应的子类型
            os_type = None
            database_type = None
            mdw_type = None
            
            if system_type == 'os':
                os_type = random.choice(os_types)
            elif system_type == 'database':
                database_type = random.choice(database_types)
            elif system_type == 'middleware':
                mdw_type = random.choice(mdw_types)
            
            # 生成服务器信息
            server_prefix = random.choice(server_prefixes)
            server_num = random.randint(1, 99)
            title = f"{server_prefix}-server-{server_num:02d}"
            
            # 生成IP地址
            ip = f"192.168.{random.randint(1, 255)}.{random.randint(1, 254)}"
            port = random.choice([22, 80, 443, 3306, 5432, 6379, 8080, 9000])
            
            # 生成用户名和密码
            username = random.choice(usernames)
            if random.random() < 0.3:  # 30%概率添加数字后缀
                username += str(random.randint(1, 9))
            
            # 生成密码
            password_chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
            password = ''.join(random.choice(password_chars) for _ in range(random.randint(8, 16)))
            
            # 随机选择项目和环境
            project_name = random.choice(projects)
            environment = random.choice(environments)
            
            # 随机设置收藏状态
            is_favorite = random.random() < 0.2  # 20%概率设为收藏
            
            # 生成备注
            notes = f"测试密码 #{i} - {system_type}类型服务器"
            if os_type:
                notes += f" - {os_type}系统"
            if database_type:
                notes += f" - {database_type}数据库"
            if mdw_type:
                notes += f" - {mdw_type}中间件"
            
            # 创建密码条目
            password_entry = PasswordEntry.objects.create(
                title=title,
                username=username,
                password=password,
                url=f"http://{ip}:{port}" if system_type in ['website', 'middleware'] else None,
                ip_address=ip,
                port=port,
                notes=notes,
                system_type=system_type,
                os_type=os_type,
                database_type=database_type,
                mdw_type=mdw_type,
                environment=environment,
                project_name=project_name,
                is_favorite=is_favorite,
                owner=admin_user,
                created_by=admin_user.username,
                created_at=timezone.now() - timedelta(days=random.randint(0, 30)),
                is_deleted=False
            )
            
            created_count += 1
            print(f"✅ 创建密码 {i}/100: {title} ({system_type})")
            
        except Exception as e:
            print(f"❌ 创建密码 {i} 失败: {e}")
    
    print(f"\n=== 创建完成 ===")
    print(f"成功创建 {created_count} 个测试密码")
    
    # 统计信息
    total_passwords = PasswordEntry.objects.filter(owner=admin_user, is_deleted=False).count()
    favorite_passwords = PasswordEntry.objects.filter(owner=admin_user, is_deleted=False, is_favorite=True).count()
    
    print(f"Admin用户总密码数: {total_passwords}")
    print(f"收藏密码数: {favorite_passwords}")
    
    # 按系统类型统计
    print(f"\n=== 按系统类型统计 ===")
    for sys_type in system_types:
        count = PasswordEntry.objects.filter(owner=admin_user, is_deleted=False, system_type=sys_type).count()
        print(f"{sys_type}: {count} 个")

if __name__ == "__main__":
    create_test_passwords()
