#!/usr/bin/env python
"""
检查数据库表结构
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def check_database_tables():
    """检查数据库表"""
    print("🔍 检查数据库表结构...")
    
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            print(f"\n📊 数据库中的表 ({len(tables)} 个):")
            for table in tables:
                table_name = table[0]
                print(f"  - {table_name}")
                
                # 检查audit相关的表
                if 'audit' in table_name or 'business' in table_name or 'password' in table_name or 'security' in table_name:
                    cursor.execute(f"PRAGMA table_info({table_name});")
                    columns = cursor.fetchall()
                    print(f"    字段 ({len(columns)} 个):")
                    for col in columns:
                        print(f"      - {col[1]} ({col[2]})")
                    print()
        
        return True
        
    except Exception as e:
        print(f"❌ 检查数据库表失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_audit_models():
    """检查audit模型"""
    print("\n🔍 检查audit模型...")
    
    try:
        from apps.audit.models import BusinessOperationLog, PasswordAccessLog, SecurityEvent
        from django.db import connection
        
        models = [
            ('BusinessOperationLog', BusinessOperationLog),
            ('PasswordAccessLog', PasswordAccessLog),
            ('SecurityEvent', SecurityEvent),
        ]
        
        for model_name, model_class in models:
            print(f"\n📋 {model_name}:")
            print(f"  - 表名: {model_class._meta.db_table}")
            
            # 检查表是否存在
            with connection.cursor() as cursor:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {model_class._meta.db_table};")
                    count = cursor.fetchone()[0]
                    print(f"  - ✅ 表存在，记录数: {count}")
                except Exception as e:
                    print(f"  - ❌ 表不存在: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查audit模型失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始检查数据库和模型")
    print("=" * 60)
    
    tests = [
        check_database_tables,
        check_audit_models,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            print("-" * 50)
    
    print("=" * 60)
    print(f"📊 检查结果: {passed}/{total} 个检查通过")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
