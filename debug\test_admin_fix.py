#!/usr/bin/env python
"""
测试admin登录修复
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def test_admin_login_after_fix():
    """测试修复后的admin登录"""
    print("🔍 测试修复后的admin登录")
    print("=" * 60)
    
    try:
        from django.test import Client
        
        client = Client()
        
        # 测试1: GET请求
        print("1. 测试GET /admin/login/")
        response = client.get('/admin/login/')
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ GET请求成功")
        else:
            print(f"   ❌ GET请求失败: {response.status_code}")
            return False
        
        # 测试2: POST请求（错误凭据）
        print("\n2. 测试POST /admin/login/ (错误凭据)")
        
        # 获取CSRF token
        import re
        content = response.content.decode()
        csrf_match = re.search(r'name=["\']csrfmiddlewaretoken["\'] value=["\']([^"\']+)["\']', content)
        csrf_token = csrf_match.group(1) if csrf_match else None
        
        post_data = {
            'username': 'wronguser',
            'password': 'wrongpass',
            'next': '/admin/',
        }
        
        if csrf_token:
            post_data['csrfmiddlewaretoken'] = csrf_token
            print(f"   CSRF token: {csrf_token[:10]}...")
        
        response = client.post('/admin/login/', post_data)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code in [200, 302]:
            print("   ✅ POST请求成功处理")
        else:
            print(f"   ❌ POST请求失败: {response.status_code}")
            return False
        
        # 测试3: 正确凭据登录
        print("\n3. 测试正确凭据登录")
        
        # 重新获取页面和CSRF token
        response = client.get('/admin/login/')
        content = response.content.decode()
        csrf_match = re.search(r'name=["\']csrfmiddlewaretoken["\'] value=["\']([^"\']+)["\']', content)
        csrf_token = csrf_match.group(1) if csrf_match else None
        
        post_data = {
            'username': 'admin',
            'password': 'admin123!',
            'next': '/admin/',
        }
        
        if csrf_token:
            post_data['csrfmiddlewaretoken'] = csrf_token
        
        response = client.post('/admin/login/', post_data)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 302:
            print("   ✅ 登录成功（重定向）")
            print(f"   重定向到: {response.get('Location', 'N/A')}")
        elif response.status_code == 200:
            if 'Please enter the correct username and password' in response.content.decode():
                print("   ⚠️ 登录失败（凭据错误）")
            else:
                print("   ✅ 登录页面正常显示")
        else:
            print(f"   ❌ 登录异常: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_middleware_order():
    """测试中间件顺序"""
    print("\n🔍 测试中间件顺序")
    print("=" * 60)
    
    try:
        from django.conf import settings
        
        middleware = settings.MIDDLEWARE
        print("当前中间件顺序:")
        for i, mw in enumerate(middleware, 1):
            print(f"   {i}. {mw.split('.')[-1]}")
        
        # 检查clickjacking是否在最后
        if middleware[-1] == "django.middleware.clickjacking.XFrameOptionsMiddleware":
            print("\n✅ clickjacking中间件已移到最后位置")
        else:
            print(f"\n⚠️ clickjacking中间件位置: {middleware.index('django.middleware.clickjacking.XFrameOptionsMiddleware') + 1}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试中间件顺序失败: {e}")
        return False

def test_admin_pages():
    """测试admin页面"""
    print("\n🔍 测试admin页面")
    print("=" * 60)
    
    try:
        from django.test import Client
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        client = Client()
        
        # 测试admin主页
        print("1. 测试admin主页")
        response = client.get('/admin/')
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 302:
            print("   ✅ 正确重定向到登录页面")
        elif response.status_code == 200:
            print("   ⚠️ 直接访问成功（可能已登录）")
        else:
            print(f"   ❌ 异常状态码: {response.status_code}")
        
        # 测试登录页面
        print("\n2. 测试登录页面")
        response = client.get('/admin/login/')
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ 登录页面正常")
            
            # 检查页面内容
            content = response.content.decode()
            if 'Django administration' in content:
                print("   ✅ 页面内容正确")
            else:
                print("   ⚠️ 页面内容异常")
        else:
            print(f"   ❌ 登录页面异常: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试admin页面失败: {e}")
        return False

def check_superuser():
    """检查超级用户"""
    print("\n🔍 检查超级用户")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        superusers = User.objects.filter(is_superuser=True, is_active=True)
        print(f"活跃超级用户数量: {superusers.count()}")
        
        for user in superusers:
            print(f"   👑 {user.username} - 员工: {user.is_staff}")
        
        if superusers.exists():
            print("\n✅ 有可用的超级用户")
            print("💡 可以使用以下凭据登录admin:")
            for user in superusers:
                print(f"   用户名: {user.username}")
                if user.username == 'admin':
                    print("   密码: admin123!")
                elif user.username == 'root':
                    print("   密码: root123!")
                else:
                    print("   密码: 请联系管理员")
        else:
            print("⚠️ 没有可用的超级用户")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查超级用户失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试admin登录修复")
    print("=" * 80)
    
    tests = [
        ("中间件顺序", test_middleware_order),
        ("超级用户", check_superuser),
        ("admin页面", test_admin_pages),
        ("admin登录", test_admin_login_after_fix),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            print("-" * 50)
    
    print("=" * 80)
    print("📋 修复测试总结")
    print("=" * 80)
    
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 admin登录问题已修复！")
        print("\n✅ 修复内容:")
        print("   1. 调整了中间件顺序（clickjacking移到最后）")
        print("   2. 修复了中间件响应处理")
        print("   3. admin页面正常工作")
        
        print("\n🎯 现在可以:")
        print("   1. 访问: http://127.0.0.1:8001/admin/")
        print("   2. 使用超级用户登录")
        print("   3. 正常使用admin功能")
        
        print("\n💡 建议:")
        print("   1. 重启Django开发服务器")
        print("   2. 清除浏览器缓存")
        print("   3. 使用无痕模式测试")
        
    else:
        print("⚠️ 仍有问题需要解决")
        print("💡 建议:")
        print("   1. 检查失败的测试项目")
        print("   2. 重启Django服务器")
        print("   3. 检查日志文件")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
