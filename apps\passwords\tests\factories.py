"""
测试数据工厂类
"""

import factory
from factory.django import DjangoModelFactory
from django.contrib.auth import get_user_model
from apps.passwords.models import (
    PasswordEntry,
    Category,
    CustomField,
    Attachment,
    PasswordHistory,
    PasswordEntryGroup,
    GroupPermission,
    PasswordEntryGroupMembership,
    PasswordPolicy,
)
from apps.users.models import Department, Role
from utils.encryption import encrypt_data

User = get_user_model()


class UserFactory(DjangoModelFactory):
    """用户工厂"""

    class Meta:
        model = User

    username = factory.Sequence(lambda n: f"testuser{n}")
    email = factory.LazyAttribute(lambda obj: f"{obj.username}@example.com")
    name = factory.Faker("name")
    is_active = True
    is_staff = False
    is_superuser = False


class AdminUserFactory(UserFactory):
    """管理员用户工厂"""

    is_staff = True
    is_superuser = True


class DepartmentFactory(DjangoModelFactory):
    """部门工厂"""

    class Meta:
        model = Department

    name = factory.Faker("company")
    description = factory.Faker("text", max_nb_chars=200)


class RoleFactory(DjangoModelFactory):
    """角色工厂"""

    class Meta:
        model = Role

    name = factory.Iterator(["admin", "manager", "user", "viewer"])
    description = factory.Faker("text", max_nb_chars=200)
    permissions = factory.LazyFunction(lambda: ["password:read", "password:write"])


class CategoryFactory(DjangoModelFactory):
    """分类工厂"""

    class Meta:
        model = Category

    name = factory.Faker("word")
    description = factory.Faker("text", max_nb_chars=200)
    color = factory.Faker("color")


class PasswordEntryFactory(DjangoModelFactory):
    """密码条目工厂"""

    class Meta:
        model = PasswordEntry

    title = factory.Faker("word")
    username = factory.Faker("user_name")
    password = factory.LazyFunction(lambda: encrypt_data("test_password_123"))
    url = factory.Faker("url")
    notes = factory.Faker("text", max_nb_chars=500)
    ip_address = factory.Faker("ipv4")
    port = factory.Faker("port_number")
    owner = factory.SubFactory(UserFactory)
    category = factory.SubFactory(CategoryFactory)
    system_type = factory.Iterator(
        [choice[0] for choice in PasswordEntry.SYSTEM_TYPE_CHOICES]
    )
    environment = "dev"
    is_deleted = False
    is_favorite = False


class CustomFieldFactory(DjangoModelFactory):
    """自定义字段工厂"""

    class Meta:
        model = CustomField

    password_entry = factory.SubFactory(PasswordEntryFactory)
    field_name = factory.Faker("word")
    field_value = factory.Faker("text", max_nb_chars=100)
    field_type = factory.Iterator(
        [choice[0] for choice in CustomField.FIELD_TYPE_CHOICES]
    )
    is_encrypted = False


class AttachmentFactory(DjangoModelFactory):
    """附件工厂"""

    class Meta:
        model = Attachment

    password_entry = factory.SubFactory(PasswordEntryFactory)
    file_name = factory.Faker("file_name")
    file_size = factory.Faker("random_int", min=1024, max=1048576)
    content_type = "text/plain"
    description = factory.Faker("text", max_nb_chars=200)


class PasswordHistoryFactory(DjangoModelFactory):
    """密码历史工厂"""

    class Meta:
        model = PasswordHistory

    password_entry = factory.SubFactory(PasswordEntryFactory)
    old_password = factory.LazyFunction(lambda: encrypt_data("old_password_123"))
    changed_by = factory.SubFactory(UserFactory)


class PasswordEntryGroupFactory(DjangoModelFactory):
    """密码组工厂"""

    class Meta:
        model = PasswordEntryGroup

    name = factory.Faker("word")
    description = factory.Faker("text", max_nb_chars=200)
    created_by = factory.SubFactory(UserFactory)
    is_active = True


class GroupPermissionFactory(DjangoModelFactory):
    """组权限工厂"""

    class Meta:
        model = GroupPermission

    group = factory.SubFactory(PasswordEntryGroupFactory)
    user = factory.SubFactory(UserFactory)
    permission_level = factory.Iterator(
        [choice[0] for choice in GroupPermission.PERMISSION_CHOICES]
    )


class PasswordEntryGroupMembershipFactory(DjangoModelFactory):
    """密码组成员关系工厂"""

    class Meta:
        model = PasswordEntryGroupMembership

    group = factory.SubFactory(PasswordEntryGroupFactory)
    password_entry = factory.SubFactory(PasswordEntryFactory)
    added_by = factory.SubFactory(UserFactory)


class PasswordPolicyFactory(DjangoModelFactory):
    """密码策略工厂"""

    class Meta:
        model = PasswordPolicy

    name = factory.Faker("word")
    description = factory.Faker("text", max_nb_chars=200)
    min_length = 8
    max_length = 128
    uppercase_count = 1
    lowercase_count = 1
    digit_count = 1
    special_char_count = 1
    allowed_special_chars = "!@#$%^&*"
    is_active = True
    created_by = factory.SubFactory(UserFactory)
