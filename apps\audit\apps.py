from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class AuditConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "apps.audit"
    verbose_name = _("审计日志")

    def ready(self):
        """应用准备就绪时的初始化操作"""
        # 注册auditlog模型
        try:
            from . import auditlog_registry
        except ImportError:
            pass

        # 导入信号处理器
        try:
            from . import signals
        except ImportError:
            pass
