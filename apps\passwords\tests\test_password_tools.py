"""
密码工具相关视图测试
"""
from rest_framework import status
from django.urls import reverse
from apps.passwords.models import PasswordEntry
from apps.audit.models import BusinessOperationLog
from .base import BasePasswordAPITestCase
from .factories import PasswordEntryFactory, PasswordPolicyFactory


class PasswordGeneratorViewTest(BasePasswordAPITestCase):
    """密码生成器视图测试"""
    
    def test_generate_password_success(self):
        """测试成功生成密码"""
        self.authenticate_user()
        self.clear_logs()
        
        url = reverse("password_generator")
        data = {
            "length": 12,
            "include_uppercase": True,
            "include_lowercase": True,
            "include_digits": True,
            "include_symbols": True,
            "exclude_ambiguous": False
        }
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证返回数据
        self.assertIn("password", response_data)
        self.assertIn("strength", response_data)
        self.assertEqual(len(response_data["password"]), data["length"])
        
        # 验证密码强度
        self.assertIn(response_data["strength"], ["weak", "medium", "strong"])
        
        # 验证操作日志
        self.assert_operation_logged("password_generate", "password_generator")
    
    def test_generate_password_custom_length(self):
        """测试生成自定义长度密码"""
        self.authenticate_user()
        
        url = reverse("password_generator")
        
        # 测试不同长度
        for length in [8, 16, 32, 64]:
            data = {
                "length": length,
                "include_uppercase": True,
                "include_lowercase": True,
                "include_digits": True,
                "include_symbols": False
            }
            
            response = self.client.post(url, data, format="json")
            response_data = self.assert_response_success(response)
            
            self.assertEqual(len(response_data["password"]), length)
    
    def test_generate_password_only_lowercase(self):
        """测试只生成小写字母密码"""
        self.authenticate_user()
        
        url = reverse("password_generator")
        data = {
            "length": 10,
            "include_uppercase": False,
            "include_lowercase": True,
            "include_digits": False,
            "include_symbols": False
        }
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证密码只包含小写字母
        password = response_data["password"]
        self.assertTrue(password.islower())
        self.assertTrue(password.isalpha())
    
    def test_generate_password_exclude_ambiguous(self):
        """测试生成密码时排除易混淆字符"""
        self.authenticate_user()
        
        url = reverse("password_generator")
        data = {
            "length": 20,
            "include_uppercase": True,
            "include_lowercase": True,
            "include_digits": True,
            "include_symbols": False,
            "exclude_ambiguous": True
        }
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证密码不包含易混淆字符
        password = response_data["password"]
        ambiguous_chars = "0O1lI"
        for char in ambiguous_chars:
            self.assertNotIn(char, password)
    
    def test_generate_password_invalid_length(self):
        """测试生成密码时长度无效"""
        self.authenticate_user()
        
        url = reverse("password_generator")
        
        # 测试长度过短
        data = {
            "length": 3,  # 最小长度为4
            "include_lowercase": True
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
        
        # 测试长度过长
        data = {
            "length": 129,  # 最大长度为128
            "include_lowercase": True
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_generate_password_no_character_types(self):
        """测试生成密码时未选择任何字符类型"""
        self.authenticate_user()
        
        url = reverse("password_generator")
        data = {
            "length": 12,
            "include_uppercase": False,
            "include_lowercase": False,
            "include_digits": False,
            "include_symbols": False
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_generate_password_unauthenticated(self):
        """测试未认证用户生成密码"""
        url = reverse("password_generator")
        data = {
            "length": 12,
            "include_lowercase": True
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_401_UNAUTHORIZED)


class PasswordSecurityAnalysisViewTest(BasePasswordAPITestCase):
    """密码安全分析视图测试"""
    
    def setUp(self):
        super().setUp()
        
        # 创建不同强度的密码条目用于测试
        self.weak_password = PasswordEntryFactory(
            owner=self.user,
            title="Weak Password"
        )
        
        self.medium_password = PasswordEntryFactory(
            owner=self.user,
            title="Medium Password"
        )
        
        self.strong_password = PasswordEntryFactory(
            owner=self.user,
            title="Strong Password"
        )
    
    def test_security_analysis_success(self):
        """测试成功获取安全分析"""
        self.authenticate_user()
        
        url = reverse("security_analysis")
        
        response = self.client.get(url)
        response_data = self.assert_response_success(response)
        
        # 验证返回数据结构
        expected_keys = [
            "total_passwords",
            "weak_passwords",
            "duplicate_passwords",
            "expired_passwords",
            "expiring_soon",
            "strength_distribution"
        ]
        
        for key in expected_keys:
            self.assertIn(key, response_data)
        
        # 验证总密码数
        total_passwords = PasswordEntry.objects.filter(
            owner=self.user,
            is_deleted=False
        ).count()
        self.assertEqual(response_data["total_passwords"], total_passwords)
        
        # 验证强度分布
        strength_dist = response_data["strength_distribution"]
        self.assertIn("weak", strength_dist)
        self.assertIn("medium", strength_dist)
        self.assertIn("strong", strength_dist)
    
    def test_security_analysis_empty_passwords(self):
        """测试没有密码时的安全分析"""
        # 删除所有密码
        PasswordEntry.objects.filter(owner=self.user).update(is_deleted=True)
        
        self.authenticate_user()
        
        url = reverse("security_analysis")
        
        response = self.client.get(url)
        response_data = self.assert_response_success(response)
        
        # 验证空数据
        self.assertEqual(response_data["total_passwords"], 0)
        self.assertEqual(response_data["weak_passwords"], 0)
        self.assertEqual(response_data["duplicate_passwords"], 0)
        self.assertEqual(response_data["expired_passwords"], 0)
        self.assertEqual(response_data["expiring_soon"], 0)
    
    def test_security_analysis_unauthenticated(self):
        """测试未认证用户获取安全分析"""
        url = reverse("security_analysis")
        
        response = self.client.get(url)
        self.assert_response_error(response, status.HTTP_401_UNAUTHORIZED)
    
    def test_security_analysis_only_own_passwords(self):
        """测试安全分析只包含自己的密码"""
        # 创建其他用户的密码
        other_passwords = PasswordEntryFactory.create_batch(5, owner=self.other_user)
        
        self.authenticate_user()
        
        url = reverse("security_analysis")
        
        response = self.client.get(url)
        response_data = self.assert_response_success(response)
        
        # 验证只统计自己的密码
        own_passwords_count = PasswordEntry.objects.filter(
            owner=self.user,
            is_deleted=False
        ).count()
        self.assertEqual(response_data["total_passwords"], own_passwords_count)
        
        # 验证不包含其他用户的密码
        total_passwords_in_db = PasswordEntry.objects.filter(is_deleted=False).count()
        self.assertLess(response_data["total_passwords"], total_passwords_in_db)


class PasswordPolicyViewTest(BasePasswordAPITestCase):
    """密码策略相关视图测试"""
    
    def test_generate_password_by_policy_success(self):
        """测试根据策略生成密码成功"""
        self.authenticate_user()
        
        url = reverse("password_generate_by_policy")
        data = {"policy_id": self.password_policy.id}
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证返回数据
        self.assertIn("password", response_data)
        self.assertIn("policy", response_data)
        
        # 验证密码长度符合策略
        password = response_data["password"]
        self.assertGreaterEqual(len(password), self.password_policy.min_length)
        
        # 验证策略信息
        policy_info = response_data["policy"]
        self.assertEqual(policy_info["name"], self.password_policy.name)
        self.assertEqual(policy_info["min_length"], self.password_policy.min_length)
    
    def test_generate_password_by_policy_not_found(self):
        """测试根据不存在的策略生成密码"""
        self.authenticate_user()
        
        url = reverse("password_generate_by_policy")
        data = {"policy_id": 99999}  # 不存在的策略ID
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)
    
    def test_validate_password_by_policy_success(self):
        """测试根据策略验证密码成功"""
        self.authenticate_user()
        
        url = reverse("password_validate_by_policy")
        data = {
            "policy_id": self.password_policy.id,
            "password": "StrongP@ssw0rd123",
            "username": "testuser",
            "email": "<EMAIL>"
        }
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证返回数据
        self.assertIn("is_valid", response_data)
        self.assertIn("errors", response_data)
        self.assertIn("policy", response_data)
        
        # 验证策略信息
        policy_info = response_data["policy"]
        self.assertEqual(policy_info["name"], self.password_policy.name)
    
    def test_validate_password_by_policy_weak_password(self):
        """测试根据策略验证弱密码"""
        self.authenticate_user()
        
        url = reverse("password_validate_by_policy")
        data = {
            "policy_id": self.password_policy.id,
            "password": "123",  # 弱密码
            "username": "testuser"
        }
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证密码被标记为无效
        self.assertFalse(response_data["is_valid"])
        self.assertGreater(len(response_data["errors"]), 0)
    
    def test_validate_password_by_policy_not_found(self):
        """测试根据不存在的策略验证密码"""
        self.authenticate_user()
        
        url = reverse("password_validate_by_policy")
        data = {
            "policy_id": 99999,  # 不存在的策略ID
            "password": "StrongP@ssw0rd123"
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)
