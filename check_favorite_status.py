#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from apps.passwords.models import PasswordEntry
from apps.users.models import User

def check_favorite_status():
    """检查密码的收藏状态"""
    print("=== 检查密码收藏状态 ===")
    
    # 获取admin用户
    try:
        admin_user = User.objects.get(username='admin')
        print(f"Admin用户: {admin_user.username} (ID: {admin_user.id})")
    except User.DoesNotExist:
        print("❌ Admin用户不存在")
        return
    
    # 检查密码aaa的详细信息
    try:
        aaa_password = PasswordEntry.objects.get(title='aaa')
        print(f"\n密码'aaa'的详细信息:")
        print(f"  - ID: {aaa_password.id}")
        print(f"  - 标题: {aaa_password.title}")
        print(f"  - 所有者: {aaa_password.owner.username} (ID: {aaa_password.owner.id})")
        print(f"  - 是否删除: {aaa_password.is_deleted}")
        print(f"  - 是否收藏: {aaa_password.is_favorite}")
        print(f"  - 创建时间: {aaa_password.created_at}")
        print(f"  - 更新时间: {aaa_password.updated_at}")
        
    except PasswordEntry.DoesNotExist:
        print("❌ 密码'aaa'不存在")
        return
    
    # 测试不同的筛选条件
    print(f"\n=== 测试筛选条件 ===")
    
    # 1. 基础个人密码
    personal_passwords = PasswordEntry.objects.filter(owner=admin_user, is_deleted=False)
    print(f"1. 基础个人密码: {personal_passwords.count()}")
    
    # 2. 个人密码 + 非收藏
    personal_non_favorite = PasswordEntry.objects.filter(
        owner=admin_user, is_deleted=False, is_favorite=False
    )
    print(f"2. 个人密码 + 非收藏: {personal_non_favorite.count()}")
    
    # 3. 个人密码 + 收藏
    personal_favorite = PasswordEntry.objects.filter(
        owner=admin_user, is_deleted=False, is_favorite=True
    )
    print(f"3. 个人密码 + 收藏: {personal_favorite.count()}")
    
    # 4. 检查所有admin的密码的收藏状态
    print(f"\n=== Admin所有密码的收藏状态 ===")
    all_admin_passwords = PasswordEntry.objects.filter(owner=admin_user)
    for pwd in all_admin_passwords:
        print(f"  - {pwd.title}: 收藏={pwd.is_favorite}, 删除={pwd.is_deleted}")

if __name__ == "__main__":
    check_favorite_status()
