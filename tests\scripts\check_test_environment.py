#!/usr/bin/env python3
"""
测试环境检查脚本
验证测试环境是否正确配置
"""
import os
import sys
import subprocess
import importlib
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(
            f"   ✅ Python {version.major}.{version.minor}.{version.micro} (满足要求)"
        )
        return True
    else:
        print(
            f"   ❌ Python {version.major}.{version.minor}.{version.micro} (需要3.8+)"
        )
        return False


def check_django_project():
    """检查Django项目结构"""
    print("🏗️  检查Django项目结构...")

    required_files = [
        "manage.py",
        "config/settings.py",
        "apps/users/models.py",
        "apps/users/views.py",
        "apps/users/crud_views.py",
    ]

    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)

    if not missing_files:
        print("   ✅ Django项目结构完整")
        return True
    else:
        print("   ❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"      - {file_path}")
        return False


def check_required_packages():
    """检查必需的Python包"""
    print("📦 检查必需的Python包...")

    required_packages = [
        "django",
        "djangorestframework",
        "djangorestframework_simplejwt",
        "django_filters",
        "requests",
    ]

    missing_packages = []
    for package in required_packages:
        try:
            importlib.import_module(package.replace("-", "_"))
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package} (未安装)")

    if not missing_packages:
        return True
    else:
        print("\n   安装缺少的包:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False


def check_test_files():
    """检查测试文件"""
    print("🧪 检查测试文件...")

    test_files = [
        "tests/unit/test_user_crud_automation.py",
        "tests/unit/test_user_crud_api.py",
        "tests/integration/test_user_crud_compatibility.py",
        "tests/scripts/run_user_api_tests.py",
        "tests/scripts/quick_test.py",
    ]

    missing_files = []
    for file_path in test_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"   ✅ {file_path}")

    if missing_files:
        print("   ❌ 缺少以下测试文件:")
        for file_path in missing_files:
            print(f"      - {file_path}")
        return False

    return True


def check_database_connection():
    """检查数据库连接"""
    print("🗄️  检查数据库连接...")

    try:
        # 设置Django环境
        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
        import django

        django.setup()

        from django.db import connection

        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()

        if result:
            print("   ✅ 数据库连接正常")
            return True
        else:
            print("   ❌ 数据库连接失败")
            return False

    except Exception as e:
        print(f"   ❌ 数据库连接错误: {e}")
        return False


def check_migrations():
    """检查数据库迁移"""
    print("🔄 检查数据库迁移...")

    try:
        result = subprocess.run(
            [sys.executable, "manage.py", "showmigrations", "--plan"],
            capture_output=True,
            text=True,
            timeout=30,
        )

        if result.returncode == 0:
            unapplied = [line for line in result.stdout.split("\n") if "[ ]" in line]
            if unapplied:
                print("   ⚠️  有未应用的迁移:")
                for migration in unapplied[:5]:  # 只显示前5个
                    print(f"      {migration.strip()}")
                print("   运行: python manage.py migrate")
                return False
            else:
                print("   ✅ 所有迁移已应用")
                return True
        else:
            print(f"   ❌ 检查迁移失败: {result.stderr}")
            return False

    except Exception as e:
        print(f"   ❌ 检查迁移错误: {e}")
        return False


def check_admin_user():
    """检查管理员用户"""
    print("👤 检查管理员用户...")

    try:
        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
        import django

        django.setup()

        from apps.users.models import User

        admin_users = User.objects.filter(is_staff=True, is_active=True)

        if admin_users.exists():
            admin_user = admin_users.first()
            print(f"   ✅ 找到管理员用户: {admin_user.username}")
            return True
        else:
            print("   ❌ 未找到活跃的管理员用户")
            print("   创建管理员用户: python manage.py createsuperuser")
            return False

    except Exception as e:
        print(f"   ❌ 检查管理员用户错误: {e}")
        return False


def check_server_status():
    """检查开发服务器状态"""
    print("🌐 检查开发服务器状态...")

    try:
        import requests

        response = requests.get("http://localhost:8001/admin/", timeout=5)
        if response.status_code in [200, 302, 404]:
            print("   ✅ 开发服务器正在运行")
            return True
        else:
            print(f"   ⚠️  服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("   ⚠️  开发服务器未运行")
        print("   启动服务器: python manage.py runserver 8001")
        return False
    except Exception as e:
        print(f"   ❌ 检查服务器错误: {e}")
        return False


def create_reports_directory():
    """创建报告目录"""
    print("📁 创建报告目录...")

    reports_dir = Path("reports")
    if not reports_dir.exists():
        reports_dir.mkdir()
        print("   ✅ 创建reports目录")
    else:
        print("   ✅ reports目录已存在")

    return True


def main():
    """主函数"""
    print("🔍 用户管理CRUD API测试环境检查")
    print("=" * 50)

    checks = [
        ("Python版本", check_python_version),
        ("Django项目", check_django_project),
        ("Python包", check_required_packages),
        ("测试文件", check_test_files),
        ("数据库连接", check_database_connection),
        ("数据库迁移", check_migrations),
        ("管理员用户", check_admin_user),
        ("开发服务器", check_server_status),
        ("报告目录", create_reports_directory),
    ]

    passed = 0
    failed = 0
    warnings = 0

    for check_name, check_func in checks:
        try:
            result = check_func()
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"   ❌ {check_name}检查异常: {e}")
            failed += 1

        print()

    # 总结
    print("=" * 50)
    print("📊 检查结果总结")
    print("=" * 50)
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 成功率: {passed/(passed+failed)*100:.1f}%")

    if failed == 0:
        print("\n🎉 测试环境配置完整，可以运行测试！")
        print("\n🚀 快速开始:")
        print("   python tests/scripts/quick_test.py")
        print("   python tests/scripts/run_user_api_tests.py")
        return True
    else:
        print(f"\n⚠️  有 {failed} 项检查失败，请修复后再运行测试")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
