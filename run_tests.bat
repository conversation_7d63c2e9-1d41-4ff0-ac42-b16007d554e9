@echo off
REM 用户管理CRUD API测试运行脚本 (Windows)
echo ========================================
echo 用户管理CRUD API自动化测试
echo ========================================

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或不在PATH中
    pause
    exit /b 1
)

REM 检查Django项目
if not exist "manage.py" (
    echo 错误: 未找到manage.py文件，请在Django项目根目录运行此脚本
    pause
    exit /b 1
)

echo.
echo 选择测试类型:
echo 1. 快速API测试 (推荐)
echo 2. 完整测试套件
echo 3. 特定测试类别
echo 4. Django单元测试
echo 5. 退出
echo.

set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto quick_test
if "%choice%"=="2" goto full_test
if "%choice%"=="3" goto category_test
if "%choice%"=="4" goto django_test
if "%choice%"=="5" goto end
goto invalid_choice

:quick_test
echo.
echo 🚀 运行快速API测试...
echo ----------------------------------------
python tests/scripts/quick_test.py
if errorlevel 1 (
    echo.
    echo ❌ 快速测试失败
) else (
    echo.
    echo ✅ 快速测试完成
)
goto end

:full_test
echo.
echo 🚀 运行完整测试套件...
echo ----------------------------------------
python tests/scripts/run_user_api_tests.py
if errorlevel 1 (
    echo.
    echo ❌ 完整测试失败
) else (
    echo.
    echo ✅ 完整测试完成
)
goto end

:category_test
echo.
echo 可用的测试类别:
echo - crud: CRUD操作测试
echo - permission: 权限控制测试
echo - validation: 数据验证测试
echo - search: 搜索过滤测试
echo - organization: 组织架构测试
echo - group: 用户组测试
echo - audit: 审计日志测试
echo - performance: 性能测试
echo - security: 安全测试
echo - compatibility: 兼容性测试
echo.
set /p category="请输入测试类别: "

echo.
echo 🚀 运行 %category% 测试...
echo ----------------------------------------
python tests/scripts/run_user_api_tests.py --category %category%
if errorlevel 1 (
    echo.
    echo ❌ %category% 测试失败
) else (
    echo.
    echo ✅ %category% 测试完成
)
goto end

:django_test
echo.
echo Django测试选项:
echo 1. 所有用户相关测试
echo 2. CRUD自动化测试
echo 3. CRUD API测试
echo 4. 兼容性测试
echo 5. 返回主菜单
echo.

set /p django_choice="请输入选择 (1-5): "

if "%django_choice%"=="1" (
    echo.
    echo 🚀 运行所有用户相关测试...
    echo ----------------------------------------
    python manage.py test tests.unit tests.integration --verbosity=2
)
if "%django_choice%"=="2" (
    echo.
    echo 🚀 运行CRUD自动化测试...
    echo ----------------------------------------
    python manage.py test tests.unit.test_user_crud_automation --verbosity=2
)
if "%django_choice%"=="3" (
    echo.
    echo 🚀 运行CRUD API测试...
    echo ----------------------------------------
    python manage.py test tests.unit.test_user_crud_api --verbosity=2
)
if "%django_choice%"=="4" (
    echo.
    echo 🚀 运行兼容性测试...
    echo ----------------------------------------
    python manage.py test tests.integration.test_user_crud_compatibility --verbosity=2
)
if "%django_choice%"=="5" goto start

goto end

:invalid_choice
echo.
echo ❌ 无效选择，请重新输入
echo.
goto start

:end
echo.
echo ========================================
echo 测试完成
echo ========================================
echo.
echo 测试报告位置: reports/
echo 查看详细文档: docs/testing-guide.md
echo.
pause
