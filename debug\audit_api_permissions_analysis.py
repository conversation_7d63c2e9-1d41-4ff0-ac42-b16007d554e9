#!/usr/bin/env python
"""
audit应用API权限分析报告
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def analyze_audit_api_permissions():
    """分析audit API的权限设置"""
    print("🔍 audit应用API权限分析报告")
    print("=" * 80)
    
    # 从视图代码中提取的权限信息
    api_endpoints_permissions = {
        # 主要API端点
        "/api/audit/business-operations/": {
            "view_class": "BusinessOperationLogListView",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取业务操作日志列表",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
        "/api/audit/password-access/": {
            "view_class": "PasswordAccessLogListView", 
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取密码访问日志列表",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
        "/api/audit/security-events/": {
            "view_class": "SecurityEventListView",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取安全事件列表",
            "required_role": "管理员", 
            "access_level": "管理员专用"
        },
        "/api/audit/security-events/{id}/": {
            "view_class": "SecurityEventDetailView",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET", "PUT", "PATCH"],
            "description": "获取/更新安全事件详情",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
        "/api/audit/model-changes/": {
            "view_class": "ModelChangeLogListView",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取模型变更日志",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
        "/api/audit/login-attempts/": {
            "view_class": "LoginAttemptListView",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取登录尝试记录",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
        "/api/audit/users/{user_id}/audit-trail/": {
            "view_function": "user_audit_trail",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取用户审计轨迹",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
        "/api/audit/model-changes/history/": {
            "view_function": "model_change_history",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取模型实例变更历史",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
        "/api/audit/statistics/": {
            "view_function": "audit_statistics",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取审计统计信息",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
        
        # Axes相关端点
        "/api/audit/axes/access-attempts/": {
            "view_class": "LoginAttemptListView",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取Axes访问尝试记录",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
        "/api/audit/axes/access-failures/": {
            "view_class": "AccessFailureLogListView",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取Axes访问失败记录",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
        "/api/audit/axes/access-logs/": {
            "view_class": "AccessLogListView",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取Axes访问日志",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
        
        # 兼容性端点
        "/api/audit/operation-logs/": {
            "view_class": "BusinessOperationLogListView",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取操作日志（兼容性端点）",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
        "/api/audit/access-logs/": {
            "view_class": "PasswordAccessLogListView",
            "permissions": ["IsAuthenticated", "IsAdminUser"],
            "methods": ["GET"],
            "description": "获取访问日志（兼容性端点）",
            "required_role": "管理员",
            "access_level": "管理员专用"
        },
    }
    
    print("📊 API端点权限分析:")
    print("-" * 80)
    
    # 按权限级别分组
    admin_only_endpoints = []
    authenticated_endpoints = []
    public_endpoints = []
    
    for endpoint, info in api_endpoints_permissions.items():
        permissions = info["permissions"]
        if "IsAdminUser" in permissions:
            admin_only_endpoints.append(endpoint)
        elif "IsAuthenticated" in permissions:
            authenticated_endpoints.append(endpoint)
        else:
            public_endpoints.append(endpoint)
    
    print(f"🔒 管理员专用端点 ({len(admin_only_endpoints)} 个):")
    for endpoint in admin_only_endpoints:
        info = api_endpoints_permissions[endpoint]
        print(f"   - {endpoint}")
        print(f"     描述: {info['description']}")
        print(f"     方法: {', '.join(info['methods'])}")
        print()
    
    print(f"🔐 认证用户端点 ({len(authenticated_endpoints)} 个):")
    if authenticated_endpoints:
        for endpoint in authenticated_endpoints:
            info = api_endpoints_permissions[endpoint]
            print(f"   - {endpoint}")
            print(f"     描述: {info['description']}")
            print()
    else:
        print("   无")
    
    print(f"🌐 公开访问端点 ({len(public_endpoints)} 个):")
    if public_endpoints:
        for endpoint in public_endpoints:
            info = api_endpoints_permissions[endpoint]
            print(f"   - {endpoint}")
            print(f"     描述: {info['description']}")
            print()
    else:
        print("   无")
    
    print("=" * 80)
    print("📋 权限分析总结:")
    print(f"   - 总端点数: {len(api_endpoints_permissions)}")
    print(f"   - 管理员专用: {len(admin_only_endpoints)} 个 ({len(admin_only_endpoints)/len(api_endpoints_permissions)*100:.1f}%)")
    print(f"   - 认证用户: {len(authenticated_endpoints)} 个 ({len(authenticated_endpoints)/len(api_endpoints_permissions)*100:.1f}%)")
    print(f"   - 公开访问: {len(public_endpoints)} 个 ({len(public_endpoints)/len(api_endpoints_permissions)*100:.1f}%)")
    
    print("\n🔍 权限设置特点:")
    print("   ✅ 所有audit API端点都需要管理员权限")
    print("   ✅ 使用了双重权限验证: IsAuthenticated + IsAdminUser")
    print("   ✅ 没有公开访问的audit端点，符合安全要求")
    print("   ✅ 兼容性端点使用相同的权限设置")
    
    return api_endpoints_permissions

def check_documentation_gaps():
    """检查文档中的权限说明缺失"""
    print("\n🔍 文档权限说明检查")
    print("=" * 80)
    
    # 检查现有文档
    documentation_issues = [
        {
            "file": "apps/audit/templates/audit_api_docs.html",
            "issues": [
                "❌ 缺少权限要求说明",
                "❌ 没有明确标注哪些端点需要管理员权限",
                "❌ 缺少JWT认证的详细使用说明",
                "❌ 没有权限不足时的错误响应示例"
            ]
        },
        {
            "file": "apps/api_docs.py",
            "issues": [
                "⚠️ 审计日志API部分缺少权限级别标注",
                "⚠️ 没有明确说明管理员权限要求"
            ]
        },
        {
            "file": "Password Locker API.yaml",
            "issues": [
                "❌ OpenAPI文档中缺少权限要求的security定义",
                "❌ 没有标注哪些端点需要管理员权限"
            ]
        }
    ]
    
    print("📋 文档问题清单:")
    for doc in documentation_issues:
        print(f"\n📄 {doc['file']}:")
        for issue in doc['issues']:
            print(f"   {issue}")
    
    return documentation_issues

def generate_documentation_recommendations():
    """生成文档改进建议"""
    print("\n💡 文档改进建议")
    print("=" * 80)
    
    recommendations = [
        {
            "priority": "高",
            "title": "更新audit API文档模板",
            "description": "在 apps/audit/templates/audit_api_docs.html 中添加权限要求说明",
            "actions": [
                "添加权限要求章节",
                "为每个端点标注所需权限级别",
                "添加JWT认证使用示例",
                "添加权限不足的错误响应示例"
            ]
        },
        {
            "priority": "高", 
            "title": "更新OpenAPI文档",
            "description": "在 Password Locker API.yaml 中添加安全定义",
            "actions": [
                "添加JWT Bearer认证的security scheme",
                "为每个audit端点添加security要求",
                "标注管理员权限要求"
            ]
        },
        {
            "priority": "中",
            "title": "更新API文档视图",
            "description": "在 apps/api_docs.py 中完善审计API说明",
            "actions": [
                "为审计API端点添加权限级别标注",
                "明确说明管理员权限要求",
                "添加权限相关的使用说明"
            ]
        },
        {
            "priority": "中",
            "title": "添加权限验证示例",
            "description": "提供权限验证的代码示例",
            "actions": [
                "添加前端权限检查示例",
                "提供API调用权限验证示例",
                "添加错误处理示例"
            ]
        }
    ]
    
    for i, rec in enumerate(recommendations, 1):
        print(f"\n{i}. 【{rec['priority']}优先级】{rec['title']}")
        print(f"   描述: {rec['description']}")
        print(f"   行动项:")
        for action in rec['actions']:
            print(f"     - {action}")
    
    return recommendations

def main():
    """主函数"""
    print("🚀 开始audit应用API权限分析")
    
    # 分析权限设置
    permissions_data = analyze_audit_api_permissions()
    
    # 检查文档缺失
    doc_issues = check_documentation_gaps()
    
    # 生成改进建议
    recommendations = generate_documentation_recommendations()
    
    print("\n" + "=" * 80)
    print("📊 分析完成总结")
    print("=" * 80)
    
    print("✅ 权限设置状况:")
    print("   - 所有audit API端点都正确设置了管理员权限要求")
    print("   - 使用了双重权限验证机制")
    print("   - 没有安全漏洞或权限过度开放的问题")
    
    print("\n❌ 文档问题:")
    print("   - API文档中缺少权限要求的明确说明")
    print("   - 没有为开发者提供权限验证的使用指导")
    print("   - OpenAPI文档缺少安全定义")
    
    print("\n🎯 下一步行动:")
    print("   1. 更新audit API文档模板，添加权限说明")
    print("   2. 完善OpenAPI文档的安全定义")
    print("   3. 为开发者提供权限验证的使用示例")
    print("   4. 确保所有API文档与实际代码实现保持一致")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
