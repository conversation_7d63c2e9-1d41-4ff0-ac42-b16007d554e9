#!/usr/bin/env python
"""
诊断admin登录问题
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def check_middleware_settings():
    """检查中间件设置"""
    print("🔍 检查中间件设置")
    print("=" * 60)
    
    try:
        from django.conf import settings
        
        middleware = getattr(settings, 'MIDDLEWARE', [])
        print("📋 当前中间件配置:")
        for i, mw in enumerate(middleware, 1):
            print(f"   {i}. {mw}")
        
        # 检查关键中间件
        required_middleware = [
            'django.middleware.security.SecurityMiddleware',
            'django.contrib.sessions.middleware.SessionMiddleware',
            'django.middleware.common.CommonMiddleware',
            'django.middleware.csrf.CsrfViewMiddleware',
            'django.contrib.auth.middleware.AuthenticationMiddleware',
            'django.contrib.messages.middleware.MessageMiddleware',
            'django.middleware.clickjacking.XFrameOptionsMiddleware',
        ]
        
        print("\n🔍 检查必需中间件:")
        for mw in required_middleware:
            if mw in middleware:
                print(f"   ✅ {mw}")
            else:
                print(f"   ❌ 缺失: {mw}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查中间件设置失败: {e}")
        return False

def check_admin_settings():
    """检查admin相关设置"""
    print("\n🔍 检查admin相关设置")
    print("=" * 60)
    
    try:
        from django.conf import settings
        
        # 检查INSTALLED_APPS
        installed_apps = getattr(settings, 'INSTALLED_APPS', [])
        admin_apps = [
            'django.contrib.admin',
            'django.contrib.auth',
            'django.contrib.contenttypes',
            'django.contrib.sessions',
            'django.contrib.messages',
        ]
        
        print("📋 检查admin相关应用:")
        for app in admin_apps:
            if app in installed_apps:
                print(f"   ✅ {app}")
            else:
                print(f"   ❌ 缺失: {app}")
        
        # 检查其他设置
        settings_to_check = [
            'SECRET_KEY',
            'DEBUG',
            'ALLOWED_HOSTS',
            'ROOT_URLCONF',
            'TEMPLATES',
            'DATABASES',
        ]
        
        print("\n📋 检查关键设置:")
        for setting_name in settings_to_check:
            if hasattr(settings, setting_name):
                value = getattr(settings, setting_name)
                if setting_name == 'SECRET_KEY':
                    print(f"   ✅ {setting_name}: {'已设置' if value else '未设置'}")
                elif setting_name == 'TEMPLATES':
                    print(f"   ✅ {setting_name}: {len(value)} 个模板配置")
                elif setting_name == 'DATABASES':
                    print(f"   ✅ {setting_name}: {list(value.keys())}")
                else:
                    print(f"   ✅ {setting_name}: {value}")
            else:
                print(f"   ❌ 缺失: {setting_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查admin设置失败: {e}")
        return False

def check_admin_user():
    """检查admin用户"""
    print("\n🔍 检查admin用户")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 查找超级用户
        superusers = User.objects.filter(is_superuser=True)
        print(f"📊 超级用户数量: {superusers.count()}")
        
        if superusers.exists():
            for user in superusers:
                print(f"   👑 {user.username} - 激活: {user.is_active}, 员工: {user.is_staff}")
        else:
            print("   ⚠️ 没有找到超级用户")
        
        # 查找管理员用户
        staff_users = User.objects.filter(is_staff=True)
        print(f"📊 管理员用户数量: {staff_users.count()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查admin用户失败: {e}")
        return False

def test_admin_login():
    """测试admin登录"""
    print("\n🔍 测试admin登录")
    print("=" * 60)
    
    try:
        from django.test import Client
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        client = Client()
        
        # 获取admin登录页面
        print("测试admin登录页面访问...")
        response = client.get('/admin/login/')
        print(f"   📡 GET /admin/login/: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ admin登录页面可访问")
        else:
            print(f"   ❌ admin登录页面访问失败: {response.status_code}")
            return False
        
        # 尝试登录
        superuser = User.objects.filter(is_superuser=True).first()
        if superuser:
            print(f"测试用户 {superuser.username} 登录...")
            
            # 获取CSRF token
            csrf_token = None
            if 'csrfmiddlewaretoken' in response.content.decode():
                # 简单提取CSRF token (实际应用中可能需要更复杂的解析)
                print("   📋 CSRF token已获取")
            
            # 这里我们不实际测试登录，因为我们不知道密码
            print("   ⚠️ 跳过实际登录测试（需要密码）")
        else:
            print("   ⚠️ 没有超级用户可供测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试admin登录失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_clickjacking_middleware():
    """检查clickjacking中间件"""
    print("\n🔍 检查clickjacking中间件")
    print("=" * 60)
    
    try:
        from django.conf import settings
        
        # 检查X-Frame-Options设置
        x_frame_options = getattr(settings, 'X_FRAME_OPTIONS', None)
        print(f"📋 X_FRAME_OPTIONS: {x_frame_options}")
        
        # 检查中间件是否正确配置
        middleware = getattr(settings, 'MIDDLEWARE', [])
        clickjacking_mw = 'django.middleware.clickjacking.XFrameOptionsMiddleware'
        
        if clickjacking_mw in middleware:
            position = middleware.index(clickjacking_mw)
            print(f"   ✅ clickjacking中间件已配置 (位置: {position + 1})")
            
            # 检查位置是否合理（应该在最后）
            if position == len(middleware) - 1:
                print("   ✅ clickjacking中间件位置正确（最后）")
            else:
                print("   ⚠️ clickjacking中间件不在最后位置")
        else:
            print("   ❌ clickjacking中间件未配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查clickjacking中间件失败: {e}")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 建议的解决方案")
    print("=" * 60)
    
    solutions = [
        "1. 检查中间件配置顺序",
        "2. 确保所有必需的中间件都已配置",
        "3. 检查CSRF设置",
        "4. 清除浏览器缓存和cookies",
        "5. 重启Django开发服务器",
        "6. 检查是否有自定义中间件冲突",
        "7. 临时禁用clickjacking中间件进行测试",
        "8. 检查Django版本兼容性",
    ]
    
    for solution in solutions:
        print(f"   {solution}")
    
    print("\n🔧 立即尝试的修复:")
    print("   1. 重启服务器: Ctrl+C 然后重新运行")
    print("   2. 清除浏览器数据: F12 -> Application -> Clear Storage")
    print("   3. 尝试无痕模式访问admin")

def create_test_admin_user():
    """创建测试admin用户"""
    print("\n🔧 创建测试admin用户")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 检查是否已有超级用户
        if User.objects.filter(is_superuser=True).exists():
            print("   ✅ 已存在超级用户，跳过创建")
            return True
        
        # 创建测试超级用户
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123!'
        )
        
        print(f"   ✅ 创建超级用户成功: {admin_user.username}")
        print("   📋 登录信息:")
        print("      用户名: admin")
        print("      密码: admin123!")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试admin用户失败: {e}")
        return False

def main():
    """主诊断函数"""
    print("🚀 开始admin登录问题诊断")
    print("=" * 80)
    
    tests = [
        ("中间件设置", check_middleware_settings),
        ("admin设置", check_admin_settings),
        ("admin用户", check_admin_user),
        ("clickjacking中间件", check_clickjacking_middleware),
        ("admin登录测试", test_admin_login),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            print("-" * 50)
    
    # 建议解决方案
    suggest_solutions()
    
    # 尝试创建测试用户
    create_test_admin_user()
    
    print("\n" + "=" * 80)
    print("📋 诊断总结")
    print("=" * 80)
    
    print(f"📊 诊断结果: {passed}/{total} 个检查通过")
    
    if passed == total:
        print("✅ 基本配置正常，问题可能是:")
        print("   1. 浏览器缓存问题")
        print("   2. CSRF token问题")
        print("   3. 会话问题")
        print("   4. 中间件执行顺序问题")
    else:
        print("⚠️ 发现配置问题，请检查失败的项目")
    
    print("\n🎯 立即尝试:")
    print("   1. 重启Django服务器")
    print("   2. 清除浏览器缓存")
    print("   3. 使用无痕模式访问 http://127.0.0.1:8001/admin/")
    print("   4. 如果有超级用户，尝试登录")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
