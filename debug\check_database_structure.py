#!/usr/bin/env python
"""
检查数据库结构，查找team_id字段问题
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def check_database_structure():
    """检查数据库结构"""
    print("🔍 检查数据库结构")
    print("=" * 60)
    
    try:
        from django.db import connection
        
        # 检查users表结构
        with connection.cursor() as cursor:
            cursor.execute("PRAGMA table_info(users);")
            columns = cursor.fetchall()
            
            print("📋 users表字段:")
            team_id_exists = False
            for col in columns:
                col_id, name, type_name, not_null, default, pk = col
                print(f"   {name} ({type_name}) - {'NOT NULL' if not_null else 'NULL'}")
                if name == 'team_id':
                    team_id_exists = True
                    print(f"   ❌ 发现team_id字段！")
            
            if not team_id_exists:
                print("   ✅ 没有发现team_id字段")
            
            # 检查teams表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='teams';
            """)
            teams_table = cursor.fetchone()
            
            if teams_table:
                print("\n❌ teams表仍然存在！")
                cursor.execute("PRAGMA table_info(teams);")
                team_columns = cursor.fetchall()
                print("📋 teams表字段:")
                for col in team_columns:
                    col_id, name, type_name, not_null, default, pk = col
                    print(f"   {name} ({type_name})")
            else:
                print("\n✅ teams表已删除")
        
        return team_id_exists, teams_table is not None
        
    except Exception as e:
        print(f"❌ 检查数据库结构失败: {e}")
        return None, None

def check_migrations():
    """检查迁移状态"""
    print("\n🔍 检查迁移状态")
    print("=" * 60)
    
    try:
        from django.core.management import call_command
        from io import StringIO
        import sys
        
        # 捕获迁移状态输出
        old_stdout = sys.stdout
        stdout_capture = StringIO()
        
        try:
            sys.stdout = stdout_capture
            call_command('showmigrations', 'users', verbosity=2)
        finally:
            sys.stdout = old_stdout
        
        output = stdout_capture.getvalue()
        print("📋 users应用迁移状态:")
        print(output)
        
        # 检查是否有未应用的迁移
        if '[ ]' in output:
            print("⚠️ 发现未应用的迁移")
            return False
        else:
            print("✅ 所有迁移已应用")
            return True
        
    except Exception as e:
        print(f"❌ 检查迁移状态失败: {e}")
        return None

def check_model_fields():
    """检查模型字段"""
    print("\n🔍 检查模型字段")
    print("=" * 60)
    
    try:
        from apps.users.models import User
        
        # 获取User模型的所有字段
        user_fields = [field.name for field in User._meta.fields]
        print("📋 User模型字段:")
        for field in user_fields:
            print(f"   {field}")
        
        if 'team' in user_fields:
            print("❌ User模型仍然包含team字段")
            return False
        else:
            print("✅ User模型不包含team字段")
            return True
        
    except Exception as e:
        print(f"❌ 检查模型字段失败: {e}")
        return None

def test_user_query():
    """测试用户查询"""
    print("\n🔍 测试用户查询")
    print("=" * 60)
    
    try:
        from apps.users.models import User
        
        # 尝试简单查询
        print("测试基本查询...")
        users = User.objects.all()
        print(f"   ✅ 基本查询成功，找到 {users.count()} 个用户")
        
        # 尝试select_related查询
        print("测试select_related查询...")
        users = User.objects.select_related('department', 'role').all()
        print(f"   ✅ select_related查询成功")
        
        # 尝试过滤查询
        print("测试过滤查询...")
        active_users = User.objects.filter(is_active=True)
        print(f"   ✅ 过滤查询成功，找到 {active_users.count()} 个活跃用户")
        
        return True
        
    except Exception as e:
        print(f"❌ 用户查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_fix_migration():
    """生成修复迁移"""
    print("\n🔧 生成修复迁移")
    print("=" * 60)
    
    try:
        from django.core.management import call_command
        
        print("生成新的迁移文件...")
        call_command('makemigrations', 'users', '--name', 'fix_team_id_removal')
        print("✅ 迁移文件生成成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成迁移失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始检查team_id字段问题")
    print("=" * 80)
    
    # 1. 检查数据库结构
    team_id_exists, teams_table_exists = check_database_structure()
    
    # 2. 检查迁移状态
    migrations_applied = check_migrations()
    
    # 3. 检查模型字段
    model_correct = check_model_fields()
    
    # 4. 测试用户查询
    query_works = test_user_query()
    
    print("\n" + "=" * 80)
    print("📋 问题诊断结果")
    print("=" * 80)
    
    if team_id_exists:
        print("❌ 问题确认: users表中仍然存在team_id字段")
        print("💡 解决方案:")
        print("   1. 需要生成新的迁移来删除team_id字段")
        print("   2. 可能之前的迁移没有正确执行")
        
        # 生成修复迁移
        if generate_fix_migration():
            print("\n🎯 下一步操作:")
            print("   运行: python manage.py migrate users")
            print("   这将应用修复迁移并删除team_id字段")
    
    elif teams_table_exists:
        print("❌ 问题确认: teams表仍然存在")
        print("💡 解决方案: 需要手动删除teams表")
    
    elif not query_works:
        print("❌ 问题确认: 查询仍然失败")
        print("💡 解决方案: 需要进一步调查查询问题")
    
    else:
        print("✅ 没有发现明显问题")
        print("💡 可能的原因:")
        print("   1. 缓存问题 - 尝试重启Django服务")
        print("   2. 查询中仍有team字段引用")
        print("   3. 数据库连接问题")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
