#!/usr/bin/env python
"""
修复auditlog相关问题
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def fix_auditlog_settings():
    """修复auditlog设置"""
    print("🔧 修复auditlog设置...")
    
    settings_file = "config/settings.py"
    
    try:
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 确保正确的auditlog设置
        fixes = [
            # 禁用文本变更记录，只使用JSON
            ('AUDITLOG_USE_TEXT_CHANGES_IF_JSON_IS_NOT_PRESENT = True', 
             'AUDITLOG_USE_TEXT_CHANGES_IF_JSON_IS_NOT_PRESENT = False'),
            
            # 确保不包含所有模型（避免性能问题）
            ('AUDITLOG_INCLUDE_ALL_MODELS = True', 
             'AUDITLOG_INCLUDE_ALL_MODELS = False'),
        ]
        
        modified = False
        for old, new in fixes:
            if old in content:
                content = content.replace(old, new)
                modified = True
                print(f"  ✅ 修复: {old} -> {new}")
        
        if modified:
            with open(settings_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ auditlog设置已修复")
        else:
            print("✅ auditlog设置无需修复")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复auditlog设置失败: {e}")
        return False

def check_auditlog_migrations():
    """检查auditlog迁移状态"""
    print("\n🔧 检查auditlog迁移状态...")
    
    try:
        from django.core.management import execute_from_command_line
        import subprocess
        
        # 检查迁移状态
        result = subprocess.run([
            'python', 'manage.py', 'showmigrations', 'auditlog'
        ], capture_output=True, text=True, cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        print("📊 auditlog迁移状态:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ 错误信息:")
            print(result.stderr)
        
        # 检查是否有未应用的迁移
        if '[ ]' in result.stdout:
            print("🔧 发现未应用的迁移，正在应用...")
            
            migrate_result = subprocess.run([
                'python', 'manage.py', 'migrate', 'auditlog'
            ], capture_output=True, text=True, cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            
            if migrate_result.returncode == 0:
                print("✅ auditlog迁移应用成功")
            else:
                print(f"❌ auditlog迁移应用失败: {migrate_result.stderr}")
                return False
        else:
            print("✅ 所有auditlog迁移已应用")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查auditlog迁移失败: {e}")
        return False

def test_auditlog_functionality():
    """测试auditlog功能"""
    print("\n🧪 测试auditlog功能...")
    
    try:
        from auditlog.models import LogEntry
        from django.contrib.auth import get_user_model
        from django.contrib.contenttypes.models import ContentType
        
        User = get_user_model()
        
        # 测试基本查询
        print("📊 基本功能测试:")
        
        # 1. 测试查询
        count = LogEntry.objects.count()
        print(f"  - 总记录数: {count}")
        
        # 2. 测试创建记录
        user_ct = ContentType.objects.get_for_model(User)
        test_entry = LogEntry.objects.create(
            content_type=user_ct,
            object_pk="test_fix",
            object_id=999,
            object_repr="Test Fix Object",
            action=1,  # CREATE
            changes={"test": "fix_value"}
        )
        print(f"  - 创建测试记录: {test_entry.id}")
        
        # 3. 测试字段访问
        print(f"  - changes字段: {test_entry.changes}")
        print(f"  - changes_text字段: {test_entry.changes_text}")
        
        # 4. 测试查询过滤
        filtered = LogEntry.objects.filter(object_repr__contains="Test Fix")
        print(f"  - 过滤查询结果: {filtered.count()}")
        
        # 5. 清理测试记录
        test_entry.delete()
        print("  - 清理测试记录完成")
        
        print("✅ auditlog功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ auditlog功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_auditlog_registry():
    """修复auditlog注册"""
    print("\n🔧 修复auditlog注册...")
    
    try:
        # 重新注册模型
        from apps.audit.auditlog_registry import register_models
        
        print("📊 重新注册auditlog模型...")
        register_models()
        print("✅ auditlog模型注册完成")
        
        return True
        
    except Exception as e:
        print(f"❌ auditlog注册失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_auditlog_test_script():
    """创建auditlog测试脚本"""
    print("\n🔧 创建auditlog测试脚本...")
    
    test_script = """#!/usr/bin/env python
'''
auditlog问题诊断脚本
'''
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def diagnose_auditlog():
    from auditlog.models import LogEntry
    from django.db import connection
    
    print("🔍 auditlog诊断报告")
    print("=" * 40)
    
    # 检查表结构
    cursor = connection.cursor()
    cursor.execute("PRAGMA table_info(auditlog_logentry)")
    columns = [col[1] for col in cursor.fetchall()]
    print(f"表字段: {columns}")
    
    # 检查记录数
    count = LogEntry.objects.count()
    print(f"记录数: {count}")
    
    # 检查最近记录
    if count > 0:
        recent = LogEntry.objects.order_by('-timestamp').first()
        print(f"最新记录: {recent.object_repr} at {recent.timestamp}")
    
    print("✅ 诊断完成")

if __name__ == '__main__':
    diagnose_auditlog()
"""
    
    try:
        with open('debug/auditlog_diagnosis.py', 'w', encoding='utf-8') as f:
            f.write(test_script)
        print("✅ 创建auditlog测试脚本: debug/auditlog_diagnosis.py")
        return True
    except Exception as e:
        print(f"❌ 创建测试脚本失败: {e}")
        return False

def main():
    """主修复函数"""
    print("🚀 开始修复auditlog问题")
    print("=" * 50)
    
    fixes = [
        fix_auditlog_settings,
        check_auditlog_migrations,
        fix_auditlog_registry,
        test_auditlog_functionality,
        create_auditlog_test_script,
    ]
    
    passed = 0
    total = len(fixes)
    
    for fix_func in fixes:
        try:
            if fix_func():
                passed += 1
            print("-" * 40)
        except Exception as e:
            print(f"❌ 修复 {fix_func.__name__} 出现异常: {e}")
            print("-" * 40)
    
    print("=" * 50)
    print(f"📊 修复结果: {passed}/{total} 个步骤成功")
    
    if passed == total:
        print("🎉 auditlog问题修复完成！")
        print("\n💡 修复总结:")
        print("  1. ✅ 调整了auditlog设置")
        print("  2. ✅ 检查并应用了迁移")
        print("  3. ✅ 重新注册了模型")
        print("  4. ✅ 验证了功能正常")
        print("  5. ✅ 创建了诊断脚本")
        print("\n🔄 请重启Django服务器以应用更改")
    else:
        print("⚠️ 部分修复失败，需要手动检查")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
