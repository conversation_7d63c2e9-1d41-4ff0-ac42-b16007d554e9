#!/usr/bin/env python
"""
auditlog问题诊断脚本
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()


def diagnose_auditlog():
    from auditlog.models import LogEntry
    from django.db import connection

    print("🔍 auditlog诊断报告")
    print("=" * 40)

    # 检查表结构
    cursor = connection.cursor()
    cursor.execute("PRAGMA table_info(auditlog_logentry)")
    columns = [col[1] for col in cursor.fetchall()]
    print(f"表字段: {columns}")

    # 检查记录数
    count = LogEntry.objects.count()
    print(f"记录数: {count}")

    # 检查最近记录
    if count > 0:
        recent = LogEntry.objects.order_by("-timestamp").first()
        print(f"最新记录: {recent.object_repr} at {recent.timestamp}")

    print("✅ 诊断完成")


if __name__ == "__main__":
    diagnose_auditlog()
