# Generated by Django 5.2.4 on 2025-07-30 07:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("passwords", "0007_remove_tags"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="PasswordEntryGroup",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="组名称")),
                ("description", models.TextField(blank=True, verbose_name="组描述")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否激活"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_password_groups",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建者",
                    ),
                ),
            ],
            options={
                "verbose_name": "密码组",
                "verbose_name_plural": "密码组",
                "db_table": "password_entry_groups",
                "unique_together": {("name", "created_by")},
            },
        ),
        migrations.CreateModel(
            name="PasswordEntryGroupMembership",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "added_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="添加时间"),
                ),
                (
                    "added_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="添加者",
                    ),
                ),
                (
                    "group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="passwords.passwordentrygroup",
                        verbose_name="密码组",
                    ),
                ),
                (
                    "password_entry",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="passwords.passwordentry",
                        verbose_name="密码条目",
                    ),
                ),
            ],
            options={
                "verbose_name": "密码条目组成员关系",
                "verbose_name_plural": "密码条目组成员关系",
                "db_table": "password_entry_group_memberships",
                "unique_together": {("password_entry", "group")},
            },
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="groups",
            field=models.ManyToManyField(
                blank=True,
                related_name="password_entries",
                through="passwords.PasswordEntryGroupMembership",
                to="passwords.passwordentrygroup",
                verbose_name="所属组",
            ),
        ),
        migrations.CreateModel(
            name="GroupPermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "permission",
                    models.CharField(
                        choices=[
                            ("view", "查看"),
                            ("edit", "编辑"),
                            ("manage", "管理"),
                            ("admin", "管理员"),
                        ],
                        max_length=10,
                        verbose_name="权限级别",
                    ),
                ),
                (
                    "granted_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="授权时间"),
                ),
                (
                    "granted_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="granted_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="授权者",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
                (
                    "group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="passwords.passwordentrygroup",
                        verbose_name="密码组",
                    ),
                ),
            ],
            options={
                "verbose_name": "组权限",
                "verbose_name_plural": "组权限",
                "db_table": "password_group_permissions",
                "unique_together": {("user", "group")},
            },
        ),
    ]
