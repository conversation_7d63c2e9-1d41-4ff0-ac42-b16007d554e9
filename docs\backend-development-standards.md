# 后端开发技术规范文档

## 目录

1. [异常处理规范](#1-异常处理规范)
2. [日志记录规范](#2-日志记录规范)
3. [代码规范](#3-代码规范)
4. [项目结构规范](#4-项目结构规范)
5. [API设计规范](#5-api设计规范)
6. [数据库规范](#6-数据库规范)
7. [安全规范](#7-安全规范)
8. [测试规范](#8-测试规范)

---

## 1. 异常处理规范

### 1.1 自定义异常类定义

基于当前项目实践，定义统一的异常类：

```python
# utils/exceptions.py
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from django.core.exceptions import ValidationError
import logging

logger = logging.getLogger(__name__)

class VaultBaseException(Exception):
    """密码管理系统基础异常类"""
    default_message = "系统错误"
    default_code = "SYSTEM_ERROR"
    default_status = status.HTTP_500_INTERNAL_SERVER_ERROR
    
    def __init__(self, message=None, code=None, status_code=None, extra_data=None):
        self.message = message or self.default_message
        self.code = code or self.default_code
        self.status_code = status_code or self.default_status
        self.extra_data = extra_data or {}
        super().__init__(self.message)

class PermissionDeniedException(VaultBaseException):
    """权限不足异常"""
    default_message = "权限不足"
    default_code = "INSUFFICIENT_PERMISSION"
    default_status = status.HTTP_403_FORBIDDEN

class ResourceNotFoundException(VaultBaseException):
    """资源不存在异常"""
    default_message = "资源不存在"
    default_code = "RESOURCE_NOT_FOUND"
    default_status = status.HTTP_404_NOT_FOUND

class ValidationException(VaultBaseException):
    """数据验证异常"""
    default_message = "数据验证失败"
    default_code = "VALIDATION_ERROR"
    default_status = status.HTTP_400_BAD_REQUEST
```

### 1.2 统一错误响应格式

```python
def custom_exception_handler(exc, context):
    """自定义异常处理器"""
    response = exception_handler(exc, context)
    
    if response is not None:
        if isinstance(exc, VaultBaseException):
            custom_response_data = {
                'success': False,
                'error': exc.message,
                'code': exc.code,
                'timestamp': timezone.now().isoformat(),
                'path': context['request'].path,
                'method': context['request'].method,
                **exc.extra_data
            }
        else:
            custom_response_data = {
                'success': False,
                'error': '系统内部错误',
                'code': 'INTERNAL_ERROR',
                'timestamp': timezone.now().isoformat(),
                'path': context['request'].path,
                'method': context['request'].method,
            }
        
        response.data = custom_response_data
        
        # 记录异常日志
        logger.error(
            f"API异常: {exc.__class__.__name__}: {str(exc)}, "
            f"Path: {context['request'].path}, "
            f"Method: {context['request'].method}, "
            f"User: {getattr(context['request'].user, 'username', 'Anonymous')}"
        )
    
    return response
```

### 1.3 视图中的异常处理最佳实践

```python
# 基于项目中的实际实现模式
class PasswordEntryDetailView(APIView):
    def get(self, request, pk):
        try:
            password_entry = get_object_or_404(PasswordEntry, pk=pk)
            
            # 权限检查
            if not self.has_permission(request.user, password_entry, 'browse'):
                raise PermissionDeniedException(
                    message="需要浏览权限",
                    extra_data={'required_permission': 'browse'}
                )
            
            serializer = PasswordEntrySerializer(password_entry)
            return Response({
                'success': True,
                'data': serializer.data,
                'message': '获取成功'
            })
            
        except PasswordEntry.DoesNotExist:
            raise ResourceNotFoundException(message="密码条目不存在")
        except Exception as e:
            logger.error(f"获取密码条目失败: {e}")
            raise VaultBaseException(
                message="获取密码条目失败",
                code="PASSWORD_FETCH_ERROR"
            )
```

### 1.4 API错误码定义

```python
# utils/error_codes.py
class ErrorCodes:
    """统一错误码定义"""
    
    # 系统级错误 (1000-1999)
    SYSTEM_ERROR = "1000"
    INTERNAL_ERROR = "1001"
    SERVICE_UNAVAILABLE = "1002"
    
    # 认证授权错误 (2000-2999)
    AUTHENTICATION_FAILED = "2000"
    TOKEN_EXPIRED = "2001"
    TOKEN_INVALID = "2002"
    INSUFFICIENT_PERMISSION = "2003"
    ACCOUNT_LOCKED = "2004"
    
    # 数据验证错误 (3000-3999)
    VALIDATION_ERROR = "3000"
    REQUIRED_FIELD_MISSING = "3001"
    INVALID_FORMAT = "3002"
    DUPLICATE_ENTRY = "3003"
    
    # 业务逻辑错误 (4000-4999)
    RESOURCE_NOT_FOUND = "4000"
    OPERATION_NOT_ALLOWED = "4001"
    RESOURCE_CONFLICT = "4002"
    QUOTA_EXCEEDED = "4003"
```

---

## 2. 日志记录规范

### 2.1 日志级别使用标准

基于项目中的实际使用模式：

```python
import logging

logger = logging.getLogger(__name__)

# DEBUG: 详细的调试信息，仅在开发环境使用
logger.debug(f"处理密码条目: {password_entry.id}, 用户: {user.username}")

# INFO: 一般信息记录，记录正常的业务流程
logger.info(f"用户登录成功: username={user.username}, ip={ip_address}")

# WARNING: 警告信息，可能的问题但不影响系统运行
logger.warning(f"用户认证失败: username={username}, ip={ip_address}")

# ERROR: 错误信息，需要关注但系统仍可运行
logger.error(f"密码解密失败: password_id={password_id}, error={str(e)}")

# CRITICAL: 严重错误，可能导致系统不可用
logger.critical(f"数据库连接失败: {str(e)}")
```

### 2.2 日志格式和内容要求

```python
# config/settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        'json': {
            'format': '{"level": "%(levelname)s", "time": "%(asctime)s", "module": "%(module)s", "message": "%(message)s"}',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/app.log',
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'apps': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

### 2.3 敏感信息日志处理规则

```python
# 基于项目中的安全实践
def safe_log_user_action(user, action, target=None, extra_data=None):
    """安全的用户操作日志记录"""
    safe_extra_data = {}
    if extra_data:
        # 过滤敏感字段
        sensitive_fields = ['password', 'token', 'secret', 'key']
        for key, value in extra_data.items():
            if any(field in key.lower() for field in sensitive_fields):
                safe_extra_data[key] = '***MASKED***'
            else:
                safe_extra_data[key] = value
    
    logger.info(
        f"用户操作: user={user.username}, action={action}, "
        f"target={target}, data={safe_extra_data}"
    )
```

### 2.4 审计日志记录标准

```python
# 基于 apps/audit/models.py 的实现
class AuditLogger:
    """审计日志记录器"""
    
    @staticmethod
    def log_operation(user, action_type, target_type=None, target_id=None, 
                     target_name=None, result='success', description=None,
                     ip_address=None, user_agent=None, extra_data=None):
        """记录操作日志"""
        from apps.audit.models import BusinessOperationLog

        BusinessOperationLog.objects.create(
            user=user,
            action_type=action_type,
            target_type=target_type,
            target_id=target_id,
            target_name=target_name,
            result=result,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            extra_data=extra_data or {}
        )
    
    @staticmethod
    def log_access(user, password_entry, access_type, ip_address=None, 
                   user_agent=None, access_source='direct', source_id=None):
        """记录访问日志"""
        from apps.audit.models import PasswordAccessLog

        PasswordAccessLog.objects.create(
            user=user,
            password_entry=password_entry,
            access_type=access_type,
            ip_address=ip_address,
            user_agent=user_agent,
            access_source=access_source,
            source_id=source_id
        )
```

---

## 3. 代码规范

### 3.1 命名约定

基于项目中的实际命名模式：

```python
# 变量命名：使用小写字母和下划线
password_entry = PasswordEntry.objects.get(pk=pk)
user_permissions = get_user_permissions(user)
is_favorite = password_entry.is_favorite

# 函数命名：使用小写字母和下划线，动词开头
def get_user_accessible_passwords(user_id):
    """获取用户可访问的密码列表"""
    pass

def has_permission(user, password_entry, permission_type):
    """检查用户是否有指定权限"""
    pass

# 类命名：使用驼峰命名法
class PasswordEntryListCreateView(generics.ListCreateAPIView):
    """密码条目列表和创建视图"""
    pass

class PasswordPermissionService:
    """密码权限服务类"""
    pass

# 常量命名：使用大写字母和下划线
SYSTEM_TYPE_CHOICES = [
    ('os', '操作系统'),
    ('db', '数据库'),
    ('mdw', '中间件'),
]

DEFAULT_PAGE_SIZE = 20
MAX_FAILED_ATTEMPTS = 5
```

### 3.2 代码注释和文档字符串规范

```python
# 基于项目中的文档字符串实践
class PasswordEntry(models.Model):
    """
    密码条目模型

    用于存储用户的密码信息，包括基本认证信息、系统连接信息等。
    密码字段使用加密存储，确保数据安全。

    Attributes:
        title (str): 密码条目标题
        username (str): 用户名
        password (str): 加密存储的密码
        system_type (str): 系统类型（操作系统、数据库等）
        ip_address (str): 目标系统IP地址
        port (int): 端口号
        created_at (datetime): 创建时间
        updated_at (datetime): 更新时间
    """

    def decrypt_password(self, user):
        """
        解密密码

        Args:
            user (User): 请求解密的用户对象

        Returns:
            str: 解密后的明文密码

        Raises:
            PermissionDeniedException: 用户无权限访问
            DecryptionException: 解密失败

        Example:
            >>> password_entry = PasswordEntry.objects.get(pk=1)
            >>> plain_password = password_entry.decrypt_password(user)
        """
        # 实现代码...
        pass

def get_user_accessible_passwords(user_id, filters=None):
    """
    获取用户可访问的密码列表

    根据用户权限和过滤条件，返回用户有权限访问的密码条目列表。
    包括用户拥有的密码和被分享的密码。

    Args:
        user_id (int): 用户ID
        filters (dict, optional): 过滤条件，包括：
            - search (str): 搜索关键词
            - category_id (int): 分类ID
            - system_type (str): 系统类型
            - is_favorite (bool): 是否收藏

    Returns:
        QuerySet: 密码条目查询集

    Example:
        >>> passwords = get_user_accessible_passwords(
        ...     user_id=1,
        ...     filters={'search': 'database', 'is_favorite': True}
        ... )
    """
    # 实现代码...
    pass

# 复杂业务逻辑的注释
def process_password_sharing(password_id, target_users, permissions, expiry_date=None):
    """处理密码分享逻辑"""

    # 1. 验证分享者权限
    # 只有密码所有者或具有admin权限的用户才能分享
    if not has_admin_permission(current_user, password_id):
        raise PermissionDeniedException("无权限分享此密码")

    # 2. 验证目标用户
    # 确保目标用户存在且处于活跃状态
    valid_users = validate_target_users(target_users)

    # 3. 创建分享记录
    # 为每个目标用户创建权限记录
    for user in valid_users:
        create_password_permission(
            password_id=password_id,
            user_id=user.id,
            permissions=permissions,
            expiry_date=expiry_date
        )

    # 4. 记录审计日志
    log_sharing_operation(password_id, target_users, permissions)
```

### 3.3 导入语句组织方式

```python
# 基于项目中的导入组织模式
# 1. 标准库导入
import os
import uuid
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Union

# 2. Django相关导入
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from django.shortcuts import get_object_or_404

# 3. DRF相关导入
from rest_framework import status, generics, permissions, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination

# 4. 第三方库导入
from django_filters.rest_framework import DjangoFilterBackend
import pyotp
import qrcode

# 5. 本地应用导入
from .models import PasswordEntry, Category, PasswordPermission
from .serializers import PasswordEntrySerializer, CategorySerializer
from utils.encryption import encrypt_data, decrypt_data
from utils.permissions import has_password_permission
from apps.audit.models import OperationLog, AccessLog
```

### 3.4 代码格式化标准（PEP 8）

```python
# 行长度：最大88字符（项目使用black格式化工具）
def create_password_entry_with_permissions(
    title: str,
    username: str,
    password: str,
    system_type: str,
    owner: User,
    category: Optional[Category] = None,
    permissions: Optional[List[Dict]] = None,
) -> PasswordEntry:
    """创建密码条目并设置权限"""

    # 空行使用：类和函数定义前后使用两个空行
    # 方法定义前后使用一个空行

    # 字符串引号：优先使用双引号
    error_message = "密码创建失败"

    # 列表、字典格式化
    system_types = [
        "os",
        "db",
        "mdw",
        "ftp",
    ]

    config = {
        "encryption_algorithm": "fernet",
        "key_rotation_days": 90,
        "backup_enabled": True,
    }

    # 条件语句格式化
    if (
        password_strength_score >= 8
        and not is_common_password(password)
        and len(password) >= 12
    ):
        # 密码强度足够
        pass

    # 链式调用格式化
    passwords = (
        PasswordEntry.objects
        .filter(owner=owner)
        .select_related("category")
        .prefetch_related("permissions")
        .order_by("-created_at")
    )
```

---

## 4. 项目结构规范

### 4.1 Django应用组织方式

基于当前项目的实际结构：

```
backend/
├── apps/                          # 应用模块目录
│   ├── __init__.py
│   ├── api_docs.py               # API文档视图
│   ├── users/                    # 用户管理应用
│   │   ├── __init__.py
│   │   ├── apps.py              # 应用配置
│   │   ├── models.py            # 数据模型
│   │   ├── views.py             # 视图
│   │   ├── serializers.py       # 序列化器
│   │   ├── urls.py              # URL配置
│   │   ├── admin.py             # 管理后台
│   │   ├── menu_views.py        # 菜单相关视图
│   │   └── migrations/          # 数据库迁移文件
│   ├── passwords/               # 密码管理应用
│   │   ├── __init__.py
│   │   ├── models.py
│   │   ├── views.py
│   │   ├── permission_views.py  # 权限相关视图
│   │   ├── serializers.py
│   │   ├── urls.py
│   │   ├── filters.py           # 过滤器
│   │   ├── management/          # 管理命令
│   │   │   └── commands/
│   │   │       └── create_test_data.py
│   │   └── migrations/
│   ├── categories/              # 分类管理应用
│   ├── sharing/                 # 密码分享应用
│   ├── audit/                   # 审计日志应用
│   └── system/                  # 系统管理应用
├── config/                      # 项目配置
│   ├── __init__.py
│   ├── settings.py             # 设置文件
│   ├── urls.py                 # 根URL配置
│   └── wsgi.py                 # WSGI配置
├── utils/                       # 工具模块
│   ├── __init__.py
│   ├── authentication.py       # 认证工具
│   ├── encryption.py           # 加密工具
│   ├── middleware.py           # 中间件
│   ├── password_middleware.py  # 密码权限中间件
│   ├── permissions.py          # 权限工具
│   └── validators.py           # 验证器
├── templates/                   # 模板文件
│   └── emails/                 # 邮件模板
├── static/                      # 静态文件
├── media/                       # 媒体文件
├── tests/                       # 测试目录
│   ├── __init__.py
│   ├── conftest.py             # pytest配置文件
│   ├── unit/                   # 单元测试
│   │   ├── __init__.py
│   │   ├── test_user_crud_automation.py
│   │   ├── test_user_crud_api.py
│   │   └── test_*.py           # 其他单元测试
│   ├── integration/            # 集成测试
│   │   ├── __init__.py
│   │   ├── test_user_crud_compatibility.py
│   │   └── test_*.py           # 其他集成测试
│   ├── scripts/                # 测试脚本
│   │   ├── __init__.py
│   │   ├── run_user_api_tests.py
│   │   ├── quick_test.py
│   │   └── check_test_environment.py
│   └── fixtures/               # 测试数据
│       ├── users.json
│       └── test_data.json
├── reports/                     # 测试报告目录
│   ├── test_reports/           # 测试结果报告
│   └── coverage_reports/       # 覆盖率报告
├── logs/                        # 日志文件
├── docs/                        # 文档目录
├── pytest.ini                  # pytest配置文件
├── requirements.txt             # 依赖列表
├── manage.py                   # Django管理脚本
├── run_tests.bat               # Windows测试启动脚本
├── run_tests.sh                # Linux/Mac测试启动脚本
└── README.md                   # 项目说明
```

### 4.2 文件和目录命名规则

```python
# 应用命名：使用复数形式，小写字母和下划线
apps/users/          # 用户管理
apps/passwords/      # 密码管理
apps/audit/          # 审计日志

# 模型文件：单数形式，描述性命名
models.py            # 主要模型
permission_models.py # 权限相关模型（如果模型较多）

# 视图文件：功能性命名
views.py             # 主要视图
permission_views.py  # 权限相关视图
api_views.py         # API视图（如果需要分离）

# 序列化器文件
serializers.py       # 主要序列化器
permission_serializers.py  # 权限相关序列化器

# 工具文件：功能性命名
utils/encryption.py      # 加密工具
utils/permissions.py     # 权限工具
utils/validators.py      # 验证器
utils/middleware.py      # 中间件
```

### 4.3 配置文件管理

```python
# config/settings.py - 基于项目实际配置结构
import os
from pathlib import Path
import environ
from datetime import timedelta

# 环境变量配置
env = environ.Env(DEBUG=(bool, False))
environ.Env.read_env(os.path.join(BASE_DIR, ".env"))

# 应用分组配置
DJANGO_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
]

THIRD_PARTY_APPS = [
    "rest_framework",
    "rest_framework_simplejwt.token_blacklist",
    "corsheaders",
    "django_filters",
    "drf_spectacular",
]

LOCAL_APPS = [
    "apps.users",
    "apps.passwords",
    "apps.categories",
    "apps.sharing",
    "apps.audit",
    "apps.system",
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

# 中间件配置
MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "utils.middleware.RequestLoggingMiddleware",
    "utils.middleware.SecurityMiddleware",
    "utils.password_middleware.PasswordPermissionMiddleware",
]

# 数据库配置 - 支持环境变量
if env("DATABASE_URL", default=None):
    import dj_database_url
    DATABASES = {"default": dj_database_url.parse(env("DATABASE_URL"))}
else:
    DATABASES = {
        "default": {
            "ENGINE": env("DB_ENGINE", default="django.db.backends.sqlite3"),
            "NAME": env("DB_NAME", default=BASE_DIR / "db.sqlite3"),
            "USER": env("DB_USER", default=""),
            "PASSWORD": env("DB_PASSWORD", default=""),
            "HOST": env("DB_HOST", default=""),
            "PORT": env("DB_PORT", default=""),
        }
    }
```

### 4.4 环境变量配置

```bash
# .env.example - 基于项目实际配置
# Django配置
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置
DB_ENGINE=django.db.backends.mysql
DB_NAME=password_manager
DB_USER=root
DB_PASSWORD=your-password
DB_HOST=localhost
DB_PORT=3306

# 或使用DATABASE_URL（优先级更高）
# DATABASE_URL=mysql://user:password@localhost:3306/dbname

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT配置
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_LIFETIME=3600
JWT_REFRESH_TOKEN_LIFETIME=86400

# 加密配置
SM4_KEY=your-sm4-encryption-key

# 邮件配置
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# 前端配置
FRONTEND_BASE_URL=http://localhost:5666

# 安全配置
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
SECURE_SSL_REDIRECT=False
```

---

## 5. API设计规范

### 5.1 RESTful API设计原则

基于项目中的API实现模式：

```python
# URL设计规范 - 基于 apps/passwords/urls.py
urlpatterns = [
    # 资源集合操作
    path("", PasswordEntryListCreateView.as_view(), name="password_list_create"),

    # 单个资源操作
    path("<uuid:pk>/", PasswordEntryDetailView.as_view(), name="password_detail"),

    # 资源的特定操作
    path("<uuid:pk>/copy/", PasswordCopyView.as_view(), name="password_copy"),
    path("<uuid:pk>/toggle-favorite/", PasswordToggleFavoriteView.as_view(), name="password_toggle_favorite"),

    # 嵌套资源
    path("<uuid:pk>/permissions/", PasswordPermissionListView.as_view(), name="password_permissions"),
    path("<uuid:pk>/permissions/grant/", PasswordPermissionGrantView.as_view(), name="password_permission_grant"),

    # 批量操作
    path("batch-delete/", batch_delete_passwords, name="password_batch_delete"),

    # 工具类接口
    path("generator/", PasswordGeneratorView.as_view(), name="password_generator"),
    path("security-analysis/", SecurityAnalysisView.as_view(), name="security_analysis"),
]
```

### 5.2 URL路径命名规范

```python
# URL命名规范
# 1. 使用小写字母和连字符
/api/passwords/                    # 密码列表
/api/passwords/{id}/              # 单个密码
/api/passwords/{id}/copy/         # 复制密码
/api/passwords/batch-delete/      # 批量删除

# 2. 使用复数形式表示资源集合
/api/users/                       # 用户列表
/api/categories/                  # 分类列表
/api/audit/operation-logs/        # 操作日志列表

# 3. 嵌套资源使用层级结构
/api/passwords/{id}/permissions/  # 密码权限
/api/users/{id}/departments/      # 用户部门
/api/sharing/share-links/         # 分享链接

# 4. 动作使用动词
/api/passwords/generator/         # 密码生成器
/api/auth/login/                  # 登录
/api/auth/logout/                 # 登出
/api/system/maintenance/          # 系统维护
```

### 5.3 HTTP方法使用规范

```python
# 基于项目中的实际实现
class PasswordEntryListCreateView(generics.ListCreateAPIView):
    """
    GET /api/passwords/     - 获取密码列表
    POST /api/passwords/    - 创建新密码
    """

class PasswordEntryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    GET /api/passwords/{id}/     - 获取单个密码
    PUT /api/passwords/{id}/     - 完整更新密码
    PATCH /api/passwords/{id}/   - 部分更新密码
    DELETE /api/passwords/{id}/  - 删除密码
    """

# 自定义操作使用POST
class PasswordCopyView(APIView):
    """
    POST /api/passwords/{id}/copy/  - 复制密码（获取明文）
    """

class PasswordToggleFavoriteView(APIView):
    """
    POST /api/passwords/{id}/toggle-favorite/  - 切换收藏状态
    """
```

### 5.4 请求和响应数据格式

```python
# 统一响应格式 - 基于项目实际实现
class StandardResponse:
    """标准API响应格式"""

    @staticmethod
    def success(data=None, message="操作成功", total=None, page=None, page_size=None):
        """成功响应"""
        response = {
            "success": True,
            "message": message,
            "data": data,
            "timestamp": timezone.now().isoformat()
        }

        # 分页信息
        if total is not None:
            response.update({
                "total": total,
                "page": page,
                "page_size": page_size
            })

        return Response(response, status=status.HTTP_200_OK)

    @staticmethod
    def error(message="操作失败", code=None, errors=None, status_code=status.HTTP_400_BAD_REQUEST):
        """错误响应"""
        response = {
            "success": False,
            "message": message,
            "timestamp": timezone.now().isoformat()
        }

        if code:
            response["code"] = code
        if errors:
            response["errors"] = errors

        return Response(response, status=status_code)

# 使用示例
class PasswordEntryListCreateView(generics.ListCreateAPIView):
    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return StandardResponse.success(
                data=serializer.data,
                message="获取密码列表成功",
                total=queryset.count(),
                page=request.query_params.get('page', 1),
                page_size=self.paginator.page_size
            )

        serializer = self.get_serializer(queryset, many=True)
        return StandardResponse.success(
            data=serializer.data,
            message="获取密码列表成功"
        )
```

### 5.5 分页、过滤、排序标准实现

```python
# 分页配置 - 基于项目实际实现
class StandardPagination(PageNumberPagination):
    """标准分页器"""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100
    page_query_param = 'page'

# 过滤器实现 - 基于 apps/passwords/filters.py
class PasswordEntryFilter(django_filters.FilterSet):
    """密码条目过滤器"""

    # 文本搜索
    search = django_filters.CharFilter(method='filter_search', label='搜索')

    # 精确匹配
    category_id = django_filters.NumberFilter(field_name='category__id', label='分类ID')
    system_type = django_filters.ChoiceFilter(choices=PasswordEntry.SYSTEM_TYPE_CHOICES, label='系统类型')
    is_favorite = django_filters.BooleanFilter(label='是否收藏')

    # 范围过滤
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte', label='创建时间起')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte', label='创建时间止')

    class Meta:
        model = PasswordEntry
        fields = ['search', 'category_id', 'system_type', 'is_favorite', 'created_after', 'created_before']

    def filter_search(self, queryset, name, value):
        """自定义搜索过滤"""
        if value:
            return queryset.filter(
                Q(title__icontains=value) |
                Q(username__icontains=value) |
                Q(ip_address__icontains=value) |
                Q(description__icontains=value)
            )
        return queryset

# 视图中的使用
class PasswordEntryListCreateView(generics.ListCreateAPIView):
    """密码条目列表和创建视图"""

    serializer_class = PasswordEntrySerializer
    pagination_class = StandardPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = PasswordEntryFilter
    search_fields = ['title', 'username', 'ip_address']
    ordering_fields = ['created_at', 'updated_at', 'title']
    ordering = ['-created_at']  # 默认排序
```

---

## 6. 数据库规范

### 6.1 模型字段命名和类型选择

```python
# 基于项目中的模型实现 - apps/passwords/models.py
class PasswordEntry(models.Model):
    """密码条目模型"""

    # 主键：使用UUID
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # 字符串字段：指定最大长度
    title = models.CharField(max_length=200, verbose_name=_("标题"))
    username = models.CharField(max_length=200, verbose_name=_("用户名"))

    # 长文本：使用TextField
    password = models.TextField(verbose_name=_("密码"))  # 加密存储
    description = models.TextField(blank=True, null=True, verbose_name=_("描述"))

    # 选择字段：使用choices
    system_type = models.CharField(
        max_length=20,
        choices=SYSTEM_TYPE_CHOICES,
        default="other",
        verbose_name=_("系统类型"),
    )

    # 可选字段：使用null=True, blank=True
    ip_address = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=_("IP地址")
    )
    port = models.PositiveIntegerField(null=True, blank=True, verbose_name=_("端口"))

    # 外键关系：使用ForeignKey
    category = models.ForeignKey(
        'categories.Category',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("分类")
    )
    owner = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        verbose_name=_("所有者")
    )

    # 布尔字段：提供默认值
    is_favorite = models.BooleanField(default=False, verbose_name=_("是否收藏"))
    is_deleted = models.BooleanField(default=False, verbose_name=_("是否删除"))

    # 时间字段：使用auto_now_add和auto_now
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name=_("删除时间"))

    class Meta:
        verbose_name = _("密码条目")
        verbose_name_plural = _("密码条目")
        ordering = ['-created_at']

        # 数据库索引
        indexes = [
            models.Index(fields=['owner', 'is_deleted']),
            models.Index(fields=['category', 'is_deleted']),
            models.Index(fields=['system_type']),
            models.Index(fields=['created_at']),
        ]

        # 唯一约束
        constraints = [
            models.UniqueConstraint(
                fields=['owner', 'title'],
                condition=models.Q(is_deleted=False),
                name='unique_title_per_owner'
            )
        ]
```

### 6.2 数据库迁移管理

```python
# 迁移文件命名规范
# 0001_initial.py                    # 初始迁移
# 0002_add_password_groups.py        # 添加密码组功能
# 0003_alter_password_fields.py     # 修改密码字段
# 0004_add_soft_delete.py           # 添加软删除功能

# 迁移最佳实践
class Migration(migrations.Migration):
    """添加密码组功能"""

    dependencies = [
        ('passwords', '0001_initial'),
    ]

    operations = [
        # 1. 先添加新字段（可为空）
        migrations.AddField(
            model_name='passwordentry',
            name='group',
            field=models.ForeignKey(
                null=True,
                blank=True,
                on_delete=models.SET_NULL,
                to='passwords.passwordentrygroup'
            ),
        ),

        # 2. 添加索引
        migrations.AddIndex(
            model_name='passwordentry',
            index=models.Index(fields=['group'], name='password_group_idx'),
        ),

        # 3. 数据迁移（如果需要）
        migrations.RunPython(
            code=migrate_existing_passwords_to_groups,
            reverse_code=migrations.RunPython.noop,
        ),
    ]

def migrate_existing_passwords_to_groups(apps, schema_editor):
    """数据迁移函数"""
    PasswordEntry = apps.get_model('passwords', 'PasswordEntry')
    PasswordEntryGroup = apps.get_model('passwords', 'PasswordEntryGroup')

    # 为没有组的密码创建默认组
    for password in PasswordEntry.objects.filter(group__isnull=True):
        default_group, created = PasswordEntryGroup.objects.get_or_create(
            name=f"{password.owner.username}的默认组",
            owner=password.owner,
            defaults={'description': '系统自动创建的默认组'}
        )
        password.group = default_group
        password.save()
```

### 6.3 查询优化建议

```python
# 基于项目中的查询优化实践
class PasswordEntryQuerySet(models.QuerySet):
    """密码条目查询集"""

    def with_related(self):
        """预加载相关对象"""
        return self.select_related(
            'owner',
            'category'
        ).prefetch_related(
            'permissions__user',
            'custom_fields',
            'attachments'
        )

    def accessible_by_user(self, user):
        """获取用户可访问的密码"""
        return self.filter(
            Q(owner=user) |  # 用户拥有的密码
            Q(permissions__user=user, permissions__is_active=True)  # 被分享的密码
        ).distinct()

    def not_deleted(self):
        """排除已删除的记录"""
        return self.filter(is_deleted=False)

    def by_category(self, category_id):
        """按分类过滤"""
        return self.filter(category_id=category_id)

class PasswordEntry(models.Model):
    # ... 字段定义 ...

    objects = PasswordEntryQuerySet.as_manager()

    @classmethod
    def get_user_passwords_optimized(cls, user, filters=None):
        """优化的用户密码查询"""
        queryset = (
            cls.objects
            .with_related()
            .accessible_by_user(user)
            .not_deleted()
        )

        # 应用过滤条件
        if filters:
            if filters.get('search'):
                queryset = queryset.filter(
                    Q(title__icontains=filters['search']) |
                    Q(username__icontains=filters['search'])
                )

            if filters.get('category_id'):
                queryset = queryset.by_category(filters['category_id'])

        return queryset.order_by('-created_at')

# 使用示例
def get_password_list(request):
    """获取密码列表 - 优化版本"""
    filters = {
        'search': request.GET.get('search'),
        'category_id': request.GET.get('category_id'),
    }

    # 使用优化的查询
    passwords = PasswordEntry.get_user_passwords_optimized(
        user=request.user,
        filters=filters
    )

    # 分页处理
    paginator = Paginator(passwords, 20)
    page = paginator.get_page(request.GET.get('page', 1))

    return page
```

### 6.4 数据完整性约束

```python
# 基于项目中的约束实现
class PasswordEntry(models.Model):
    # ... 字段定义 ...

    class Meta:
        # 唯一约束
        constraints = [
            # 同一用户下标题唯一（排除已删除）
            models.UniqueConstraint(
                fields=['owner', 'title'],
                condition=models.Q(is_deleted=False),
                name='unique_title_per_owner'
            ),

            # 检查约束
            models.CheckConstraint(
                check=models.Q(port__gte=1, port__lte=65535),
                name='valid_port_range'
            ),

            # 条件约束
            models.CheckConstraint(
                check=models.Q(
                    models.Q(system_type='database', database_name__isnull=False) |
                    ~models.Q(system_type='database')
                ),
                name='database_requires_name'
            ),
        ]

        # 数据库索引
        indexes = [
            # 复合索引
            models.Index(fields=['owner', 'is_deleted', 'created_at']),
            models.Index(fields=['category', 'system_type']),

            # 单字段索引
            models.Index(fields=['title']),
            models.Index(fields=['ip_address']),

            # 部分索引（仅对活跃记录）
            models.Index(
                fields=['created_at'],
                condition=models.Q(is_deleted=False),
                name='active_passwords_created_idx'
            ),
        ]

# 自定义验证器
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

def validate_ip_address(value):
    """验证IP地址格式"""
    import ipaddress
    try:
        ipaddress.ip_address(value)
    except ValueError:
        raise ValidationError(_('请输入有效的IP地址'))

def validate_port_range(value):
    """验证端口范围"""
    if not (1 <= value <= 65535):
        raise ValidationError(_('端口号必须在1-65535之间'))

class PasswordEntry(models.Model):
    ip_address = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        validators=[validate_ip_address],
        verbose_name=_("IP地址")
    )
    port = models.PositiveIntegerField(
        null=True,
        blank=True,
        validators=[validate_port_range],
        verbose_name=_("端口")
    )
```

---

## 7. 安全规范

### 7.1 认证和授权实现标准

```python
# JWT认证实现 - 基于 utils/authentication.py
from rest_framework_simplejwt.authentication import JWTAuthentication as BaseJWTAuthentication
from rest_framework import exceptions
from django.utils.translation import gettext_lazy as _

class JWTAuthentication(BaseJWTAuthentication):
    """自定义JWT认证类"""

    def authenticate(self, request):
        header = self.get_header(request)
        if header is None:
            return None

        raw_token = self.get_raw_token(header)
        if raw_token is None:
            return None

        validated_token = self.get_validated_token(raw_token)
        user = self.get_user(validated_token)

        # 检查用户状态
        if not user.is_active:
            raise exceptions.AuthenticationFailed(_("用户账户已被禁用"))

        # 检查用户是否被锁定
        if hasattr(user, "locked_until") and user.locked_until:
            from django.utils import timezone
            if timezone.now() < user.locked_until:
                raise exceptions.AuthenticationFailed(_("账户已被锁定，请稍后再试"))

        return user, validated_token

# 权限检查 - 基于 utils/password_permissions.py
class PasswordPermissionService:
    """密码权限服务"""

    @staticmethod
    def has_permission(password_id, user_id, permission_type):
        """检查用户是否有指定权限"""
        try:
            password_entry = PasswordEntry.objects.get(id=password_id)

            # 所有者拥有所有权限
            if password_entry.owner_id == user_id:
                return True

            # 检查分享权限
            permission = PasswordPermission.objects.filter(
                password_entry_id=password_id,
                user_id=user_id,
                is_active=True
            ).first()

            if not permission:
                return False

            # 检查权限类型
            permission_map = {
                'browse': permission.can_browse,
                'read': permission.can_read,
                'write': permission.can_write,
                'admin': permission.can_admin,
            }

            return permission_map.get(permission_type, False)

        except PasswordEntry.DoesNotExist:
            return False
        except Exception as e:
            logger.error(f"权限检查异常: {e}")
            return False

# 权限装饰器
from functools import wraps
from django.http import JsonResponse

def require_password_permission(permission_type):
    """密码权限装饰器"""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            password_id = kwargs.get('pk') or kwargs.get('password_id')

            if not password_id:
                return JsonResponse({
                    'error': '缺少密码ID参数',
                    'code': 'MISSING_PASSWORD_ID'
                }, status=400)

            if not PasswordPermissionService.has_permission(
                password_id, request.user.id, permission_type
            ):
                return JsonResponse({
                    'error': f'需要 {permission_type} 权限',
                    'code': 'INSUFFICIENT_PERMISSION'
                }, status=403)

            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator
```

### 7.2 数据加密和敏感信息处理

```python
# 加密工具 - 基于 utils/encryption.py
from cryptography.fernet import Fernet
from django.conf import settings
import base64
import logging

logger = logging.getLogger(__name__)

class EncryptionService:
    """加密服务类"""

    def __init__(self):
        # 从环境变量获取加密密钥
        key = settings.SM4_KEY.encode() if hasattr(settings, 'SM4_KEY') else Fernet.generate_key()
        self.cipher = Fernet(base64.urlsafe_b64encode(key[:32]))

    def encrypt(self, plaintext: str) -> str:
        """加密字符串"""
        try:
            if not plaintext:
                return ""

            encrypted_data = self.cipher.encrypt(plaintext.encode('utf-8'))
            return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')

        except Exception as e:
            logger.error(f"加密失败: {e}")
            raise EncryptionException("数据加密失败")

    def decrypt(self, encrypted_text: str) -> str:
        """解密字符串"""
        try:
            if not encrypted_text:
                return ""

            encrypted_data = base64.urlsafe_b64decode(encrypted_text.encode('utf-8'))
            decrypted_data = self.cipher.decrypt(encrypted_data)
            return decrypted_data.decode('utf-8')

        except Exception as e:
            logger.error(f"解密失败: {e}")
            raise DecryptionException("数据解密失败")

# 全局加密实例
encryption_service = EncryptionService()

def encrypt_data(plaintext: str) -> str:
    """加密数据"""
    return encryption_service.encrypt(plaintext)

def decrypt_data(encrypted_text: str) -> str:
    """解密数据"""
    return encryption_service.decrypt(encrypted_text)

# 模型中的加密字段
class PasswordEntry(models.Model):
    password = models.TextField(verbose_name=_("密码"))  # 存储加密后的密码

    def set_password(self, plaintext_password):
        """设置密码（自动加密）"""
        self.password = encrypt_data(plaintext_password)

    def get_password(self):
        """获取密码（自动解密）"""
        return decrypt_data(self.password)

    def save(self, *args, **kwargs):
        # 确保密码被加密
        if hasattr(self, '_plaintext_password'):
            self.set_password(self._plaintext_password)
            delattr(self, '_plaintext_password')
        super().save(*args, **kwargs)
```

### 7.3 CORS和CSRF防护配置

```python
# CORS配置 - 基于 config/settings.py
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://localhost:5666",
    "http://127.0.0.1:5666",
    # 生产环境域名
    "https://vault.example.com",
]

CORS_ALLOW_CREDENTIALS = True

CORS_ALLOWED_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# CSRF配置
CSRF_TRUSTED_ORIGINS = [
    "http://localhost:5666",
    "https://vault.example.com",
]

# 安全中间件配置
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# 生产环境安全配置
if not DEBUG:
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
```

### 7.4 输入验证和数据清理

```python
# 输入验证 - 基于 utils/validators.py
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import re

def validate_password_strength(password):
    """验证密码强度"""
    if len(password) < 8:
        raise ValidationError(_('密码长度至少8位'))

    if not re.search(r'[A-Z]', password):
        raise ValidationError(_('密码必须包含大写字母'))

    if not re.search(r'[a-z]', password):
        raise ValidationError(_('密码必须包含小写字母'))

    if not re.search(r'\d', password):
        raise ValidationError(_('密码必须包含数字'))

    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        raise ValidationError(_('密码必须包含特殊字符'))

def validate_username(username):
    """验证用户名格式"""
    if not re.match(r'^[a-zA-Z0-9_-]+$', username):
        raise ValidationError(_('用户名只能包含字母、数字、下划线和连字符'))

    if len(username) < 3:
        raise ValidationError(_('用户名长度至少3位'))

# 序列化器中的验证
class PasswordEntrySerializer(serializers.ModelSerializer):
    """密码条目序列化器"""

    def validate_title(self, value):
        """验证标题"""
        if not value.strip():
            raise serializers.ValidationError("标题不能为空")

        # 检查HTML标签
        if re.search(r'<[^>]+>', value):
            raise serializers.ValidationError("标题不能包含HTML标签")

        return value.strip()

    def validate_ip_address(self, value):
        """验证IP地址"""
        if value:
            import ipaddress
            try:
                ipaddress.ip_address(value)
            except ValueError:
                raise serializers.ValidationError("请输入有效的IP地址")
        return value

    def validate(self, attrs):
        """整体验证"""
        # 检查端口和IP地址的组合
        if attrs.get('port') and not attrs.get('ip_address'):
            raise serializers.ValidationError("指定端口时必须提供IP地址")

        return attrs

# XSS防护
import html
from django.utils.html import escape

def sanitize_input(text):
    """清理用户输入"""
    if not text:
        return text

    # HTML转义
    text = escape(text)

    # 移除潜在的脚本标签
    text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.IGNORECASE | re.DOTALL)

    return text.strip()
```

---

## 8. 测试规范

### 8.1 单元测试编写标准

```python
# tests/test_password_models.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from apps.passwords.models import PasswordEntry, Category
from utils.encryption import encrypt_data, decrypt_data

User = get_user_model()

class PasswordEntryModelTest(TestCase):
    """密码条目模型测试"""

    def setUp(self):
        """测试前置设置"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.category = Category.objects.create(
            name='测试分类',
            owner=self.user
        )

    def test_create_password_entry(self):
        """测试创建密码条目"""
        password_entry = PasswordEntry.objects.create(
            title='测试密码',
            username='admin',
            password=encrypt_data('secret123'),
            system_type='os',
            owner=self.user,
            category=self.category
        )

        self.assertEqual(password_entry.title, '测试密码')
        self.assertEqual(password_entry.username, 'admin')
        self.assertEqual(password_entry.owner, self.user)
        self.assertEqual(password_entry.category, self.category)
        self.assertIsNotNone(password_entry.id)
        self.assertIsNotNone(password_entry.created_at)

    def test_password_encryption_decryption(self):
        """测试密码加密解密"""
        original_password = 'MySecretPassword123!'

        password_entry = PasswordEntry.objects.create(
            title='加密测试',
            username='admin',
            password=encrypt_data(original_password),
            owner=self.user
        )

        # 验证密码被加密存储
        self.assertNotEqual(password_entry.password, original_password)

        # 验证可以正确解密
        decrypted_password = decrypt_data(password_entry.password)
        self.assertEqual(decrypted_password, original_password)

    def test_unique_title_constraint(self):
        """测试标题唯一性约束"""
        PasswordEntry.objects.create(
            title='重复标题',
            username='admin1',
            password=encrypt_data('pass1'),
            owner=self.user
        )

        # 同一用户创建相同标题应该失败
        with self.assertRaises(ValidationError):
            password_entry = PasswordEntry(
                title='重复标题',
                username='admin2',
                password=encrypt_data('pass2'),
                owner=self.user
            )
            password_entry.full_clean()

    def test_soft_delete(self):
        """测试软删除功能"""
        password_entry = PasswordEntry.objects.create(
            title='待删除密码',
            username='admin',
            password=encrypt_data('pass'),
            owner=self.user
        )

        # 执行软删除
        password_entry.is_deleted = True
        password_entry.save()

        # 验证记录仍存在但被标记为删除
        self.assertTrue(password_entry.is_deleted)
        self.assertIsNotNone(password_entry.deleted_at)

# tests/test_password_views.py
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model

User = get_user_model()

class PasswordAPITest(TestCase):
    """密码API测试"""

    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)

    def test_create_password_entry(self):
        """测试创建密码条目API"""
        url = reverse('password_list_create')
        data = {
            'title': '新密码',
            'username': 'admin',
            'password': 'secret123',
            'system_type': 'os'
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['data']['title'], '新密码')
        self.assertEqual(response.data['data']['username'], 'admin')

    def test_get_password_list(self):
        """测试获取密码列表API"""
        # 创建测试数据
        PasswordEntry.objects.create(
            title='测试密码1',
            username='admin1',
            password=encrypt_data('pass1'),
            owner=self.user
        )
        PasswordEntry.objects.create(
            title='测试密码2',
            username='admin2',
            password=encrypt_data('pass2'),
            owner=self.user
        )

        url = reverse('password_list_create')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['data']), 2)

    def test_unauthorized_access(self):
        """测试未授权访问"""
        self.client.force_authenticate(user=None)

        url = reverse('password_list_create')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
```

### 8.2 测试文件组织方式

```
backend/
├── tests/                          # 测试目录
│   ├── __init__.py
│   ├── test_models/               # 模型测试
│   │   ├── __init__.py
│   │   ├── test_password_models.py
│   │   ├── test_user_models.py
│   │   └── test_category_models.py
│   ├── test_views/                # 视图测试
│   │   ├── __init__.py
│   │   ├── test_password_views.py
│   │   ├── test_auth_views.py
│   │   └── test_sharing_views.py
│   ├── test_utils/                # 工具测试
│   │   ├── __init__.py
│   │   ├── test_encryption.py
│   │   ├── test_permissions.py
│   │   └── test_validators.py
│   ├── test_integration/          # 集成测试
│   │   ├── __init__.py
│   │   ├── test_password_workflow.py
│   │   └── test_sharing_workflow.py
│   └── fixtures/                  # 测试数据
│       ├── users.json
│       ├── passwords.json
│       └── categories.json
```

### 8.3 测试数据管理

```python
# tests/factories.py - 使用factory_boy创建测试数据
import factory
from django.contrib.auth import get_user_model
from apps.passwords.models import PasswordEntry, Category
from utils.encryption import encrypt_data

User = get_user_model()

class UserFactory(factory.django.DjangoModelFactory):
    """用户工厂"""
    class Meta:
        model = User

    username = factory.Sequence(lambda n: f'user{n}')
    email = factory.LazyAttribute(lambda obj: f'{obj.username}@example.com')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')

class CategoryFactory(factory.django.DjangoModelFactory):
    """分类工厂"""
    class Meta:
        model = Category

    name = factory.Faker('word')
    description = factory.Faker('text', max_nb_chars=200)
    owner = factory.SubFactory(UserFactory)

class PasswordEntryFactory(factory.django.DjangoModelFactory):
    """密码条目工厂"""
    class Meta:
        model = PasswordEntry

    title = factory.Faker('sentence', nb_words=3)
    username = factory.Faker('user_name')
    password = factory.LazyFunction(lambda: encrypt_data('TestPassword123!'))
    system_type = factory.Faker('random_element', elements=['os', 'db', 'mdw'])
    ip_address = factory.Faker('ipv4')
    port = factory.Faker('random_int', min=1, max=65535)
    owner = factory.SubFactory(UserFactory)
    category = factory.SubFactory(CategoryFactory)

# 使用示例
class PasswordTestCase(TestCase):
    def setUp(self):
        self.user = UserFactory()
        self.category = CategoryFactory(owner=self.user)
        self.passwords = PasswordEntryFactory.create_batch(5, owner=self.user)
```

### 8.4 测试目录结构规范

```
tests/                          # 测试根目录
├── __init__.py
├── conftest.py                 # pytest全局配置
├── unit/                       # 单元测试目录
│   ├── __init__.py
│   ├── test_user_crud_automation.py    # 用户CRUD自动化测试
│   ├── test_user_crud_api.py           # 用户CRUD API测试
│   ├── test_password_models.py         # 密码模型测试
│   ├── test_password_views.py          # 密码视图测试
│   ├── test_encryption.py              # 加密工具测试
│   └── test_*.py                       # 其他单元测试
├── integration/                # 集成测试目录
│   ├── __init__.py
│   ├── test_user_crud_compatibility.py # 用户CRUD兼容性测试
│   ├── test_password_workflow.py       # 密码管理工作流测试
│   ├── test_sharing_workflow.py        # 分享功能工作流测试
│   └── test_*.py                       # 其他集成测试
├── scripts/                    # 测试脚本目录
│   ├── __init__.py
│   ├── run_user_api_tests.py           # 完整测试运行器
│   ├── quick_test.py                   # 快速API测试工具
│   ├── check_test_environment.py       # 环境检查工具
│   └── performance_test.py             # 性能测试脚本
└── fixtures/                   # 测试数据目录
    ├── users.json              # 用户测试数据
    ├── passwords.json          # 密码测试数据
    └── test_data.json          # 通用测试数据

reports/                        # 测试报告目录
├── test_reports/               # 测试结果报告
│   ├── user_api_test_report_*.json
│   └── quick_test_report_*.json
└── coverage_reports/           # 覆盖率报告
    ├── htmlcov/               # HTML覆盖率报告
    └── coverage.xml           # XML覆盖率报告
```

### 8.5 测试文件命名规范

```python
# 单元测试文件命名
test_<模块名>_<功能>.py
test_user_crud_automation.py      # 用户CRUD自动化测试
test_password_models.py           # 密码模型测试
test_encryption_utils.py          # 加密工具测试

# 集成测试文件命名
test_<功能>_<类型>.py
test_user_crud_compatibility.py   # 用户CRUD兼容性测试
test_password_workflow.py         # 密码工作流测试
test_api_integration.py           # API集成测试

# 测试类命名
class <功能>TestCase(TestCase):
class UserCRUDTestCase(TestCase):      # 用户CRUD测试类
class PasswordModelTest(TestCase):     # 密码模型测试类
class EncryptionServiceTest(TestCase): # 加密服务测试类

# 测试方法命名
def test_<具体功能>(self):
def test_create_user_with_valid_data(self):    # 测试使用有效数据创建用户
def test_encrypt_password_successfully(self):  # 测试成功加密密码
def test_unauthorized_access_denied(self):     # 测试拒绝未授权访问
```

### 8.6 测试配置文件

```python
# pytest.ini - 项目根目录
[tool:pytest]
DJANGO_SETTINGS_MODULE = config.settings
python_files = test_*.py *_test.py *_tests.py
python_classes = Test* *Test *TestCase
python_functions = test_*
testpaths = tests
addopts =
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --reuse-db
    --nomigrations
markers =
    unit: 单元测试
    integration: 集成测试
    api: API测试
    slow: 慢速测试
    security: 安全测试
    performance: 性能测试

# tests/conftest.py - 测试配置
import os
import sys
import django
from django.conf import settings

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# 测试配置
TEST_CONFIG = {
    'API_BASE_URL': 'http://localhost:8001',
    'ADMIN_USERNAME': 'admin',
    'ADMIN_PASSWORD': 'admin123',
    'TEST_TIMEOUT': 30,
}
```

### 8.7 测试运行方式

```bash
# 1. 使用便捷脚本运行测试
# Windows
run_tests.bat

# Linux/Mac
./run_tests.sh

# 2. 使用测试脚本运行
# 快速API测试
python tests/scripts/quick_test.py

# 完整测试套件
python tests/scripts/run_user_api_tests.py

# 特定类别测试
python tests/scripts/run_user_api_tests.py --category crud
python tests/scripts/run_user_api_tests.py --category security

# 3. 使用Django测试命令
# 运行所有测试
python manage.py test tests

# 运行单元测试
python manage.py test tests.unit

# 运行集成测试
python manage.py test tests.integration

# 运行特定测试文件
python manage.py test tests.unit.test_user_crud_automation

# 运行特定测试类
python manage.py test tests.unit.test_user_crud_automation.UserCRUDTestCase

# 运行特定测试方法
python manage.py test tests.unit.test_user_crud_automation.UserCRUDTestCase.test_create_user

# 4. 使用pytest运行
# 运行所有测试
pytest

# 运行特定目录
pytest tests/unit/
pytest tests/integration/

# 运行特定标记的测试
pytest -m unit
pytest -m integration
pytest -m api

# 运行特定文件
pytest tests/unit/test_user_crud_automation.py

# 详细输出
pytest -v

# 并行运行
pytest -n 4
```

### 8.8 测试覆盖率要求

```python
# 覆盖率配置 - .coveragerc
[run]
source = .
omit =
    */venv/*
    */migrations/*
    */tests/*
    manage.py
    */settings/*
    */wsgi.py
    */asgi.py
    */__pycache__/*
    */node_modules/*

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:

# 覆盖率要求
# 整体覆盖率: >= 80%
# 核心业务逻辑: >= 90%
# 工具函数: >= 85%

# 运行覆盖率测试
coverage run --source='.' manage.py test tests
coverage report
coverage html

# 生成覆盖率报告
coverage xml  # 用于CI/CD
```

### 8.9 测试数据管理

```python
# tests/fixtures/users.json - 用户测试数据
[
    {
        "model": "users.user",
        "pk": 1,
        "fields": {
            "username": "testuser",
            "email": "<EMAIL>",
            "is_active": true,
            "is_staff": false
        }
    }
]

# 使用测试数据
class UserTestCase(TestCase):
    fixtures = ['users.json', 'passwords.json']

    def setUp(self):
        self.user = User.objects.get(username='testuser')

# 使用Factory创建测试数据
import factory
from django.contrib.auth import get_user_model

User = get_user_model()

class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User

    username = factory.Sequence(lambda n: f'user{n}')
    email = factory.LazyAttribute(lambda obj: f'{obj.username}@example.com')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')

# 使用示例
class UserTestCase(TestCase):
    def setUp(self):
        self.user = UserFactory()
        self.users = UserFactory.create_batch(5)
```

### 8.10 测试报告和监控

```python
# 测试报告格式
{
    "timestamp": "2024-08-04T15:30:00",
    "summary": {
        "total_duration": 45.67,
        "total_tests": 156,
        "total_failures": 0,
        "total_errors": 0,
        "success_rate": 100.0
    },
    "modules": {
        "tests.unit.test_user_crud_automation": {
            "success": true,
            "duration": 23.45,
            "test_count": 89,
            "failure_count": 0,
            "error_count": 0
        }
    }
}

# 测试报告存储位置
reports/
├── test_reports/
│   ├── user_api_test_report_20240804_153000.json
│   └── quick_test_report_20240804_153000.json
└── coverage_reports/
    ├── htmlcov/index.html
    └── coverage.xml

# CI/CD集成
# GitHub Actions示例
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.11
    - name: Install dependencies
      run: pip install -r requirements.txt
    - name: Run tests
      run: python tests/scripts/run_user_api_tests.py
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

---

## 总结

本技术规范文档基于当前Django密码管理系统的实际代码库架构制定，涵盖了后端开发的各个重要方面：

### 核心原则
1. **安全第一**：所有敏感数据加密存储，完善的权限控制
2. **代码质量**：遵循PEP 8规范，完善的文档和注释
3. **可维护性**：清晰的项目结构，模块化设计
4. **可测试性**：完善的测试覆盖，自动化测试流程

### 关键特性
- 统一的异常处理和错误响应格式
- 完善的日志记录和审计追踪
- RESTful API设计规范
- 数据库优化和安全约束
- 多层次的安全防护机制
- 全面的测试策略

### 使用建议
1. 新功能开发严格遵循本规范
2. 定期review现有代码，逐步改进
3. 持续更新规范，适应项目发展
4. 团队培训，确保规范执行

本规范将作为团队开发的重要参考，确保代码质量和项目的长期可维护性。
```
