#!/usr/bin/env python
"""
测试django-axes和django-auditlog集成
"""
import os
import sys
import django
import requests
import json
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.audit.models import BusinessOperationLog, SecurityEvent
from apps.audit.utils import log_business_operation, log_security_event
from django.contrib.auth import get_user_model
from auditlog.models import LogEntry
from axes.models import AccessAttempt

User = get_user_model()

def test_business_operation_log():
    """测试业务操作日志"""
    print("🧪 测试业务操作日志...")
    
    try:
        # 获取或创建测试用户
        user, created = User.objects.get_or_create(
            username='test_audit_user',
            defaults={
                'email': '<EMAIL>',
                'name': '审计测试用户'
            }
        )
        
        # 记录业务操作
        log_business_operation(
            user=user,
            action_type='user_login',
            description='测试登录操作',
            target_type='user',
            target_id=str(user.id),
            target_name=user.username,
            extra_data={'test': True}
        )
        
        # 验证记录
        log_count = BusinessOperationLog.objects.filter(user=user).count()
        print(f"✅ 业务操作日志记录成功，共 {log_count} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 业务操作日志测试失败: {e}")
        return False


def test_security_event():
    """测试安全事件"""
    print("🧪 测试安全事件...")
    
    try:
        # 记录安全事件
        log_security_event(
            event_type='suspicious_login',
            severity='high',
            title='可疑登录尝试',
            description='检测到来自异常IP的登录尝试',
            event_data={
                'ip_address': '*************',
                'user_agent': 'Test Agent',
                'timestamp': datetime.now().isoformat()
            }
        )
        
        # 验证记录
        event_count = SecurityEvent.objects.filter(event_type='suspicious_login').count()
        print(f"✅ 安全事件记录成功，共 {event_count} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 安全事件测试失败: {e}")
        return False


def test_auditlog_integration():
    """测试auditlog集成"""
    print("🧪 测试auditlog集成...")
    
    try:
        # 创建或更新用户以触发auditlog
        user, created = User.objects.get_or_create(
            username='test_auditlog_user',
            defaults={
                'email': '<EMAIL>',
                'name': '审计日志测试用户'
            }
        )
        
        if not created:
            # 更新用户以触发auditlog
            user.name = f'更新的用户名_{datetime.now().strftime("%H%M%S")}'
            user.save()
        
        # 检查auditlog记录
        log_entries = LogEntry.objects.filter(
            content_type__model='user',
            object_pk=str(user.id)
        ).count()
        
        print(f"✅ Auditlog集成成功，共 {log_entries} 条变更记录")
        
        return True
        
    except Exception as e:
        print(f"❌ Auditlog集成测试失败: {e}")
        return False


def test_axes_integration():
    """测试axes集成"""
    print("🧪 测试axes集成...")
    
    try:
        # 检查axes表是否存在
        attempt_count = AccessAttempt.objects.count()
        print(f"✅ Axes集成成功，当前有 {attempt_count} 条访问尝试记录")
        
        return True
        
    except Exception as e:
        print(f"❌ Axes集成测试失败: {e}")
        return False


def test_api_endpoints():
    """测试API端点"""
    print("🧪 测试API端点...")
    
    base_url = 'http://localhost:8001'
    
    # 测试端点列表
    endpoints = [
        '/api/audit/business-operations/',
        '/api/audit/security-events/',
        '/api/audit/model-changes/',
        '/api/audit/login-attempts/',
        '/api/audit/statistics/',
    ]
    
    success_count = 0
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code in [200, 401, 403]:  # 401/403 表示端点存在但需要认证
                print(f"✅ {endpoint} - 状态码: {response.status_code}")
                success_count += 1
            else:
                print(f"❌ {endpoint} - 状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint} - 连接错误: {e}")
    
    print(f"API端点测试完成: {success_count}/{len(endpoints)} 个端点正常")
    return success_count == len(endpoints)


def main():
    """主测试函数"""
    print("🚀 开始django-axes和django-auditlog集成测试")
    print("=" * 60)
    
    tests = [
        test_business_operation_log,
        test_security_event,
        test_auditlog_integration,
        test_axes_integration,
        test_api_endpoints,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 40)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            print("-" * 40)
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！django-axes和django-auditlog集成成功！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置和实现")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
