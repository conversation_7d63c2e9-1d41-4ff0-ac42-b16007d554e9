#!/usr/bin/env python
"""
测试登录功能
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def test_login_api():
    """测试登录API"""
    print("🧪 测试登录API...")
    
    try:
        from django.test import Client
        from django.contrib.auth import get_user_model
        from django.urls import reverse
        import json
        
        User = get_user_model()
        
        # 创建测试用户
        test_user = User.objects.filter(username='test_login').first()
        if not test_user:
            test_user = User.objects.create_user(
                username='test_login',
                email='<EMAIL>',
                password='test_password_123',
                name='测试登录用户'
            )
            print("✅ 创建测试用户")
        else:
            print("✅ 使用现有测试用户")
        
        # 创建测试客户端
        client = Client()
        
        # 测试登录API
        login_url = reverse('login')  # 假设登录URL名称为'login'
        login_data = {
            'username': 'test_login',
            'password': 'test_password_123'
        }
        
        print(f"📡 发送登录请求到: {login_url}")
        response = client.post(
            login_url, 
            data=json.dumps(login_data),
            content_type='application/json'
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ 登录成功")
            print(f"  - 用户: {response_data.get('user', {}).get('username', 'N/A')}")
            print(f"  - 消息: {response_data.get('message', 'N/A')}")
            
            if 'access_token' in response_data:
                print("  - ✅ 获得访问令牌")
            if 'refresh_token' in response_data:
                print("  - ✅ 获得刷新令牌")
                
        elif response.status_code == 400:
            print("⚠️ 登录请求错误")
            try:
                error_data = response.json()
                print(f"  - 错误: {error_data}")
            except:
                print(f"  - 响应内容: {response.content}")
        else:
            print(f"❌ 登录失败，状态码: {response.status_code}")
            print(f"  - 响应内容: {response.content}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 登录API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auditlog_during_login():
    """测试登录过程中的auditlog记录"""
    print("\n🧪 测试登录过程中的auditlog记录...")
    
    try:
        from auditlog.models import LogEntry
        from apps.audit.models import BusinessOperationLog
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 记录登录前的日志数量
        auditlog_count_before = LogEntry.objects.count()
        business_log_count_before = BusinessOperationLog.objects.count()
        
        print(f"📊 登录前日志数量:")
        print(f"  - AuditLog: {auditlog_count_before}")
        print(f"  - BusinessOperationLog: {business_log_count_before}")
        
        # 执行登录测试
        login_success = test_login_api()
        
        # 记录登录后的日志数量
        auditlog_count_after = LogEntry.objects.count()
        business_log_count_after = BusinessOperationLog.objects.count()
        
        print(f"\n📊 登录后日志数量:")
        print(f"  - AuditLog: {auditlog_count_after}")
        print(f"  - BusinessOperationLog: {business_log_count_after}")
        
        # 检查是否有新的日志记录
        auditlog_new = auditlog_count_after - auditlog_count_before
        business_log_new = business_log_count_after - business_log_count_before
        
        print(f"\n📈 新增日志:")
        print(f"  - AuditLog: +{auditlog_new}")
        print(f"  - BusinessOperationLog: +{business_log_new}")
        
        # 检查最新的业务日志
        if business_log_new > 0:
            latest_business_logs = BusinessOperationLog.objects.order_by('-created_at')[:business_log_new]
            print(f"\n📋 最新业务日志:")
            for log in latest_business_logs:
                print(f"  - {log.action_type}: {log.target_name}")
        
        # 检查最新的审计日志
        if auditlog_new > 0:
            latest_audit_logs = LogEntry.objects.order_by('-timestamp')[:auditlog_new]
            print(f"\n📋 最新审计日志:")
            for log in latest_audit_logs:
                print(f"  - {log.action} on {log.object_repr}")
        
        return login_success
        
    except Exception as e:
        print(f"❌ 登录auditlog测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_axes_functionality():
    """测试django-axes功能"""
    print("\n🧪 测试django-axes功能...")
    
    try:
        from axes.models import AccessAttempt, AccessFailureLog, AccessLog
        
        print("📊 Axes模型统计:")
        print(f"  - AccessAttempt: {AccessAttempt.objects.count()}")
        print(f"  - AccessFailureLog: {AccessFailureLog.objects.count()}")
        print(f"  - AccessLog: {AccessLog.objects.count()}")
        
        # 检查最新的访问记录
        latest_attempts = AccessAttempt.objects.order_by('-attempt_time')[:3]
        if latest_attempts:
            print(f"\n📋 最新访问尝试:")
            for attempt in latest_attempts:
                print(f"  - {attempt.username} from {attempt.ip_address} at {attempt.attempt_time}")
        
        return True
        
    except Exception as e:
        print(f"❌ Axes功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试登录功能和auditlog")
    print("=" * 60)
    
    tests = [
        test_auditlog_during_login,
        test_axes_functionality,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            print("-" * 50)
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 登录功能和auditlog测试通过！")
        print("\n💡 修复总结:")
        print("  1. ✅ auditlog配置已修复")
        print("  2. ✅ 登录功能正常工作")
        print("  3. ✅ 审计日志正常记录")
        print("  4. ✅ django-axes正常工作")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
