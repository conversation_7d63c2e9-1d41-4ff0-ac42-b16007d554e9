from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views
from .menu_views import MenuListView

# 认证相关URL
auth_urlpatterns = [
    path("login/", views.LoginView.as_view(), name="login"),
    path("logout/", views.LogoutView.as_view(), name="logout"),
    path(
        "token/refresh/", views.CustomTokenRefreshView.as_view(), name="token_refresh"
    ),
    path(
        "password/change/", views.PasswordChangeView.as_view(), name="password_change"
    ),
    path("password/reset/", views.PasswordResetView.as_view(), name="password_reset"),
    path(
        "password/reset/confirm/<str:uidb64>/<str:token>/",
        views.PasswordResetConfirmView.as_view(),
        name="password_reset_confirm",
    ),
    path(
        "password/first-login-change/",
        views.FirstLoginPasswordChangeView.as_view(),
        name="first_login_password_change",
    ),
    path("mfa/setup/", views.MFASetupView.as_view(), name="mfa_setup"),
    path("mfa/qrcode/", views.MFAQRCodeView.as_view(), name="mfa_qrcode"),
    path("codes/", views.AccessCodesView.as_view(), name="access_codes"),
    path("menu/all/", MenuListView.as_view(), name="menu_list"),
]

# 用户管理相关URL
user_urlpatterns = [
    path("profile/", views.UserProfileView.as_view(), name="user_profile"),
    path("users/", views.UserListCreateView.as_view(), name="user_list_create"),
    path("users/<int:pk>/", views.UserDetailView.as_view(), name="user_detail"),
    path(
        "users/<int:pk>/reset-password/",
        views.UserResetPasswordView.as_view(),
        name="user_reset_password",
    ),
    path(
        "users/<int:pk>/toggle-active/",
        views.UserToggleActiveView.as_view(),
        name="user_toggle_active",
    ),
]

# 组织架构相关URL
org_urlpatterns = [
    path(
        "departments/",
        views.DepartmentListCreateView.as_view(),
        name="department_list_create",
    ),
    path(
        "departments/<int:pk>/",
        views.DepartmentDetailView.as_view(),
        name="department_detail",
    ),
    path(
        "departments/<int:pk>/users/",
        views.DepartmentUsersView.as_view(),
        name="department_users",
    ),
    path("roles/", views.RoleListCreateView.as_view(), name="role_list_create"),
    path("roles/<int:pk>/", views.RoleDetailView.as_view(), name="role_detail"),
    path(
        "roles/<int:pk>/users/",
        views.RoleUsersView.as_view(),
        name="role_users",
    ),
    # 用户组管理
    path("groups/", views.GroupListCreateView.as_view(), name="group_list_create"),
    path("groups/<int:pk>/", views.GroupDetailView.as_view(), name="group_detail"),
    path(
        "groups/<int:pk>/users/",
        views.GroupUsersView.as_view(),
        name="group_users",
    ),
    path(
        "groups/<int:pk>/add-users/",
        views.GroupAddUsersView.as_view(),
        name="group_add_users",
    ),
    path(
        "groups/<int:pk>/remove-users/",
        views.GroupRemoveUsersView.as_view(),
        name="group_remove_users",
    ),
]

urlpatterns = [
    path("", include(auth_urlpatterns)),
    path("", include(user_urlpatterns)),
    path("", include(org_urlpatterns)),
]
