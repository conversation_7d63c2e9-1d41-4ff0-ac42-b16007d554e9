from django.urls import path
from rest_framework.routers import Default<PERSON>outer
from . import views

# 业务操作日志相关URL
business_operation_urlpatterns = [
    path(
        "business-operations/",
        views.BusinessOperationLogListView.as_view(),
        name="business_operation_log_list",
    ),
]

# 密码访问日志相关URL
password_access_urlpatterns = [
    path(
        "password-access/",
        views.PasswordAccessLogListView.as_view(),
        name="password_access_log_list",
    ),
]

# 安全事件相关URL
security_event_urlpatterns = [
    path(
        "security-events/",
        views.SecurityEventListView.as_view(),
        name="security_event_list",
    ),
    path(
        "security-events/<uuid:pk>/",
        views.SecurityEventDetailView.as_view(),
        name="security_event_detail",
    ),
]

# 模型变更日志相关URL
model_change_urlpatterns = [
    path(
        "model-changes/",
        views.ModelChangeLogListView.as_view(),
        name="model_change_log_list",
    ),
    path(
        "model-changes/history/",
        views.model_change_history,
        name="model_change_history",
    ),
]

# 登录尝试记录相关URL
login_attempt_urlpatterns = [
    path(
        "login-attempts/",
        views.LoginAttemptListView.as_view(),
        name="login_attempt_list",
    ),
]

# Axes模型相关URL
axes_urlpatterns = [
    path(
        "axes/access-attempts/",
        views.LoginAttemptListView.as_view(),
        name="axes_access_attempts",
    ),
    path(
        "axes/access-failures/",
        views.AccessFailureLogListView.as_view(),
        name="axes_access_failures",
    ),
    path(
        "axes/access-logs/",
        views.AccessLogListView.as_view(),
        name="axes_access_logs",
    ),
]

# 审计轨迹相关URL
audit_trail_urlpatterns = [
    path(
        "users/<int:user_id>/audit-trail/",
        views.user_audit_trail,
        name="user_audit_trail",
    ),
]

# 统计和分析相关URL
stats_urlpatterns = [
    path(
        "statistics/",
        views.audit_statistics,
        name="audit_statistics",
    ),
]

# 兼容性URL（为了向后兼容）
compatibility_urlpatterns = [
    path(
        "operation-logs/",
        views.BusinessOperationLogListView.as_view(),
        name="operation_log_list",
    ),
    path(
        "access-logs/",
        views.PasswordAccessLogListView.as_view(),
        name="password_access_log_list",
    ),
]

urlpatterns = (
    business_operation_urlpatterns
    + password_access_urlpatterns
    + security_event_urlpatterns
    + model_change_urlpatterns
    + login_attempt_urlpatterns
    + axes_urlpatterns
    + audit_trail_urlpatterns
    + stats_urlpatterns
    + compatibility_urlpatterns
)

# 使用DRF路由器
router = DefaultRouter()
# 如果需要ViewSet，可以在这里注册

urlpatterns += router.urls
