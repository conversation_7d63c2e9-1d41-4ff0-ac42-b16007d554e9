"""
密码强度评估工具
"""
import re
import math
from typing import Dict, List, Tuple


class PasswordStrengthAnalyzer:
    """
    密码强度分析器
    """
    
    # 常见弱密码列表
    COMMON_PASSWORDS = {
        '123456', 'password', '123456789', '12345678', '12345', '1234567',
        'qwerty', 'abc123', 'password123', 'admin', 'letmein', 'welcome',
        '123123', 'password1', 'qwerty123', '111111', '000000', 'iloveyou',
        'dragon', 'monkey', 'sunshine', 'princess', 'football', 'charlie',
        'aa123456', 'donald', 'password0', 'qwertyuiop'
    }
    
    # 键盘模式
    KEYBOARD_PATTERNS = [
        'qwerty', 'asdf', 'zxcv', '1234', '4567', '7890',
        'qwertyuiop', 'asdfghjkl', 'zxcvbnm', '1234567890'
    ]
    
    @classmethod
    def analyze_password(cls, password: str) -> Dict:
        """
        分析密码强度
        
        Returns:
            Dict: 包含强度评分、等级、建议等信息
        """
        if not password:
            return {
                'score': 0,
                'strength': 'very_weak',
                'feedback': ['密码不能为空'],
                'entropy': 0,
                'crack_time': '立即'
            }
        
        # 计算各项指标
        length_score = cls._calculate_length_score(password)
        complexity_score = cls._calculate_complexity_score(password)
        pattern_penalty = cls._calculate_pattern_penalty(password)
        common_penalty = cls._calculate_common_penalty(password)
        
        # 计算总分
        total_score = length_score + complexity_score - pattern_penalty - common_penalty
        total_score = max(0, min(100, total_score))
        
        # 确定强度等级
        strength = cls._get_strength_level(total_score)
        
        # 生成反馈建议
        feedback = cls._generate_feedback(password, total_score)
        
        # 计算熵值
        entropy = cls._calculate_entropy(password)
        
        # 估算破解时间
        crack_time = cls._estimate_crack_time(entropy)
        
        return {
            'score': int(total_score),
            'strength': strength,
            'feedback': feedback,
            'entropy': round(entropy, 2),
            'crack_time': crack_time,
            'details': {
                'length_score': length_score,
                'complexity_score': complexity_score,
                'pattern_penalty': pattern_penalty,
                'common_penalty': common_penalty
            }
        }
    
    @classmethod
    def _calculate_length_score(cls, password: str) -> float:
        """计算长度得分"""
        length = len(password)
        if length < 6:
            return 0
        elif length < 8:
            return 10
        elif length < 12:
            return 20
        elif length < 16:
            return 30
        else:
            return 35
    
    @classmethod
    def _calculate_complexity_score(cls, password: str) -> float:
        """计算复杂度得分"""
        score = 0
        
        # 包含小写字母
        if re.search(r'[a-z]', password):
            score += 10
        
        # 包含大写字母
        if re.search(r'[A-Z]', password):
            score += 10
        
        # 包含数字
        if re.search(r'[0-9]', password):
            score += 10
        
        # 包含特殊字符
        if re.search(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]', password):
            score += 15
        
        # 字符种类多样性奖励
        unique_chars = len(set(password))
        if unique_chars > len(password) * 0.7:
            score += 10
        
        return score
    
    @classmethod
    def _calculate_pattern_penalty(cls, password: str) -> float:
        """计算模式惩罚"""
        penalty = 0
        password_lower = password.lower()
        
        # 检查键盘模式
        for pattern in cls.KEYBOARD_PATTERNS:
            if pattern in password_lower:
                penalty += 15
        
        # 检查重复字符
        for i in range(len(password) - 2):
            if password[i] == password[i+1] == password[i+2]:
                penalty += 10
                break
        
        # 检查连续数字
        if re.search(r'(012|123|234|345|456|567|678|789|890)', password):
            penalty += 10
        
        # 检查连续字母
        if re.search(r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)', password_lower):
            penalty += 10
        
        return penalty
    
    @classmethod
    def _calculate_common_penalty(cls, password: str) -> float:
        """计算常见密码惩罚"""
        if password.lower() in cls.COMMON_PASSWORDS:
            return 50
        
        # 检查是否包含常见密码片段
        for common in cls.COMMON_PASSWORDS:
            if len(common) > 4 and common in password.lower():
                return 25
        
        return 0
    
    @classmethod
    def _get_strength_level(cls, score: float) -> str:
        """根据分数确定强度等级"""
        if score < 20:
            return 'very_weak'
        elif score < 40:
            return 'weak'
        elif score < 60:
            return 'medium'
        elif score < 80:
            return 'strong'
        else:
            return 'very_strong'
    
    @classmethod
    def _generate_feedback(cls, password: str, score: float) -> List[str]:
        """生成改进建议"""
        feedback = []
        
        if len(password) < 8:
            feedback.append('密码长度至少应为8个字符')
        
        if not re.search(r'[a-z]', password):
            feedback.append('添加小写字母')
        
        if not re.search(r'[A-Z]', password):
            feedback.append('添加大写字母')
        
        if not re.search(r'[0-9]', password):
            feedback.append('添加数字')
        
        if not re.search(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]', password):
            feedback.append('添加特殊字符')
        
        if password.lower() in cls.COMMON_PASSWORDS:
            feedback.append('避免使用常见密码')
        
        if score >= 80:
            feedback.append('密码强度很好！')
        elif score >= 60:
            feedback.append('密码强度良好，可以进一步改进')
        
        return feedback
    
    @classmethod
    def _calculate_entropy(cls, password: str) -> float:
        """计算密码熵值"""
        if not password:
            return 0
        
        # 确定字符集大小
        charset_size = 0
        if re.search(r'[a-z]', password):
            charset_size += 26
        if re.search(r'[A-Z]', password):
            charset_size += 26
        if re.search(r'[0-9]', password):
            charset_size += 10
        if re.search(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]', password):
            charset_size += 32
        
        if charset_size == 0:
            return 0
        
        # 熵 = log2(字符集大小^密码长度)
        entropy = len(password) * math.log2(charset_size)
        return entropy
    
    @classmethod
    def _estimate_crack_time(cls, entropy: float) -> str:
        """估算破解时间"""
        if entropy < 20:
            return '立即'
        elif entropy < 30:
            return '几秒钟'
        elif entropy < 40:
            return '几分钟'
        elif entropy < 50:
            return '几小时'
        elif entropy < 60:
            return '几天'
        elif entropy < 70:
            return '几个月'
        elif entropy < 80:
            return '几年'
        else:
            return '几个世纪'


def analyze_password_strength(password: str) -> Dict:
    """
    分析密码强度的便捷函数
    """
    return PasswordStrengthAnalyzer.analyze_password(password)


def check_password_reuse(password: str, user_passwords: List[str]) -> bool:
    """
    检查密码是否重复使用
    """
    return password in user_passwords


def generate_password_report(passwords: List[str]) -> Dict:
    """
    生成密码安全报告
    """
    if not passwords:
        return {
            'total_passwords': 0,
            'weak_passwords': 0,
            'reused_passwords': 0,
            'average_strength': 0,
            'recommendations': []
        }
    
    weak_count = 0
    total_score = 0
    password_counts = {}
    reused_count = 0
    
    for password in passwords:
        analysis = analyze_password_strength(password)
        total_score += analysis['score']
        
        if analysis['strength'] in ['very_weak', 'weak']:
            weak_count += 1
        
        # 检查重复
        if password in password_counts:
            password_counts[password] += 1
            if password_counts[password] == 2:  # 第一次发现重复
                reused_count += 1
        else:
            password_counts[password] = 1
    
    average_strength = total_score / len(passwords)
    
    recommendations = []
    if weak_count > 0:
        recommendations.append(f'有{weak_count}个弱密码需要加强')
    if reused_count > 0:
        recommendations.append(f'有{reused_count}个密码被重复使用')
    if average_strength < 60:
        recommendations.append('整体密码强度偏低，建议提升')
    
    return {
        'total_passwords': len(passwords),
        'weak_passwords': weak_count,
        'reused_passwords': reused_count,
        'average_strength': round(average_strength, 2),
        'recommendations': recommendations
    }
