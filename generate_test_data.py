#!/usr/bin/env python
"""
生成大量测试数据脚本
"""
import os
import sys
import django
from django.conf import settings

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

import random
from faker import Faker
from django.contrib.auth import get_user_model
from apps.passwords.models import Category, PasswordEntry

User = get_user_model()
fake = Faker("zh_CN")

# 系统类型选择
SYSTEM_TYPES = [
    "linux",
    "windows",
    "database",
    "middleware",
    "network",
    "cloud",
    "container",
    "other",
]

# 环境类型选择
ENVIRONMENTS = ["dev", "test", "staging", "prod"]

# 数据库类型选择
DATABASE_TYPES = [
    "mysql",
    "postgresql",
    "oracle",
    "sqlserver",
    "mongodb",
    "redis",
    "elasticsearch",
    "other",
]

# 协议选择
PROTOCOLS = ["ssh", "rdp", "telnet", "http", "https", "ftp", "sftp", "other"]

# 项目名称
PROJECT_NAMES = [
    "用户管理系统",
    "订单管理系统",
    "支付系统",
    "库存管理系统",
    "客户关系管理",
    "财务管理系统",
    "人力资源系统",
    "项目管理系统",
    "数据分析平台",
    "监控告警系统",
    "日志分析系统",
    "配置管理系统",
    "API网关",
    "消息队列",
    "缓存服务",
    "文件存储服务",
    "邮件服务",
    "短信服务",
    "推送服务",
    "搜索服务",
]


def generate_ip():
    """生成随机IP地址"""
    return f"{random.randint(10, 192)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 254)}"


def generate_password():
    """生成随机密码"""
    passwords = [
        "Admin123!",
        "Password@123",
        "Qwerty123!",
        "Welcome@2024",
        "System123#",
        "Database@456",
        "Server2024!",
        "Secure@Pass",
        "MyPassword123",
        "StrongPass@2024",
        "AdminUser123!",
        "DevOps@2024",
    ]
    return random.choice(passwords)


def generate_username():
    """生成随机用户名"""
    usernames = [
        "admin",
        "root",
        "administrator",
        "user",
        "developer",
        "devops",
        "operator",
        "manager",
        "service",
        "system",
        "database",
        "webapp",
        "api",
        "monitor",
        "backup",
    ]
    return random.choice(usernames)


def create_test_data():
    """创建测试数据"""
    print("开始生成测试数据...")

    # 获取用户
    try:
        user = User.objects.get(username="admin")
    except User.DoesNotExist:
        print("创建admin用户...")
        user = User.objects.create_user(
            username="admin", email="<EMAIL>", password="admin123"
        )

    # 获取或创建分类
    categories = list(Category.objects.filter(user=user))
    if not categories:
        print("创建默认分类...")
        categories = [
            Category.objects.create(
                name="服务器", description="服务器相关密码", user=user
            ),
            Category.objects.create(
                name="数据库", description="数据库相关密码", user=user
            ),
            Category.objects.create(
                name="应用系统", description="应用系统相关密码", user=user
            ),
            Category.objects.create(
                name="网络设备", description="网络设备相关密码", user=user
            ),
        ]

    # 标签功能已移除，跳过标签创建

    print(f"开始生成1000条密码记录...")

    # 生成1000条密码记录
    for i in range(1000):
        if i % 100 == 0:
            print(f"已生成 {i} 条记录...")

        # 随机选择项目名称
        project_name = random.choice(PROJECT_NAMES)

        # 生成标题
        env = random.choice(ENVIRONMENTS)
        env_name = {"dev": "开发", "test": "测试", "staging": "预发布", "prod": "生产"}[
            env
        ]
        title = f"{project_name}-{env_name}环境"

        # 创建密码记录
        password_entry = PasswordEntry.objects.create(
            title=title,
            username=generate_username(),
            password=generate_password(),
            hostname=generate_ip(),
            port=random.choice([22, 3306, 5432, 1433, 6379, 80, 443, 8080, 9200]),
            system_type=random.choice(SYSTEM_TYPES),
            environment=env,
            database_type=(
                random.choice(DATABASE_TYPES) if random.random() > 0.5 else ""
            ),
            protocol=random.choice(PROTOCOLS),
            project_name=project_name,
            description=f"{title}的访问密码",
            category=random.choice(categories),
            owner=user,
            is_favorite=random.random() > 0.9,  # 10%概率设为收藏
            is_pinned=random.random() > 0.95,  # 5%概率设为置顶
        )

        # 标签功能已移除，跳过标签设置

    print("测试数据生成完成！")
    print(f"总共生成了 {PasswordEntry.objects.filter(owner=user).count()} 条密码记录")


if __name__ == "__main__":
    create_test_data()
