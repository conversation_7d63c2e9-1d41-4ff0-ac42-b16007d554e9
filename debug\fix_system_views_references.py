#!/usr/bin/env python
"""
修复apps/system/views.py中的模型引用
将OperationLog替换为BusinessOperationLog，将AccessLog替换为PasswordAccessLog
"""
import re

def fix_system_views():
    """修复system views中的模型引用"""
    file_path = 'apps/system/views.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换OperationLog为BusinessOperationLog
    content = re.sub(r'\bOperationLog\b', 'BusinessOperationLog', content)
    
    # 替换AccessLog为PasswordAccessLog，但需要注意字段差异
    # AccessLog在这里主要用于统计，需要检查字段是否匹配
    
    # 对于统计查询，我们需要特殊处理
    # 将 AccessLog.objects.filter(action="login", timestamp__gte=last_24h) 
    # 替换为使用axes模型或BusinessOperationLog
    
    # 替换登录统计查询
    content = re.sub(
        r'AccessLog\.objects\.filter\(\s*action="login",\s*timestamp__gte=([^)]+)\)',
        r'BusinessOperationLog.objects.filter(action_type="user_login", created_at__gte=\1)',
        content
    )
    
    # 替换失败登录统计查询
    content = re.sub(
        r'AccessLog\.objects\.filter\(\s*action="login_failed",\s*timestamp__gte=([^)]+)\)',
        r'BusinessOperationLog.objects.filter(action_type="user_login", result="failed", created_at__gte=\1)',
        content
    )
    
    # 替换日志清理中的timestamp为created_at
    content = re.sub(
        r'BusinessOperationLog\.objects\.filter\(timestamp__lt=([^)]+)\)',
        r'BusinessOperationLog.objects.filter(created_at__lt=\1)',
        content
    )
    
    # 替换AccessLog的清理操作为PasswordAccessLog
    content = re.sub(
        r'AccessLog\.objects\.filter\(timestamp__lt=([^)]+)\)',
        r'PasswordAccessLog.objects.filter(created_at__lt=\1)',
        content
    )
    
    # 替换其他AccessLog引用
    content = re.sub(r'\bAccessLog\b', 'PasswordAccessLog', content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已修复apps/system/views.py中的模型引用")

if __name__ == '__main__':
    fix_system_views()
