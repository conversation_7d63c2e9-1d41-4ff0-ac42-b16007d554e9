from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import connection
from django.core.mail import send_mail
from django.conf import settings
from django.core.cache import cache
from django.db.models import Count
from drf_spectacular.utils import extend_schema, extend_schema_view
import os
import sys
import django
import shutil
import subprocess
from datetime import datetime, timedelta

from .models import SystemSetting, EmailTemplate, BackupConfig
from .serializers import (
    SystemSettingSerializer,
    SystemSettingUpdateSerializer,
    EmailTemplateSerializer,
    BackupConfigSerializer,
    SystemStatusSerializer,
    SystemMaintenanceSerializer,
)
from apps.audit.models import BusinessOperationLog, PasswordAccessLog, SecurityEvent
from axes.models import AccessFailureLog
from apps.passwords.models import PasswordEntry
from apps.sharing.models import OneTimeLink

from utils.permissions import IsAdminOrSuperUser
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


@extend_schema(
    summary="获取系统设置列表",
    description="获取系统设置列表，非管理员只能查看公开设置",
    responses={200: SystemSettingSerializer(many=True)},
    tags=["系统设置"],
)
class SystemSettingListView(generics.ListAPIView):
    """系统设置列表视图"""

    serializer_class = SystemSettingSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取系统设置查询集"""
        queryset = SystemSetting.objects.all().order_by("category", "key")

        # 非管理员只能查看公开设置
        if not self.request.user.is_staff:
            queryset = queryset.filter(is_public=True)

        # 按分类过滤
        category = self.request.query_params.get("category")
        if category:
            queryset = queryset.filter(category=category)

        return queryset


class SystemSettingDetailView(generics.RetrieveUpdateAPIView):
    """系统设置详情视图"""

    queryset = SystemSetting.objects.all()
    serializer_class = SystemSettingSerializer
    permission_classes = [IsAdminOrSuperUser]
    lookup_field = "key"

    def perform_update(self, serializer):
        """更新系统设置"""
        instance = serializer.save(modified_by=self.request.user)

        # 记录操作日志
        BusinessOperationLog.objects.create(
            user=self.request.user,
            action_type="system_setting_update",
            target_type="system_setting",
            target_id=str(instance.id),
            target_name=instance.key,
            ip_address="127.0.0.1",  # 需要从request获取
            extra_data={
                "key": instance.key,
                "old_value": serializer.initial_data.get("value"),
                "new_value": instance.value,
            },
        )


class SystemSettingBatchUpdateView(APIView):
    """系统设置批量更新视图"""

    permission_classes = [IsAdminOrSuperUser]

    def post(self, request):
        """批量更新系统设置"""
        serializer = SystemSettingUpdateSerializer(data=request.data)

        if serializer.is_valid():
            try:
                updated_settings = serializer.update_settings(
                    serializer.validated_data, request.user
                )

                # 记录操作日志
                BusinessOperationLog.objects.create(
                    user=request.user,
                    action_type="system_setting_update",
                    target_type="system_setting",
                    target_id="batch_update",
                    target_name="batch_update",
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    extra_data={
                        "updated_count": len(updated_settings),
                        "settings": list(serializer.validated_data["settings"].keys()),
                    },
                )

                return Response(
                    {
                        "message": f"成功更新 {len(updated_settings)} 个设置项",
                        "updated_settings": [
                            SystemSettingSerializer(setting).data
                            for setting in updated_settings
                        ],
                    }
                )

            except Exception as e:
                logger.error(f"批量更新系统设置失败: {e}")
                return Response(
                    {"error": "更新失败，请检查设置项"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class EmailTemplateListCreateView(generics.ListCreateAPIView):
    """邮件模板列表和创建视图"""

    queryset = EmailTemplate.objects.all().order_by("template_type", "name")
    serializer_class = EmailTemplateSerializer
    permission_classes = [IsAdminOrSuperUser]

    def get_queryset(self):
        """获取邮件模板查询集"""
        queryset = super().get_queryset()

        # 按模板类型过滤
        template_type = self.request.query_params.get("template_type")
        if template_type:
            queryset = queryset.filter(template_type=template_type)

        # 按激活状态过滤
        is_active = self.request.query_params.get("is_active")
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == "true")

        return queryset

    def perform_create(self, serializer):
        """创建邮件模板"""
        instance = serializer.save(created_by=self.request.user)

        # 记录操作日志
        BusinessOperationLog.objects.create(
            user=self.request.user,
            action_type="create_email_template",
            target_type="email_template",
            target_id=str(instance.id),
            target_name=instance.name,
            ip_address=self.get_client_ip(self.request),
            user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
            extra_data={"name": instance.name, "template_type": instance.template_type},
        )

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class EmailTemplateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """邮件模板详情视图"""

    queryset = EmailTemplate.objects.all()
    serializer_class = EmailTemplateSerializer
    permission_classes = [IsAdminOrSuperUser]

    def perform_update(self, serializer):
        """更新邮件模板"""
        instance = serializer.save()

        # 记录操作日志
        BusinessOperationLog.objects.create(
            user=self.request.user,
            action_type="email_template_update",
            target_type="email_template",
            target_id=str(instance.id),
            target_name=instance.name,
            ip_address="127.0.0.1",  # 需要从request获取
            extra_data={"name": instance.name, "template_type": instance.template_type},
        )

    def perform_destroy(self, instance):
        """删除邮件模板"""
        # 记录操作日志
        BusinessOperationLog.objects.create(
            user=self.request.user,
            action_type="email_template_delete",
            target_type="email_template",
            target_id=str(instance.id),
            target_name=instance.name,
            ip_address="127.0.0.1",  # 需要从request获取
            extra_data={"name": instance.name, "template_type": instance.template_type},
        )

        instance.delete()


class BackupConfigListCreateView(generics.ListCreateAPIView):
    """备份配置列表和创建视图"""

    queryset = BackupConfig.objects.all().order_by("-created_at")
    serializer_class = BackupConfigSerializer
    permission_classes = [IsAdminOrSuperUser]

    def perform_create(self, serializer):
        """创建备份配置"""
        instance = serializer.save(created_by=self.request.user)

        # 记录操作日志
        BusinessOperationLog.objects.create(
            user=self.request.user,
            action_type="create_backup_config",
            target_type="backup_config",
            target_id=str(instance.id),
            target_name=instance.name,
            ip_address=self.get_client_ip(self.request),
            user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
            extra_data={
                "name": instance.name,
                "backup_type": instance.backup_type,
                "schedule_type": instance.schedule_type,
            },
        )

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class BackupConfigDetailView(generics.RetrieveUpdateDestroyAPIView):
    """备份配置详情视图"""

    queryset = BackupConfig.objects.all()
    serializer_class = BackupConfigSerializer
    permission_classes = [IsAdminOrSuperUser]

    def perform_update(self, serializer):
        """更新备份配置"""
        instance = serializer.save()

        # 记录操作日志
        BusinessOperationLog.objects.create(
            user=self.request.user,
            action_type="backup_config_update",
            target_type="backup_config",
            target_id=str(instance.id),
            target_name=instance.name,
            ip_address="127.0.0.1",  # 需要从request获取
            extra_data={
                "name": instance.name,
                "backup_type": instance.backup_type,
                "is_enabled": instance.is_enabled,
            },
        )

    def perform_destroy(self, instance):
        """删除备份配置"""
        # 记录操作日志
        BusinessOperationLog.objects.create(
            user=self.request.user,
            action_type="backup_config_delete",
            target_type="backup_config",
            target_id=str(instance.id),
            target_name=instance.name,
            ip_address="127.0.0.1",  # 需要从request获取
            extra_data={"name": instance.name, "backup_type": instance.backup_type},
        )

        instance.delete()


class SystemStatusView(APIView):
    """系统状态视图"""

    permission_classes = [IsAdminOrSuperUser]

    def get(self, request):
        """获取系统状态信息"""
        # 检查数据库状态
        database_status = self.check_database_status()

        # 检查Redis状态
        redis_status = self.check_redis_status()

        # 检查邮件服务状态
        email_status = self.check_email_status()

        # 检查备份状态
        backup_status = self.check_backup_status()

        # 获取系统信息
        system_info = self.get_system_info()

        # 获取统计信息
        stats = self.get_statistics()

        # 获取存储信息
        storage_info = self.get_storage_info()

        # 获取安全状态
        security_info = self.get_security_info()

        status_data = {
            "database_status": database_status,
            "redis_status": redis_status,
            "email_status": email_status,
            "backup_status": backup_status,
            **system_info,
            **stats,
            **storage_info,
            **security_info,
        }

        serializer = SystemStatusSerializer(status_data)
        return Response(serializer.data)

    def check_database_status(self):
        """检查数据库状态"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            return "healthy"
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return "error"

    def check_redis_status(self):
        """检查Redis状态"""
        try:
            cache.set("health_check", "ok", 10)
            result = cache.get("health_check")
            return "healthy" if result == "ok" else "error"
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            return "error"

    def check_email_status(self):
        """检查邮件服务状态"""
        try:
            # 检查邮件配置
            smtp_host = SystemSetting.get_value("smtp_host")
            smtp_port = SystemSetting.get_value("smtp_port")

            if not smtp_host or not smtp_port:
                return "not_configured"

            return "configured"
        except Exception as e:
            logger.error(f"邮件服务检查失败: {e}")
            return "error"

    def check_backup_status(self):
        """检查备份状态"""
        try:
            active_configs = BackupConfig.objects.filter(is_enabled=True).count()
            if active_configs == 0:
                return "not_configured"

            # 检查最近的备份
            recent_backup = BackupConfig.objects.filter(
                is_enabled=True,
                last_backup_time__gte=timezone.now() - timedelta(days=1),
            ).exists()

            return "healthy" if recent_backup else "warning"
        except Exception as e:
            logger.error(f"备份状态检查失败: {e}")
            return "error"

    def get_system_info(self):
        """获取系统信息"""
        return {
            "system_version": "1.0.0",  # 从配置或版本文件读取
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "django_version": django.get_version(),
        }

    def get_statistics(self):
        """获取统计信息"""
        now = timezone.now()
        last_24h = now - timedelta(hours=24)

        return {
            "total_users": User.objects.count(),
            "total_passwords": PasswordEntry.objects.count(),
            "total_shares": OneTimeLink.objects.filter(status="active").count(),
            "recent_logins": BusinessOperationLog.objects.filter(
                action_type="user_login", created_at__gte=last_24h
            ).count(),
            "recent_operations": BusinessOperationLog.objects.filter(
                created_at__gte=last_24h
            ).count(),
        }

    def get_storage_info(self):
        """获取存储信息"""
        try:
            # 获取磁盘使用情况
            disk_usage = shutil.disk_usage("/")
            disk_info = {
                "total": disk_usage.total,
                "used": disk_usage.used,
                "free": disk_usage.free,
                "percent": (disk_usage.used / disk_usage.total) * 100,
            }

            # 获取数据库大小（PostgreSQL示例）
            try:
                with connection.cursor() as cursor:
                    cursor.execute(
                        "SELECT pg_size_pretty(pg_database_size(current_database()))"
                    )
                    db_size = cursor.fetchone()[0]
            except Exception:
                db_size = "Unknown"

            return {"disk_usage": disk_info, "database_size": db_size}
        except Exception as e:
            logger.error(f"获取存储信息失败: {e}")
            return {"disk_usage": {}, "database_size": "Unknown"}

    def get_security_info(self):
        """获取安全状态信息"""
        now = timezone.now()
        last_24h = now - timedelta(hours=24)

        return {
            "security_alerts": SecurityEvent.objects.filter(
                resolved=False, created_at__gte=last_24h
            ).count(),
            "failed_login_attempts": AccessFailureLog.objects.filter(
                attempt_time__gte=last_24h
            ).count(),
        }


class SystemMaintenanceView(APIView):
    """系统维护视图"""

    permission_classes = [IsAdminOrSuperUser]

    def post(self, request):
        """执行系统维护操作"""
        serializer = SystemMaintenanceSerializer(data=request.data)

        if serializer.is_valid():
            action = serializer.validated_data["action"]
            parameters = serializer.validated_data.get("parameters", {})

            try:
                result = self.execute_maintenance_action(
                    action, parameters, request.user
                )

                # 记录操作日志
                BusinessOperationLog.objects.create(
                    user=request.user,
                    action_type=f"system_maintenance_{action}",
                    target_type="system",
                    target_id="maintenance",
                    target_name=action,
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    extra_data={
                        "action": action,
                        "parameters": parameters,
                        "result": result,
                    },
                )

                return Response({"message": "维护操作执行成功", "result": result})

            except Exception as e:
                logger.error(f"系统维护操作失败: {e}")
                return Response(
                    {"error": f"维护操作失败: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def execute_maintenance_action(self, action, parameters, user):
        """执行维护操作"""
        if action == "clear_logs":
            return self.clear_logs(parameters)
        elif action == "optimize_database":
            return self.optimize_database()
        elif action == "clear_cache":
            return self.clear_cache()
        elif action == "backup_now":
            return self.backup_now(parameters)
        elif action == "test_email":
            return self.test_email(parameters)
        else:
            raise ValueError(f"未知的维护操作: {action}")

    def clear_logs(self, parameters):
        """清理日志"""
        days = parameters.get("days", 30)
        cutoff_date = timezone.now() - timedelta(days=days)

        # 清理操作日志
        operation_count = BusinessOperationLog.objects.filter(
            created_at__lt=cutoff_date
        ).count()
        BusinessOperationLog.objects.filter(created_at__lt=cutoff_date).delete()

        # 清理访问日志
        access_count = PasswordAccessLog.objects.filter(
            created_at__lt=cutoff_date
        ).count()
        PasswordAccessLog.objects.filter(created_at__lt=cutoff_date).delete()

        return {
            "operation_logs_deleted": operation_count,
            "access_logs_deleted": access_count,
            "cutoff_date": cutoff_date.isoformat(),
        }

    def optimize_database(self):
        """优化数据库"""
        try:
            with connection.cursor() as cursor:
                # PostgreSQL优化示例
                cursor.execute("VACUUM ANALYZE")
            return {"message": "数据库优化完成"}
        except Exception as e:
            raise Exception(f"数据库优化失败: {e}")

    def clear_cache(self):
        """清理缓存"""
        try:
            cache.clear()
            return {"message": "缓存清理完成"}
        except Exception as e:
            raise Exception(f"缓存清理失败: {e}")

    def backup_now(self, parameters):
        """立即备份"""
        # 这里应该调用备份服务
        # 简化实现，实际应该有专门的备份服务
        return {"message": "备份任务已启动"}

    def test_email(self, parameters):
        """测试邮件发送"""
        email = parameters["email"]

        try:
            send_mail(
                subject="Locker 系统邮件测试",
                message="这是一封测试邮件，如果您收到此邮件，说明邮件服务配置正确。",
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
                fail_silently=False,
            )
            return {"message": f"测试邮件已发送到 {email}"}
        except Exception as e:
            raise Exception(f"邮件发送失败: {e}")
