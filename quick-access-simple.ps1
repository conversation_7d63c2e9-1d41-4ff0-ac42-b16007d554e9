# Backend Agent Quick Access Tool - Simplified Version

Write-Host "=== Backend Agent Quick Access Tool ===" -ForegroundColor Green
Write-Host "Current Location: $(Get-Location)" -ForegroundColor Cyan

# Get project root directory
$projectRoot = Split-Path -Parent $PSScriptRoot
Write-Host "Project Root: $projectRoot" -ForegroundColor Yellow

# Quick Commands
function Go-ProjectRoot {
    Set-Location $projectRoot
    Write-Host "Switched to project root: $(Get-Location)" -ForegroundColor Green
}

function Show-SharedResources {
    Write-Host "`nShared Resources:" -ForegroundColor Yellow
    if (Test-Path "$projectRoot/shared-resources") {
        Get-ChildItem "$projectRoot/shared-resources" | ForEach-Object {
            Write-Host "  - $($_.Name)" -ForegroundColor Gray
        }
    } else {
        Write-Host "  No shared resources found" -ForegroundColor Red
    }
}

function Show-ApiIssues {
    Write-Host "`nAPI Issues Status:" -ForegroundColor Red
    
    $openPath = "$projectRoot/api-issues/open"
    $inProgressPath = "$projectRoot/api-issues/in-progress"
    $resolvedPath = "$projectRoot/api-issues/resolved"
    
    $openCount = if (Test-Path $openPath) { (Get-ChildItem $openPath).Count } else { 0 }
    $inProgressCount = if (Test-Path $inProgressPath) { (Get-ChildItem $inProgressPath).Count } else { 0 }
    $resolvedCount = if (Test-Path $resolvedPath) { (Get-ChildItem $resolvedPath).Count } else { 0 }
    
    Write-Host "  Open Issues: $openCount" -ForegroundColor Red
    Write-Host "  In Progress: $inProgressCount" -ForegroundColor Yellow
    Write-Host "  Resolved: $resolvedCount" -ForegroundColor Green
    
    if ($openCount -gt 0) {
        Write-Host "`n  Open Issues:" -ForegroundColor Red
        Get-ChildItem $openPath -ErrorAction SilentlyContinue | ForEach-Object {
            Write-Host "    - $($_.Name)" -ForegroundColor Gray
        }
    }
    
    if ($inProgressCount -gt 0) {
        Write-Host "`n  In Progress Issues:" -ForegroundColor Yellow
        Get-ChildItem $inProgressPath -ErrorAction SilentlyContinue | ForEach-Object {
            Write-Host "    - $($_.Name)" -ForegroundColor Gray
        }
    }
}

function Show-ApiDocs {
    Write-Host "`nAPI Documentation:" -ForegroundColor Cyan
    Write-Host "  Swagger UI: http://localhost:8001/api/docs/" -ForegroundColor Blue
    Write-Host "  ReDoc: http://localhost:8001/api/redoc/" -ForegroundColor Blue
    Write-Host "  Schema: http://localhost:8001/api/schema/" -ForegroundColor Blue
    
    $apiDocsPath = "$projectRoot/api-docs"
    if (Test-Path $apiDocsPath) {
        Write-Host "`n  Local Documentation:" -ForegroundColor Cyan
        Get-ChildItem $apiDocsPath -ErrorAction SilentlyContinue | ForEach-Object {
            Write-Host "    - $($_.Name)" -ForegroundColor Gray
        }
    }
}

function Start-HealthCheck {
    Write-Host "`nRunning API Health Check..." -ForegroundColor Blue
    & "$projectRoot/scripts/api-health-check.ps1"
}

function Start-BackendService {
    Write-Host "`nStarting Backend Service..." -ForegroundColor Green
    
    # Check virtual environment
    if (Test-Path "venv/Scripts/Activate.ps1") {
        Write-Host "Activating virtual environment..." -ForegroundColor Yellow
        & "venv/Scripts/Activate.ps1"
    }
    
    # Start Django server
    Write-Host "Starting Django server..." -ForegroundColor Yellow
    python manage.py runserver 0.0.0.0:8001
}

function Start-Tests {
    Write-Host "`nRunning tests..." -ForegroundColor Magenta
    
    # Check virtual environment
    if (Test-Path "venv/Scripts/Activate.ps1") {
        & "venv/Scripts/Activate.ps1"
    }
    
    # Run Django tests
    python manage.py test
}

function Show-Help {
    Write-Host "`n=== Backend Agent Help ===" -ForegroundColor Magenta
    Write-Host ""
    Write-Host "Quick Commands:" -ForegroundColor Magenta
    Write-Host "  root          - Switch to project root directory" -ForegroundColor Green
    Write-Host "  shared        - Show shared resources" -ForegroundColor Yellow
    Write-Host "  issues        - Show API issues status" -ForegroundColor Red
    Write-Host "  docs          - Show API documentation" -ForegroundColor Cyan
    Write-Host "  health        - Run API health check" -ForegroundColor Blue
    Write-Host "  start         - Start backend service" -ForegroundColor Green
    Write-Host "  test          - Run tests" -ForegroundColor Magenta
    Write-Host "  help          - Show this help" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Common Backend Operations:" -ForegroundColor Green
    Write-Host "  python manage.py runserver 0.0.0.0:8001  - Start development server" -ForegroundColor Gray
    Write-Host "  python manage.py makemigrations           - Create database migrations" -ForegroundColor Gray
    Write-Host "  python manage.py migrate                  - Apply database migrations" -ForegroundColor Gray
    Write-Host "  python manage.py test                     - Run tests" -ForegroundColor Gray
    Write-Host "  python manage.py shell                    - Enter Django shell" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Collaboration:" -ForegroundColor Yellow
    Write-Host "  ../api-issues/templates/                  - Issue templates" -ForegroundColor Gray
    Write-Host "  ../shared-resources/                      - Shared resources" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Issue Processing:" -ForegroundColor Red
    Write-Host "  1. Check issues command to see new reports" -ForegroundColor Gray
    Write-Host "  2. Move issue from open/ to in-progress/" -ForegroundColor Gray
    Write-Host "  3. Create response using backend template" -ForegroundColor Gray
    Write-Host "  4. Fix the issue and test" -ForegroundColor Gray
    Write-Host "  5. Notify frontend agent for verification" -ForegroundColor Gray
}

# Create aliases
Set-Alias -Name root -Value Go-ProjectRoot
Set-Alias -Name shared -Value Show-SharedResources
Set-Alias -Name issues -Value Show-ApiIssues
Set-Alias -Name docs -Value Show-ApiDocs
Set-Alias -Name health -Value Start-HealthCheck
Set-Alias -Name start -Value Start-BackendService
Set-Alias -Name test -Value Start-Tests
Set-Alias -Name help -Value Show-Help

# Show welcome message
Show-Help

Write-Host "`nTip: Type command names to execute quickly, e.g.: root, issues, start" -ForegroundColor Green
Write-Host "Backend Agent Quick Tool Loaded!" -ForegroundColor Green
