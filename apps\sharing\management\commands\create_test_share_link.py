from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import secrets

from apps.sharing.models import OneTimeLink
from apps.passwords.models import PasswordEntry

User = get_user_model()

class Command(BaseCommand):
    help = '创建测试分享链接'

    def handle(self, *args, **options):
        self.stdout.write("检查分享链接数据...")
        
        # 检查现有的分享链接
        links = OneTimeLink.objects.all()
        self.stdout.write(f"现有分享链接数量: {links.count()}")
        
        if links.exists():
            for link in links:
                self.stdout.write(f"- Token: {link.token}")
                self.stdout.write(f"  密码条目: {link.password_entry.title}")
                self.stdout.write(f"  状态: {link.status}")
                self.stdout.write(f"  过期时间: {link.expires_at}")
                self.stdout.write(f"  是否有效: {link.is_valid}")
        else:
            self.stdout.write("没有分享链接，创建测试数据...")
            
            # 获取第一个用户
            user = User.objects.first()
            if not user:
                self.stdout.write("没有用户，创建测试用户...")
                user = User.objects.create_user(
                    username='testuser',
                    email='<EMAIL>',
                    password='testpass123'
                )
            
            # 获取第一个密码条目
            password_entry = PasswordEntry.objects.first()
            if not password_entry:
                self.stdout.write("没有密码条目，创建测试密码条目...")
                password_entry = PasswordEntry.objects.create(
                    title='测试密码',
                    username='testuser',
                    password='testpass123',
                    url='https://example.com',
                    created_by=user
                )
            
            # 创建测试分享链接
            token = secrets.token_urlsafe(8)
            expires_at = timezone.now() + timedelta(days=7)
            
            link = OneTimeLink.objects.create(
                token=token,
                password_entry=password_entry,
                created_by=user,
                expires_at=expires_at,
                status='active'
            )
            
            self.stdout.write(f"创建了测试分享链接:")
            self.stdout.write(f"- Token: {link.token}")
            self.stdout.write(f"- 密码条目: {link.password_entry.title}")
            self.stdout.write(f"- 过期时间: {link.expires_at}")
            self.stdout.write(f"- 访问URL: http://localhost:5666/share/{link.token}")
            
            self.stdout.write(self.style.SUCCESS('测试数据创建成功！'))
