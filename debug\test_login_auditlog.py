#!/usr/bin/env python
"""
测试登录过程中的auditlog问题
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def test_auditlog_fields():
    """测试auditlog字段访问"""
    print("🧪 测试auditlog字段访问...")
    
    try:
        from auditlog.models import LogEntry
        from django.db import connection
        
        # 检查数据库表结构
        cursor = connection.cursor()
        cursor.execute("PRAGMA table_info(auditlog_logentry)")
        columns = cursor.fetchall()
        
        print("📊 auditlog_logentry 表字段:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # 检查模型字段
        print("\n📊 LogEntry 模型字段:")
        for field in LogEntry._meta.fields:
            print(f"  - {field.name}: {field.__class__.__name__}")
        
        # 尝试查询所有字段
        print("\n🧪 测试字段查询...")
        if LogEntry.objects.exists():
            entry = LogEntry.objects.first()
            
            # 测试每个字段
            test_fields = [
                'id', 'content_type', 'object_pk', 'object_id', 
                'object_repr', 'action', 'changes', 'timestamp', 
                'actor', 'remote_addr', 'additional_data'
            ]
            
            for field_name in test_fields:
                try:
                    value = getattr(entry, field_name, 'NOT_FOUND')
                    print(f"  ✅ {field_name}: {type(value).__name__}")
                except Exception as e:
                    print(f"  ❌ {field_name}: {e}")
            
            # 特别测试可能有问题的字段
            try:
                # 尝试访问changes_text字段（如果存在）
                cursor.execute("SELECT changes_text FROM auditlog_logentry WHERE id = ?", [entry.id])
                result = cursor.fetchone()
                print(f"  ✅ changes_text (直接SQL): {type(result[0]).__name__ if result and result[0] else 'NULL'}")
            except Exception as e:
                print(f"  ❌ changes_text (直接SQL): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ auditlog字段测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_simulation():
    """模拟登录过程"""
    print("\n🧪 模拟登录过程...")
    
    try:
        from django.contrib.auth import get_user_model, authenticate
        from django.test import RequestFactory
        from apps.users.views import LoginView
        from apps.audit.utils import log_business_operation
        
        User = get_user_model()
        
        # 创建测试用户（如果不存在）
        test_username = "test_login_user"
        test_password = "test_password_123"
        
        user, created = User.objects.get_or_create(
            username=test_username,
            defaults={
                'email': '<EMAIL>',
                'name': '测试登录用户',
                'is_active': True
            }
        )
        
        if created:
            user.set_password(test_password)
            user.save()
            print(f"✅ 创建测试用户: {test_username}")
        else:
            print(f"✅ 使用现有测试用户: {test_username}")
        
        # 创建模拟请求
        factory = RequestFactory()
        request = factory.post('/api/auth/login/', {
            'username': test_username,
            'password': test_password
        })
        request.META['HTTP_USER_AGENT'] = 'Test Agent'
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        
        # 测试认证
        print("🧪 测试用户认证...")
        auth_user = authenticate(request=request, username=test_username, password=test_password)
        if auth_user:
            print(f"✅ 用户认证成功: {auth_user.username}")
        else:
            print("❌ 用户认证失败")
            return False
        
        # 测试业务日志记录
        print("🧪 测试业务日志记录...")
        try:
            log_business_operation(
                user=user,
                action_type="test_login",
                description=f"测试用户 {user.username} 登录",
                target_type="user",
                target_id=str(user.id),
                target_name=user.username,
                request=request,
                extra_data={"test": True}
            )
            print("✅ 业务日志记录成功")
        except Exception as e:
            print(f"❌ 业务日志记录失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试auditlog自动记录
        print("🧪 测试auditlog自动记录...")
        try:
            # 修改用户信息触发auditlog
            original_name = user.name
            user.name = f"更新的用户名_{user.id}"
            user.save()
            print("✅ auditlog自动记录成功")
            
            # 恢复原始名称
            user.name = original_name
            user.save()
        except Exception as e:
            print(f"❌ auditlog自动记录失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 登录模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auditlog_queries():
    """测试各种auditlog查询"""
    print("\n🧪 测试auditlog查询...")
    
    try:
        from auditlog.models import LogEntry
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 测试基本查询
        print("📊 基本查询统计:")
        total_entries = LogEntry.objects.count()
        print(f"  - 总记录数: {total_entries}")
        
        # 测试按用户查询
        users_with_logs = LogEntry.objects.values('actor').distinct().count()
        print(f"  - 有日志的用户数: {users_with_logs}")
        
        # 测试按操作类型查询
        actions = LogEntry.objects.values('action').distinct()
        print(f"  - 操作类型: {[a['action'] for a in actions]}")
        
        # 测试复杂查询
        print("🧪 测试复杂查询...")
        recent_entries = LogEntry.objects.order_by('-timestamp')[:5]
        for entry in recent_entries:
            print(f"  - {entry.timestamp}: {entry.action} on {entry.object_repr}")
        
        # 测试可能有问题的查询
        print("🧪 测试可能有问题的查询...")
        try:
            # 这种查询可能会触发changes_text字段访问
            entries_with_changes = LogEntry.objects.exclude(changes__isnull=True)[:5]
            for entry in entries_with_changes:
                print(f"  - 变更记录: {entry.object_repr} - {len(str(entry.changes))} chars")
        except Exception as e:
            print(f"  ❌ 变更查询失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ auditlog查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试登录过程中的auditlog问题")
    print("=" * 60)
    
    tests = [
        test_auditlog_fields,
        test_login_simulation,
        test_auditlog_queries,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            print("-" * 50)
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！auditlog功能正常")
        print("\n💡 建议:")
        print("  1. 检查生产环境的auditlog版本")
        print("  2. 确认数据库迁移已正确应用")
        print("  3. 检查是否有自定义的auditlog查询")
    else:
        print("⚠️ 部分测试失败，需要进一步调查")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
