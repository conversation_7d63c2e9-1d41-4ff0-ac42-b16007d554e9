#!/usr/bin/env python
"""
修复apps/passwords/views.py中的OperationLog引用
将OperationLog替换为BusinessOperationLog，并修复字段名
"""
import re

def fix_passwords_views():
    """修复passwords views中的模型引用"""
    file_path = 'apps/passwords/views.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换OperationLog为BusinessOperationLog
    content = re.sub(r'\bOperationLog\b', 'BusinessOperationLog', content)
    
    # 修复字段名不匹配的问题
    # action -> action_type
    content = re.sub(
        r'BusinessOperationLog\.objects\.create\(\s*([^}]+?)action="([^"]+)"',
        r'BusinessOperationLog.objects.create(\1action_type="\2"',
        content,
        flags=re.DOTALL
    )
    
    # resource_type -> target_type
    content = re.sub(
        r'resource_type="([^"]+)"',
        r'target_type="\1"',
        content
    )
    
    # resource_id -> target_id
    content = re.sub(
        r'resource_id=([^,\n]+)',
        r'target_id=\1',
        content
    )
    
    # details -> extra_data (需要转换为字典格式)
    # 这个比较复杂，需要手动处理
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已修复apps/passwords/views.py中的模型引用")

if __name__ == '__main__':
    fix_passwords_views()
