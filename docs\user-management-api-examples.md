# 用户管理CRUD API使用示例

本文档提供了用户管理系统中User、Group、Department、Team和Role模型的完整CRUD API使用示例。

## API基础信息

- **基础URL**: `/api/users/crud/`
- **认证方式**: JWT Token
- **请求头**: `Authorization: Bearer <token>`
- **内容类型**: `application/json`

## 1. 用户管理 (Users)

### 1.1 获取用户列表

```bash
GET /api/users/crud/users/
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `page_size`: 每页大小 (默认: 20)
- `search`: 搜索关键词 (搜索用户名、邮箱、姓名)
- `department`: 部门ID过滤
- `team`: 团队ID过滤
- `role`: 角色ID过滤
- `is_active`: 是否激活 (true/false)
- `ordering`: 排序字段 (username, email, name, date_joined, created_at)

**示例请求**:
```bash
curl -X GET "http://localhost:8001/api/users/crud/users/?page=1&page_size=10&search=admin&department=1" \
  -H "Authorization: Bearer <token>"
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取用户列表成功",
  "data": [
    {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "name": "管理员",
      "first_name": "管理",
      "last_name": "员",
      "department": 1,
      "department_name": "技术部",
      "team": 1,
      "team_name": "后端团队",
      "role": 1,
      "role_name": "管理员",
      "groups_count": 2,
      "is_active": true,
      "is_staff": true,
      "date_joined": "2024-01-01T00:00:00Z",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 10,
  "timestamp": "2024-08-04T15:30:00Z"
}
```

### 1.2 获取用户详情

```bash
GET /api/users/crud/users/{id}/
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取用户详情成功",
  "data": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "name": "管理员",
    "first_name": "管理",
    "last_name": "员",
    "phone": "13800138000",
    "avatar": null,
    "department": 1,
    "department_name": "技术部",
    "team": 1,
    "team_name": "后端团队",
    "role": 1,
    "role_name": "管理员",
    "groups": [
      {"id": 1, "name": "管理组"},
      {"id": 2, "name": "开发组"}
    ],
    "home_path": "/dashboard",
    "is_mfa_enabled": false,
    "last_password_change": "2024-01-01T00:00:00Z",
    "is_active": true,
    "is_staff": true,
    "is_superuser": false,
    "date_joined": "2024-01-01T00:00:00Z",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "timestamp": "2024-08-04T15:30:00Z"
}
```

### 1.3 创建用户

```bash
POST /api/users/crud/users/
```

**请求体**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "name": "新用户",
  "password": "SecurePassword123!",
  "password_confirm": "SecurePassword123!",
  "first_name": "新",
  "last_name": "用户",
  "phone": "13800138001",
  "department": 1,
  "team": 1,
  "role": 2,
  "groups": [1, 2]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "创建用户成功",
  "data": {
    "id": 2,
    "username": "newuser",
    "email": "<EMAIL>",
    "name": "新用户",
    // ... 其他字段
  },
  "timestamp": "2024-08-04T15:30:00Z"
}
```

### 1.4 更新用户

```bash
PATCH /api/users/crud/users/{id}/
```

**请求体**:
```json
{
  "name": "更新后的姓名",
  "phone": "13800138002",
  "department": 2,
  "groups": [1, 3]
}
```

### 1.5 删除用户（软删除）

```bash
DELETE /api/users/crud/users/{id}/
```

**响应示例**:
```json
{
  "success": true,
  "message": "删除用户成功",
  "timestamp": "2024-08-04T15:30:00Z"
}
```

### 1.6 重置用户密码

```bash
POST /api/users/crud/users/{id}/reset_password/
```

**请求体**:
```json
{
  "new_password": "NewSecurePassword123!"
}
```

### 1.7 切换用户激活状态

```bash
POST /api/users/crud/users/{id}/toggle_active/
```

**响应示例**:
```json
{
  "success": true,
  "message": "激活用户成功",
  "data": {
    "is_active": true
  },
  "timestamp": "2024-08-04T15:30:00Z"
}
```

## 2. 部门管理 (Departments)

### 2.1 获取部门列表

```bash
GET /api/users/crud/departments/
```

**查询参数**:
- `parent`: 上级部门ID过滤
- `search`: 搜索部门名称或描述

**响应示例**:
```json
{
  "success": true,
  "message": "获取部门列表成功",
  "data": [
    {
      "id": 1,
      "name": "技术部",
      "description": "技术开发部门",
      "parent": null,
      "parent_name": null,
      "teams_count": 3,
      "users_count": 15,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "timestamp": "2024-08-04T15:30:00Z"
}
```

### 2.2 获取部门详情

```bash
GET /api/users/crud/departments/{id}/
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取部门详情成功",
  "data": {
    "id": 1,
    "name": "技术部",
    "description": "技术开发部门",
    "parent": null,
    "parent_name": null,
    "teams": [
      {"id": 1, "name": "后端团队"},
      {"id": 2, "name": "前端团队"}
    ],
    "users": [
      {"id": 1, "username": "admin", "name": "管理员"},
      {"id": 2, "username": "dev1", "name": "开发者1"}
    ],
    "children": [
      {"id": 2, "name": "技术一部"},
      {"id": 3, "name": "技术二部"}
    ],
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "timestamp": "2024-08-04T15:30:00Z"
}
```

### 2.3 创建部门

```bash
POST /api/users/crud/departments/
```

**请求体**:
```json
{
  "name": "产品部",
  "description": "产品设计部门",
  "parent": 1
}
```

### 2.4 获取部门下的用户

```bash
GET /api/users/crud/departments/{id}/users/
```

### 2.5 获取部门下的团队

```bash
GET /api/users/crud/departments/{id}/teams/
```

## 3. 团队管理 (Teams)

### 3.1 创建团队

```bash
POST /api/users/crud/teams/
```

**请求体**:
```json
{
  "name": "前端团队",
  "description": "前端开发团队",
  "department": 1
}
```

### 3.2 获取团队下的用户

```bash
GET /api/users/crud/teams/{id}/users/
```

## 4. 角色管理 (Roles)

### 4.1 创建角色

```bash
POST /api/users/crud/roles/
```

**请求体**:
```json
{
  "name": "developer",
  "description": "开发人员角色",
  "permissions": ["view_password", "create_password", "edit_password"]
}
```

### 4.2 获取角色下的用户

```bash
GET /api/users/crud/roles/{id}/users/
```

## 5. 用户组管理 (Groups)

### 5.1 创建用户组

```bash
POST /api/users/crud/groups/
```

**请求体**:
```json
{
  "name": "开发组",
  "users": [1, 2, 3],
  "permissions": [1, 2, 3]
}
```

### 5.2 向用户组添加用户

```bash
POST /api/users/crud/groups/{id}/add_users/
```

**请求体**:
```json
{
  "user_ids": [4, 5, 6]
}
```

### 5.3 从用户组移除用户

```bash
POST /api/users/crud/groups/{id}/remove_users/
```

**请求体**:
```json
{
  "user_ids": [4, 5]
}
```

## 错误处理

### 常见错误响应

**验证错误 (400)**:
```json
{
  "success": false,
  "message": "数据验证失败",
  "code": "VALIDATION_ERROR",
  "errors": {
    "username": ["该用户名已存在"],
    "email": ["请输入有效的邮箱地址"]
  },
  "timestamp": "2024-08-04T15:30:00Z"
}
```

**权限不足 (403)**:
```json
{
  "success": false,
  "message": "权限不足",
  "code": "INSUFFICIENT_PERMISSION",
  "timestamp": "2024-08-04T15:30:00Z"
}
```

**资源不存在 (404)**:
```json
{
  "success": false,
  "message": "用户不存在",
  "code": "RESOURCE_NOT_FOUND",
  "timestamp": "2024-08-04T15:30:00Z"
}
```

## 注意事项

1. **权限要求**: 大部分写操作需要管理员权限 (`is_staff=True`)
2. **软删除**: 用户删除采用软删除方式，设置 `is_active=False`
3. **关系处理**: 删除部门/团队/角色前需确保没有关联的用户
4. **密码安全**: 创建用户时密码需要满足强度要求
5. **分页**: 列表接口默认分页，可通过 `page_size` 参数调整每页大小
6. **搜索**: 支持模糊搜索用户名、邮箱、姓名等字段
7. **排序**: 支持多字段排序，使用 `ordering` 参数
