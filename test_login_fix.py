#!/usr/bin/env python
"""
测试登录修复是否有效
"""
import os
import sys
import django
import requests
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def test_login_api():
    """测试登录API是否正常工作"""
    print("🧪 测试登录API修复效果...")
    
    base_url = 'http://localhost:8001'
    login_url = f'{base_url}/api/auth/login/'
    
    # 测试数据
    test_cases = [
        {
            'name': '错误的用户名和密码',
            'data': {'username': 'nonexistent', 'password': 'wrongpass'},
            'expected_status': 400,
            'should_succeed': False
        },
        {
            'name': '空用户名',
            'data': {'username': '', 'password': 'somepass'},
            'expected_status': 400,
            'should_succeed': False
        },
        {
            'name': '空密码',
            'data': {'username': 'admin', 'password': ''},
            'expected_status': 400,
            'should_succeed': False
        }
    ]
    
    print("=" * 60)
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试: {test_case['name']}")
        print(f"   数据: {test_case['data']}")
        
        try:
            response = requests.post(
                login_url, 
                json=test_case['data'],
                timeout=10
            )
            
            print(f"   状态码: {response.status_code}")
            
            # 检查是否不再出现500错误
            if response.status_code != 500:
                print(f"   ✅ 没有500错误 (预期状态码: {test_case['expected_status']})")
                success_count += 1
                
                # 检查响应内容
                try:
                    response_data = response.json()
                    if 'AxesBackendRequestParameterRequired' not in str(response_data):
                        print(f"   ✅ 没有AxesBackend错误")
                    else:
                        print(f"   ❌ 仍然存在AxesBackend错误")
                except:
                    print(f"   ✅ 响应格式正常")
                    
            else:
                print(f"   ❌ 仍然出现500错误")
                try:
                    error_data = response.json()
                    print(f"   错误详情: {error_data}")
                except:
                    print(f"   错误详情: {response.text[:200]}...")
                    
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 请求失败: {e}")
        
        print("-" * 40)
    
    print("=" * 60)
    print(f"📊 测试结果: {success_count}/{len(test_cases)} 个测试通过")
    
    if success_count == len(test_cases):
        print("🎉 登录API修复成功！不再出现AxesBackend错误！")
        return True
    else:
        print("⚠️ 登录API仍有问题，需要进一步检查")
        return False


def test_axes_functionality():
    """测试Axes功能是否正常工作"""
    print("🧪 测试Axes功能是否正常...")
    
    try:
        from axes.models import AccessAttempt, AccessFailureLog
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 检查Axes模型
        print(f"✅ AccessAttempt模型: {AccessAttempt.objects.count()} 条记录")
        print(f"✅ AccessFailureLog模型: {AccessFailureLog.objects.count()} 条记录")
        
        # 检查配置
        from django.conf import settings
        print(f"✅ AXES_FAILURE_LIMIT: {getattr(settings, 'AXES_FAILURE_LIMIT', 'Not set')}")
        print(f"✅ AXES_COOLOFF_TIME: {getattr(settings, 'AXES_COOLOFF_TIME', 'Not set')}")
        print(f"✅ AXES_LOCKOUT_CALLABLE: {getattr(settings, 'AXES_LOCKOUT_CALLABLE', 'Not set')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Axes功能测试失败: {e}")
        return False


def test_authentication_backends():
    """测试认证后端配置"""
    print("🧪 测试认证后端配置...")
    
    try:
        from django.conf import settings
        
        backends = getattr(settings, 'AUTHENTICATION_BACKENDS', [])
        print(f"认证后端配置: {backends}")
        
        if 'axes.backends.AxesBackend' in backends:
            print("✅ AxesBackend已配置")
        else:
            print("❌ AxesBackend未配置")
            return False
            
        if 'django.contrib.auth.backends.ModelBackend' in backends:
            print("✅ ModelBackend已配置")
        else:
            print("❌ ModelBackend未配置")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 认证后端测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试登录修复效果")
    print("=" * 60)
    
    tests = [
        test_authentication_backends,
        test_axes_functionality,
        test_login_api,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 40)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            print("-" * 40)
    
    print("=" * 60)
    print(f"📊 总体测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！登录修复成功！")
        print("\n📋 修复总结:")
        print("   • 修复了AxesBackendRequestParameterRequired错误")
        print("   • 登录API不再返回500错误")
        print("   • Axes安全功能正常工作")
        print("   • 认证后端配置正确")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
