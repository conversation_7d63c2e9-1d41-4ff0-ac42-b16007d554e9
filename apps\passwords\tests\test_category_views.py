"""
分类相关视图测试
"""
from rest_framework import status
from django.urls import reverse
from apps.passwords.models import Category
from apps.audit.models import BusinessOperationLog
from .base import BasePasswordAPITestCase
from .factories import CategoryFactory


class CategoryListCreateViewTest(BasePasswordAPITestCase):
    """分类列表和创建视图测试"""
    
    def test_list_categories_authenticated(self):
        """测试认证用户获取分类列表"""
        # 创建多个分类
        CategoryFactory.create_batch(3)
        
        self.authenticate_user()
        
        url = reverse("category_list_create")
        
        response = self.client.get(url)
        data = self.assert_response_success(response)
        
        # 验证返回数据
        self.assertIn("results", data)
        self.assertGreaterEqual(len(data["results"]), 4)  # 包括setUp中创建的分类
    
    def test_list_categories_unauthenticated(self):
        """测试未认证用户获取分类列表"""
        url = reverse("category_list_create")
        
        response = self.client.get(url)
        self.assert_response_error(response, status.HTTP_401_UNAUTHORIZED)
    
    def test_list_categories_with_search(self):
        """测试带搜索条件的分类列表"""
        # 创建特定名称的分类
        special_category = CategoryFactory(name="Special Category")
        
        self.authenticate_user()
        
        url = reverse("category_list_create")
        
        # 搜索特定分类
        response = self.client.get(url, {"search": "Special"})
        data = self.assert_response_success(response)
        
        # 验证搜索结果
        self.assertEqual(len(data["results"]), 1)
        self.assertEqual(data["results"][0]["name"], special_category.name)
    
    def test_list_categories_ordering(self):
        """测试分类列表排序"""
        # 创建多个分类
        cat1 = CategoryFactory(name="A Category")
        cat2 = CategoryFactory(name="Z Category")
        cat3 = CategoryFactory(name="M Category")
        
        self.authenticate_user()
        
        url = reverse("category_list_create")
        
        # 按名称升序排序
        response = self.client.get(url, {"ordering": "name"})
        data = self.assert_response_success(response)
        
        # 验证排序结果
        names = [item["name"] for item in data["results"]]
        self.assertEqual(names, sorted(names))
    
    def test_create_category_success(self):
        """测试成功创建分类"""
        self.authenticate_user()
        self.clear_logs()
        
        url = reverse("category_list_create")
        data = {
            "name": "New Category",
            "description": "A new test category",
            "color": "#FF5733"
        }
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response, status.HTTP_201_CREATED)
        
        # 验证分类已创建
        category = Category.objects.get(id=response_data["id"])
        self.assertEqual(category.name, data["name"])
        self.assertEqual(category.description, data["description"])
        self.assertEqual(category.color, data["color"])
        
        # 验证操作日志
        self.assert_operation_logged("category_create", "category")
    
    def test_create_category_duplicate_name(self):
        """测试创建重复名称的分类"""
        self.authenticate_user()
        
        url = reverse("category_list_create")
        data = {
            "name": self.category.name,  # 使用已存在的分类名
            "description": "Duplicate category"
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_create_category_invalid_data(self):
        """测试创建分类时数据无效"""
        self.authenticate_user()
        
        url = reverse("category_list_create")
        data = {
            "name": "",  # 名称不能为空
            "description": "Invalid category"
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_create_category_missing_required_fields(self):
        """测试创建分类时缺少必需字段"""
        self.authenticate_user()
        
        url = reverse("category_list_create")
        data = {
            "description": "Category without name"
            # 缺少name字段
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)


class CategoryDetailViewTest(BasePasswordAPITestCase):
    """分类详情视图测试"""
    
    def test_retrieve_category_success(self):
        """测试成功获取分类详情"""
        self.authenticate_user()
        
        url = reverse("category_detail", kwargs={"pk": self.category.id})
        
        response = self.client.get(url)
        data = self.assert_response_success(response)
        
        # 验证返回数据
        self.assertEqual(data["id"], self.category.id)
        self.assertEqual(data["name"], self.category.name)
        self.assertEqual(data["description"], self.category.description)
    
    def test_retrieve_category_not_found(self):
        """测试获取不存在的分类"""
        self.authenticate_user()
        
        url = reverse("category_detail", kwargs={"pk": 99999})
        
        response = self.client.get(url)
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)
    
    def test_update_category_success(self):
        """测试成功更新分类"""
        self.authenticate_user()
        self.clear_logs()
        
        url = reverse("category_detail", kwargs={"pk": self.category.id})
        data = {
            "name": "Updated Category",
            "description": "Updated description",
            "color": "#00FF00"
        }
        
        response = self.client.put(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证分类已更新
        self.category.refresh_from_db()
        self.assertEqual(self.category.name, data["name"])
        self.assertEqual(self.category.description, data["description"])
        self.assertEqual(self.category.color, data["color"])
        
        # 验证操作日志
        self.assert_operation_logged("category_update", "category")
    
    def test_partial_update_category_success(self):
        """测试成功部分更新分类"""
        self.authenticate_user()
        
        url = reverse("category_detail", kwargs={"pk": self.category.id})
        data = {"name": "Partially Updated"}
        
        response = self.client.patch(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证只有名称被更新
        self.category.refresh_from_db()
        self.assertEqual(self.category.name, data["name"])
    
    def test_update_category_duplicate_name(self):
        """测试更新分类为重复名称"""
        # 创建另一个分类
        other_category = CategoryFactory(name="Other Category")
        
        self.authenticate_user()
        
        url = reverse("category_detail", kwargs={"pk": self.category.id})
        data = {"name": other_category.name}  # 使用已存在的名称
        
        response = self.client.patch(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_update_category_invalid_data(self):
        """测试更新分类时数据无效"""
        self.authenticate_user()
        
        url = reverse("category_detail", kwargs={"pk": self.category.id})
        data = {"name": ""}  # 名称不能为空
        
        response = self.client.patch(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_delete_category_success(self):
        """测试成功删除分类"""
        # 创建一个新分类用于删除测试
        test_category = CategoryFactory()
        
        self.authenticate_user()
        self.clear_logs()
        
        url = reverse("category_detail", kwargs={"pk": test_category.id})
        
        response = self.client.delete(url)
        self.assert_response_success(response, status.HTTP_204_NO_CONTENT)
        
        # 验证分类已删除
        self.assertFalse(Category.objects.filter(id=test_category.id).exists())
        
        # 验证操作日志
        self.assert_operation_logged("category_delete", "category")
    
    def test_delete_category_with_passwords(self):
        """测试删除包含密码的分类"""
        # 该分类已经有密码条目（在setUp中创建）
        self.authenticate_user()
        
        url = reverse("category_detail", kwargs={"pk": self.category.id})
        
        response = self.client.delete(url)
        
        # 根据业务逻辑，可能需要检查是否允许删除包含密码的分类
        # 这里假设不允许删除
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_retrieve_category_unauthenticated(self):
        """测试未认证用户获取分类详情"""
        url = reverse("category_detail", kwargs={"pk": self.category.id})
        
        response = self.client.get(url)
        self.assert_response_error(response, status.HTTP_401_UNAUTHORIZED)
    
    def test_update_category_unauthenticated(self):
        """测试未认证用户更新分类"""
        url = reverse("category_detail", kwargs={"pk": self.category.id})
        data = {"name": "Hacked Category"}
        
        response = self.client.patch(url, data, format="json")
        self.assert_response_error(response, status.HTTP_401_UNAUTHORIZED)
    
    def test_delete_category_unauthenticated(self):
        """测试未认证用户删除分类"""
        url = reverse("category_detail", kwargs={"pk": self.category.id})
        
        response = self.client.delete(url)
        self.assert_response_error(response, status.HTTP_401_UNAUTHORIZED)
