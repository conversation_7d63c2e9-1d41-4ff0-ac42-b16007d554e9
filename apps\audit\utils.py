"""
Audit工具函数
包含axes自定义处理器和其他审计相关工具
"""

import logging
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import SecurityEvent, BusinessOperationLog

logger = logging.getLogger(__name__)
User = get_user_model()


def axes_lockout_handler(request, credentials, *args, **kwargs):
    """
    Axes自定义锁定处理器
    当用户被锁定时调用此函数
    """
    username = credentials.get("username", "Unknown")
    ip_address = get_client_ip(request)
    user_agent = request.META.get("HTTP_USER_AGENT", "")

    logger.warning(
        f"Account lockout triggered for username: {username}, IP: {ip_address}"
    )

    # 创建安全事件
    try:
        user = None
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            pass

        SecurityEvent.objects.create(
            event_type="account_locked",
            severity="high",
            title=f"账户锁定: {username}",
            description=f"用户 {username} 因多次登录失败被自动锁定",
            user=user,
            event_data={
                "username": username,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "lockout_reason": "multiple_failed_logins",
                "timestamp": timezone.now().isoformat(),
            },
        )

        # 记录业务操作日志
        BusinessOperationLog.objects.create(
            user=user,
            action_type="account_lock",
            result="success",
            description=f"账户 {username} 因多次登录失败被自动锁定",
            ip_address=ip_address,
            user_agent=user_agent,
            request_method=request.method,
            request_path=request.path,
            extra_data={
                "lockout_reason": "multiple_failed_logins",
                "username": username,
            },
        )

    except Exception as e:
        logger.error(f"Error creating security event for lockout: {e}")

    # 返回None让django-axes使用默认的锁定处理
    return None


def get_client_ip(request):
    """获取客户端真实IP地址"""
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


def log_business_operation(
    user,
    action_type,
    result="success",
    description="",
    target_type="",
    target_id="",
    target_name="",
    request=None,
    extra_data=None,
):
    """
    记录业务操作日志的便捷函数

    Args:
        user: 操作用户
        action_type: 操作类型
        result: 操作结果 (success/failed/warning)
        description: 操作描述
        target_type: 目标类型
        target_id: 目标ID
        target_name: 目标名称
        request: HTTP请求对象
        extra_data: 额外数据
    """
    try:
        ip_address = "127.0.0.1"
        user_agent = ""
        request_method = ""
        request_path = ""

        if request:
            ip_address = get_client_ip(request)
            user_agent = request.META.get("HTTP_USER_AGENT", "")
            request_method = request.method
            request_path = request.path

        BusinessOperationLog.objects.create(
            user=user,
            action_type=action_type,
            result=result,
            description=description,
            target_type=target_type,
            target_id=str(target_id) if target_id else "",
            target_name=target_name,
            ip_address=ip_address,
            user_agent=user_agent,
            request_method=request_method,
            request_path=request_path,
            extra_data=extra_data or {},
        )

    except Exception as e:
        logger.error(f"Error logging business operation: {e}")


def log_security_event(
    event_type,
    severity,
    title,
    description,
    user=None,
    affected_resources=None,
    event_data=None,
):
    """
    记录安全事件的便捷函数

    Args:
        event_type: 事件类型
        severity: 严重程度 (low/medium/high/critical)
        title: 事件标题
        description: 事件描述
        user: 相关用户
        affected_resources: 受影响资源列表
        event_data: 事件数据
    """
    try:
        SecurityEvent.objects.create(
            event_type=event_type,
            severity=severity,
            title=title,
            description=description,
            user=user,
            affected_resources=affected_resources or [],
            event_data=event_data or {},
        )

    except Exception as e:
        logger.error(f"Error logging security event: {e}")


def get_user_audit_trail(user_id, limit=100):
    """
    获取用户的完整审计轨迹
    整合多个数据源的审计信息

    Args:
        user_id: 用户ID
        limit: 返回记录数限制

    Returns:
        dict: 包含各种审计信息的字典
    """
    try:
        from auditlog.models import LogEntry
        from axes.models import AccessAttempt

        # 获取用户对象
        user = User.objects.get(id=user_id)

        # 获取auditlog记录
        model_changes = LogEntry.objects.filter(actor=user).order_by("-timestamp")[
            :limit
        ]

        # 获取业务操作日志
        business_operations = BusinessOperationLog.objects.filter(user=user).order_by(
            "-created_at"
        )[:limit]

        # 获取安全事件
        security_events = SecurityEvent.objects.filter(user=user).order_by(
            "-created_at"
        )[:limit]

        # 获取登录尝试记录
        login_attempts = AccessAttempt.objects.filter(username=user.username).order_by(
            "-attempt_time"
        )[:limit]

        return {
            "user": user,
            "model_changes": model_changes,
            "business_operations": business_operations,
            "security_events": security_events,
            "login_attempts": login_attempts,
        }

    except Exception as e:
        logger.error(f"Error getting user audit trail: {e}")
        return {}


def get_model_change_history(model_class, instance_id, limit=50):
    """
    获取模型实例的变更历史

    Args:
        model_class: 模型类
        instance_id: 实例ID
        limit: 返回记录数限制

    Returns:
        QuerySet: auditlog记录
    """
    try:
        from auditlog.models import LogEntry
        from django.contrib.contenttypes.models import ContentType

        content_type = ContentType.objects.get_for_model(model_class)

        return LogEntry.objects.filter(
            content_type=content_type, object_pk=str(instance_id)
        ).order_by("-timestamp")[:limit]

    except Exception as e:
        logger.error(f"Error getting model change history: {e}")
        return []
