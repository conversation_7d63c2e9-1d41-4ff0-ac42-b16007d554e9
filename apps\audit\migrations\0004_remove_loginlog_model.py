# Generated by Django 5.0 on 2025-08-05 00:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('audit', '0003_remove_loginlog_model'),
    ]

    operations = [
        migrations.AlterField(
            model_name='businessoperationlog',
            name='action_type',
            field=models.CharField(choices=[('user_login', '用户登录'), ('user_logout', '用户登出'), ('first_login', '首次登录'), ('password_expired_login', '密码过期登录'), ('first_login_password_change', '首次登录密码修改'), ('user_create', '创建用户'), ('user_update', '更新用户'), ('user_delete', '删除用户'), ('password_change', '密码修改'), ('password_view', '查看密码'), ('password_copy', '复制密码'), ('password_export', '导出密码'), ('password_import', '导入密码'), ('password_share', '分享密码'), ('share_revoke', '撤销分享'), ('onetime_link_create', '创建一次性链接'), ('onetime_link_access', '访问一次性链接'), ('system_backup', '系统备份'), ('system_restore', '系统恢复'), ('system_setting_update', '系统设置更新'), ('mfa_enable', '启用多因素认证'), ('mfa_disable', '禁用多因素认证'), ('bulk_operation', '批量操作'), ('data_export', '数据导出')], max_length=30, verbose_name='操作类型'),
        ),
        migrations.DeleteModel(
            name='LoginLog',
        ),
    ]
