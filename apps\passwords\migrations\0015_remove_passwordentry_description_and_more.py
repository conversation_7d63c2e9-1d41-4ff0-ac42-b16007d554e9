# Generated by Django 5.2.4 on 2025-08-01 15:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("passwords", "0014_remove_passwordentry_created_by_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="passwordentry",
            name="description",
        ),
        migrations.RemoveField(
            model_name="passwordentry",
            name="hostname",
        ),
        migrations.RemoveField(
            model_name="passwordentry",
            name="protocol",
        ),
        migrations.RemoveField(
            model_name="passwordentry",
            name="responsible_person",
        ),
        migrations.RemoveField(
            model_name="passwordentry",
            name="schema_name",
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="created_by",
            field=models.CharField(blank=True, max_length=100, verbose_name="创建人"),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="ip_address",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=255, verbose_name="IP地址"),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="mdw_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("bes", "BES"),
                    ("was", "WAS"),
                    ("redis", "Redis"),
                    ("other", "其他"),
                ],
                max_length=20,
                verbose_name="中间件类型",
            ),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="os_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("kylin", "Kylin"),
                    ("suse", "SuSE"),
                    ("windows", "Windows"),
                    ("uos", "UOS"),
                    ("other", "其他"),
                ],
                max_length=20,
                verbose_name="操作系统类型",
            ),
        ),
        migrations.AlterField(
            model_name="passwordentry",
            name="project_name",
            field=models.CharField(max_length=100, verbose_name="应用/项目名称"),
        ),
    ]
