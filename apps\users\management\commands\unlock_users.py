#!/usr/bin/env python
"""
Django管理命令：解锁被django-axes锁定的用户
"""
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta


class Command(BaseCommand):
    help = '解锁被django-axes锁定的用户'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            help='指定要解锁的用户名（不指定则解锁所有用户）',
        )
        parser.add_argument(
            '--reset-password',
            action='store_true',
            help='同时重置用户密码为默认值',
        )
        parser.add_argument(
            '--clear-all',
            action='store_true',
            help='清除所有axes记录（包括失败日志）',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将要执行的操作，不实际执行',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 开始解锁用户操作')
        )
        
        # 检查django-axes是否可用
        try:
            from axes.models import AccessAttempt, AccessFailureLog
            from axes.utils import reset
        except ImportError:
            raise CommandError('django-axes未安装或配置不正确')
        
        User = get_user_model()
        
        # 显示当前锁定状态
        self._show_lockout_status()
        
        # 执行解锁操作
        if options['username']:
            self._unlock_specific_user(options['username'], options)
        else:
            self._unlock_all_users(options)
        
        # 重置密码（如果需要）
        if options['reset_password']:
            self._reset_passwords(options)
        
        # 清除所有记录（如果需要）
        if options['clear_all']:
            self._clear_all_records(options)
        
        self.stdout.write(
            self.style.SUCCESS('✅ 解锁操作完成')
        )

    def _show_lockout_status(self):
        """显示当前锁定状态"""
        try:
            from axes.models import AccessAttempt
            
            self.stdout.write('\n📊 当前锁定状态:')
            self.stdout.write('=' * 50)
            
            attempts = AccessAttempt.objects.all()
            
            if not attempts.exists():
                self.stdout.write('   ✅ 没有用户被锁定')
                return
            
            for attempt in attempts:
                status = '🔒 已锁定' if attempt.failures_since_start >= 3 else '⚠️ 有失败记录'
                self.stdout.write(
                    f'   {status} 用户: {attempt.username}'
                )
                self.stdout.write(
                    f'      IP: {attempt.ip_address}'
                )
                self.stdout.write(
                    f'      失败次数: {attempt.failures_since_start}'
                )
                self.stdout.write(
                    f'      最后尝试: {attempt.attempt_time}'
                )
                self.stdout.write('')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ 显示锁定状态失败: {e}')
            )

    def _unlock_specific_user(self, username, options):
        """解锁指定用户"""
        try:
            from axes.models import AccessAttempt
            from axes.utils import reset
            
            self.stdout.write(f'\n🔓 解锁用户: {username}')
            
            if options['dry_run']:
                attempts = AccessAttempt.objects.filter(username=username)
                if attempts.exists():
                    self.stdout.write(f'   [DRY RUN] 将删除 {attempts.count()} 条锁定记录')
                else:
                    self.stdout.write(f'   [DRY RUN] 用户 {username} 没有锁定记录')
                return
            
            # 使用axes的reset功能
            reset(username=username)
            
            self.stdout.write(
                self.style.SUCCESS(f'   ✅ 用户 {username} 解锁成功')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ 解锁用户 {username} 失败: {e}')
            )

    def _unlock_all_users(self, options):
        """解锁所有用户"""
        try:
            from axes.models import AccessAttempt
            
            self.stdout.write('\n🔓 解锁所有用户')
            
            attempts = AccessAttempt.objects.all()
            count = attempts.count()
            
            if count == 0:
                self.stdout.write('   ✅ 没有用户需要解锁')
                return
            
            if options['dry_run']:
                self.stdout.write(f'   [DRY RUN] 将删除 {count} 条锁定记录')
                for attempt in attempts:
                    self.stdout.write(f'      - {attempt.username} ({attempt.failures_since_start} 次失败)')
                return
            
            # 删除所有访问尝试记录
            attempts.delete()
            
            self.stdout.write(
                self.style.SUCCESS(f'   ✅ 解锁了 {count} 个用户')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ 解锁所有用户失败: {e}')
            )

    def _reset_passwords(self, options):
        """重置用户密码"""
        User = get_user_model()
        
        self.stdout.write('\n🔐 重置用户密码')
        
        # 默认密码配置
        password_configs = [
            {'username': 'root', 'password': 'root123!'},
            {'username': 'admin', 'password': 'admin123!'},
        ]
        
        for config in password_configs:
            username = config['username']
            password = config['password']
            
            try:
                user = User.objects.get(username=username)
                
                if options['dry_run']:
                    self.stdout.write(f'   [DRY RUN] 将重置用户 {username} 的密码')
                    continue
                
                user.set_password(password)
                user.is_active = True
                user.save()
                
                # 验证密码设置
                if user.check_password(password):
                    self.stdout.write(
                        self.style.SUCCESS(f'   ✅ 用户 {username} 密码重置成功')
                    )
                    self.stdout.write(f'      新密码: {password}')
                else:
                    self.stdout.write(
                        self.style.ERROR(f'   ❌ 用户 {username} 密码验证失败')
                    )
                
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'   ⚠️ 用户 {username} 不存在')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'   ❌ 重置用户 {username} 密码失败: {e}')
                )

    def _clear_all_records(self, options):
        """清除所有axes记录"""
        try:
            from axes.models import AccessAttempt, AccessFailureLog
            
            self.stdout.write('\n🗑️ 清除所有axes记录')
            
            attempt_count = AccessAttempt.objects.count()
            failure_count = AccessFailureLog.objects.count()
            
            if options['dry_run']:
                self.stdout.write(f'   [DRY RUN] 将删除 {attempt_count} 条访问尝试记录')
                self.stdout.write(f'   [DRY RUN] 将删除 {failure_count} 条失败日志记录')
                return
            
            # 清除访问尝试记录
            AccessAttempt.objects.all().delete()
            self.stdout.write(
                self.style.SUCCESS(f'   ✅ 删除了 {attempt_count} 条访问尝试记录')
            )
            
            # 清除失败日志记录
            AccessFailureLog.objects.all().delete()
            self.stdout.write(
                self.style.SUCCESS(f'   ✅ 删除了 {failure_count} 条失败日志记录')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ 清除记录失败: {e}')
            )

    def _test_login_after_unlock(self):
        """解锁后测试登录"""
        self.stdout.write('\n🧪 测试登录功能')
        
        try:
            from django.test import Client
            import json
            
            client = Client()
            
            test_credentials = [
                {'username': 'root', 'password': 'root123!'},
                {'username': 'admin', 'password': 'admin123!'},
            ]
            
            for creds in test_credentials:
                response = client.post(
                    '/api/auth/login/',
                    data=json.dumps(creds),
                    content_type='application/json'
                )
                
                if response.status_code == 200:
                    self.stdout.write(
                        self.style.SUCCESS(f'   ✅ {creds["username"]} 登录成功')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f'   ❌ {creds["username"]} 登录失败: {response.status_code}')
                    )
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ 测试登录失败: {e}')
            )
