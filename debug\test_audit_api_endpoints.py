#!/usr/bin/env python
"""
测试audit应用的API端点
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()


def test_audit_api_endpoints():
    """测试audit API端点"""
    print("🧪 测试audit API端点...")

    try:
        from django.test import Client
        from django.contrib.auth import get_user_model
        from django.urls import reverse
        from rest_framework_simplejwt.tokens import RefreshToken
        import json

        User = get_user_model()

        # 创建管理员用户用于测试
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.create_superuser(
                username="test_admin",
                email="<EMAIL>",
                password="admin_password_123",
                name="测试管理员",
            )
            print("✅ 创建测试管理员用户")
        else:
            print("✅ 使用现有管理员用户")

        # 生成JWT令牌
        refresh = RefreshToken.for_user(admin_user)
        access_token = str(refresh.access_token)

        # 创建测试客户端
        client = Client()

        # 定义要测试的API端点
        api_endpoints = [
            # 业务操作日志
            {
                "name": "业务操作日志列表",
                "url": "/api/audit/business-operations/",
                "method": "GET",
                "auth_required": True,
            },
            # 密码访问日志
            {
                "name": "密码访问日志列表",
                "url": "/api/audit/password-access/",
                "method": "GET",
                "auth_required": True,
            },
            # 安全事件
            {
                "name": "安全事件列表",
                "url": "/api/audit/security-events/",
                "method": "GET",
                "auth_required": True,
            },
            # 模型变更日志
            {
                "name": "模型变更日志列表",
                "url": "/api/audit/model-changes/",
                "method": "GET",
                "auth_required": True,
            },
            # 登录尝试记录
            {
                "name": "登录尝试记录列表",
                "url": "/api/audit/login-attempts/",
                "method": "GET",
                "auth_required": True,
            },
            # Axes相关
            {
                "name": "Axes访问尝试",
                "url": "/api/audit/axes/access-attempts/",
                "method": "GET",
                "auth_required": True,
            },
            {
                "name": "Axes访问失败",
                "url": "/api/audit/axes/access-failures/",
                "method": "GET",
                "auth_required": True,
            },
            {
                "name": "Axes访问日志",
                "url": "/api/audit/axes/access-logs/",
                "method": "GET",
                "auth_required": True,
            },
            # 统计信息
            {
                "name": "审计统计",
                "url": "/api/audit/statistics/",
                "method": "GET",
                "auth_required": True,
            },
            # 兼容性端点
            {
                "name": "操作日志（兼容）",
                "url": "/api/audit/operation-logs/",
                "method": "GET",
                "auth_required": True,
            },
            {
                "name": "访问日志（兼容）",
                "url": "/api/audit/access-logs/",
                "method": "GET",
                "auth_required": True,
            },
        ]

        successful_endpoints = 0
        total_endpoints = len(api_endpoints)

        print(f"\n📊 开始测试 {total_endpoints} 个API端点...")
        print("-" * 60)

        for endpoint in api_endpoints:
            print(f"\n🔍 测试: {endpoint['name']}")
            print(f"   URL: {endpoint['url']}")

            # 准备请求头
            headers = {}
            if endpoint["auth_required"]:
                headers["HTTP_AUTHORIZATION"] = f"Bearer {access_token}"

            try:
                # 发送请求
                if endpoint["method"] == "GET":
                    response = client.get(endpoint["url"], **headers)
                elif endpoint["method"] == "POST":
                    response = client.post(endpoint["url"], **headers)
                else:
                    print(f"   ❌ 不支持的HTTP方法: {endpoint['method']}")
                    continue

                # 检查响应
                print(f"   📊 状态码: {response.status_code}")

                if response.status_code == 200:
                    print("   ✅ 请求成功")

                    # 尝试解析JSON响应
                    try:
                        data = response.json()
                        if isinstance(data, dict):
                            if "results" in data:
                                print(f"   📋 返回 {len(data['results'])} 条记录")
                                if "count" in data:
                                    print(f"   📊 总计 {data['count']} 条记录")
                            elif "data" in data:
                                print(f"   📋 返回数据: {type(data['data']).__name__}")
                            else:
                                print(f"   📋 返回数据: {len(data)} 个字段")
                        elif isinstance(data, list):
                            print(f"   📋 返回 {len(data)} 条记录")
                        else:
                            print(f"   📋 返回数据类型: {type(data).__name__}")
                    except Exception as e:
                        print(f"   ⚠️ JSON解析失败: {e}")

                    successful_endpoints += 1

                elif response.status_code == 401:
                    print("   ❌ 认证失败")
                elif response.status_code == 403:
                    print("   ❌ 权限不足")
                elif response.status_code == 404:
                    print("   ❌ 端点不存在")
                elif response.status_code == 500:
                    print("   ❌ 服务器内部错误")
                    try:
                        error_data = response.json()
                        print(f"   🔍 错误详情: {error_data}")
                    except:
                        print(f"   🔍 响应内容: {response.content[:200]}")
                else:
                    print(f"   ❌ 未知错误: {response.status_code}")

            except Exception as e:
                print(f"   ❌ 请求异常: {e}")
                import traceback

                traceback.print_exc()

        print("\n" + "=" * 60)
        print(f"📊 测试结果: {successful_endpoints}/{total_endpoints} 个端点成功")

        if successful_endpoints == total_endpoints:
            print("🎉 所有audit API端点测试通过！")
        elif successful_endpoints > total_endpoints * 0.8:
            print("✅ 大部分audit API端点正常工作")
        else:
            print("⚠️ 多个audit API端点存在问题")

        return successful_endpoints == total_endpoints

    except Exception as e:
        print(f"❌ audit API测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_audit_data_creation():
    """测试audit数据创建"""
    print("\n🧪 测试audit数据创建...")

    try:
        from apps.audit.models import (
            BusinessOperationLog,
            PasswordAccessLog,
            SecurityEvent,
        )
        from apps.passwords.models import PasswordEntry
        from django.contrib.auth import get_user_model

        User = get_user_model()

        # 获取测试用户
        test_user = User.objects.filter(username="test_login").first()
        if not test_user:
            test_user = User.objects.create_user(
                username="test_audit",
                email="<EMAIL>",
                password="test_password_123",
                name="测试审计用户",
            )
            print("✅ 创建测试用户")

        # 测试创建业务操作日志
        print("\n📝 测试创建业务操作日志...")
        business_log = BusinessOperationLog.objects.create(
            user=test_user,
            action_type="test_audit_api",
            target_type="test",
            target_id="test_id",
            target_name="测试目标",
            ip_address="127.0.0.1",
            user_agent="Test Agent",
            extra_data={"test": "audit_api"},
        )
        print(f"✅ 创建业务操作日志: {business_log.id}")

        # 测试创建安全事件
        print("\n🔒 测试创建安全事件...")
        security_event = SecurityEvent.objects.create(
            event_type="test_security",
            severity="medium",
            title="测试安全事件",
            description="测试安全事件描述",
            user=test_user,
            event_data={
                "test": "security_event",
                "ip_address": "127.0.0.1",
                "user_agent": "Test Agent",
            },
        )
        print(f"✅ 创建安全事件: {security_event.id}")

        # 检查数据统计
        print("\n📊 检查数据统计...")
        business_count = BusinessOperationLog.objects.count()
        security_count = SecurityEvent.objects.count()
        password_access_count = PasswordAccessLog.objects.count()

        print(f"   - 业务操作日志: {business_count} 条")
        print(f"   - 安全事件: {security_count} 条")
        print(f"   - 密码访问日志: {password_access_count} 条")

        return True

    except Exception as e:
        print(f"❌ audit数据创建测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试audit应用API端点")
    print("=" * 60)

    tests = [
        test_audit_data_creation,
        test_audit_api_endpoints,
    ]

    passed = 0
    total = len(tests)

    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            print("-" * 50)

    print("=" * 60)
    print(f"📊 总体测试结果: {passed}/{total} 个测试通过")

    if passed == total:
        print("🎉 audit应用API端点测试完全通过！")
        print("\n💡 测试总结:")
        print("  1. ✅ audit数据模型正常工作")
        print("  2. ✅ 所有API端点可访问")
        print("  3. ✅ 认证和权限控制正常")
        print("  4. ✅ 数据序列化正常")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
