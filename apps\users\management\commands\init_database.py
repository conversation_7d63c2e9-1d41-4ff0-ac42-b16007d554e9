"""初始化数据库数据的Django管理命令"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone
from datetime import timedelta

User = get_user_model()


class Command(BaseCommand):
    help = "初始化数据库基础数据"

    def add_arguments(self, parser):
        parser.add_argument(
            "--force",
            action="store_true",
            help="强制重新创建数据，即使已存在",
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("开始初始化数据库数据..."))

        try:
            with transaction.atomic():
                # 创建默认部门
                self.stdout.write("\n创建默认部门...")
                self.create_default_departments(options["force"])

                # 创建默认团队
                self.stdout.write("\n创建默认团队...")
                self.create_default_teams(options["force"])

                # 创建默认角色
                self.stdout.write("\n创建默认角色...")
                self.create_default_roles(options["force"])

                # 创建默认分类
                self.stdout.write("\n创建默认分类...")
                self.create_default_categories(options["force"])

                # 创建默认标签
                self.stdout.write("\n创建默认标签...")
                self.create_default_tags(options["force"])

                # 创建默认系统设置
                self.stdout.write("\n创建默认系统设置...")
                self.create_default_system_settings(options["force"])

            self.stdout.write(self.style.SUCCESS("\n数据库初始化完成！"))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"数据库初始化失败: {e}"))
            raise

    def create_default_departments(self, force=False):
        """创建默认部门"""
        from apps.users.models import Department

        departments = [
            {"name": "技术部", "description": "负责技术开发和维护"},
            {"name": "产品部", "description": "负责产品设计和规划"},
            {"name": "运营部", "description": "负责产品运营和推广"},
            {"name": "人事部", "description": "负责人力资源管理"},
            {"name": "财务部", "description": "负责财务管理"},
            {"name": "市场部", "description": "负责市场营销"},
        ]

        for dept_data in departments:
            if force:
                Department.objects.filter(name=dept_data["name"]).delete()

            dept, created = Department.objects.get_or_create(
                name=dept_data["name"],
                defaults={"description": dept_data["description"]},
            )
            if created:
                self.stdout.write(f"  创建部门: {dept.name}")
            else:
                self.stdout.write(f"  部门已存在: {dept.name}")

    def create_default_teams(self, force=False):
        """创建默认团队 - 已废弃，Team模型已删除"""
        self.stdout.write(self.style.WARNING("Team模型已删除，跳过团队创建"))

    def create_default_roles(self, force=False):
        """创建默认角色"""
        from apps.users.models import Role

        roles = [
            {
                "name": "超级管理员",
                "description": "拥有系统所有权限",
                "permissions": {
                    "user_management": True,
                    "password_management": True,
                    "sharing_management": True,
                    "audit_management": True,
                    "system_management": True,
                    "category_management": True,
                },
            },
            {
                "name": "管理员",
                "description": "拥有大部分管理权限",
                "permissions": {
                    "user_management": True,
                    "password_management": True,
                    "sharing_management": True,
                    "audit_management": True,
                    "system_management": False,
                    "category_management": True,
                },
            },
            {
                "name": "普通用户",
                "description": "基本的密码管理权限",
                "permissions": {
                    "user_management": False,
                    "password_management": True,
                    "sharing_management": True,
                    "audit_management": False,
                    "system_management": False,
                    "category_management": False,
                },
            },
            {
                "name": "只读用户",
                "description": "只能查看密码，不能修改",
                "permissions": {
                    "user_management": False,
                    "password_management": False,
                    "sharing_management": False,
                    "audit_management": False,
                    "system_management": False,
                    "category_management": False,
                },
            },
        ]

        for role_data in roles:
            if force:
                Role.objects.filter(name=role_data["name"]).delete()

            role, created = Role.objects.get_or_create(
                name=role_data["name"],
                defaults={
                    "description": role_data["description"],
                    "permissions": role_data["permissions"],
                },
            )
            if created:
                self.stdout.write(f"  创建角色: {role.name}")
            else:
                self.stdout.write(f"  角色已存在: {role.name}")

    def create_default_categories(self, force=False):
        """创建默认密码分类"""
        from apps.passwords.models import Category

        categories = [
            {"name": "网站账号", "description": "各种网站的登录账号", "icon": "web"},
            {"name": "邮箱账号", "description": "邮箱登录账号", "icon": "email"},
            {"name": "社交媒体", "description": "社交媒体平台账号", "icon": "social"},
            {"name": "银行卡", "description": "银行卡相关信息", "icon": "bank"},
            {"name": "信用卡", "description": "信用卡相关信息", "icon": "credit"},
            {"name": "软件许可", "description": "软件许可证信息", "icon": "software"},
            {"name": "服务器", "description": "服务器登录信息", "icon": "server"},
            {"name": "数据库", "description": "数据库连接信息", "icon": "database"},
            {"name": "API密钥", "description": "API访问密钥", "icon": "api"},
            {"name": "其他", "description": "其他类型的密码", "icon": "other"},
        ]

        # 创建或获取默认的超级管理员用户作为分类的所有者
        admin_user, created = User.objects.get_or_create(
            username="admin",
            defaults={
                "email": "<EMAIL>",
                "is_staff": True,
                "is_superuser": True,
            },
        )
        if created:
            admin_user.set_password("admin123456")
            admin_user.save()
            self.stdout.write(f"  创建管理员用户: {admin_user.username}")

        for cat_data in categories:
            if force:
                Category.objects.filter(name=cat_data["name"], user=admin_user).delete()

            category, created = Category.objects.get_or_create(
                name=cat_data["name"],
                user=admin_user,
                defaults={
                    "description": cat_data["description"],
                    "icon": cat_data["icon"],
                },
            )
            if created:
                self.stdout.write(f"  创建分类: {category.name}")
            else:
                self.stdout.write(f"  分类已存在: {category.name}")

    def create_default_tags(self, force=False):
        """创建默认标签 - 已移除标签功能"""
        # 标签功能已被移除，此方法保留为空以保持向后兼容性
        self.stdout.write(self.style.WARNING("  标签功能已移除，跳过标签创建"))

    def create_default_system_settings(self, force=False):
        """创建默认系统设置"""
        from apps.system.models import SystemSetting

        settings = [
            # 密码策略设置
            {
                "key": "password_min_length",
                "value": "8",
                "category": "password_policy",
                "description": "密码最小长度",
            },
            {
                "key": "password_require_uppercase",
                "value": "true",
                "category": "password_policy",
                "description": "密码必须包含大写字母",
            },
            {
                "key": "password_require_lowercase",
                "value": "true",
                "category": "password_policy",
                "description": "密码必须包含小写字母",
            },
            {
                "key": "password_require_numbers",
                "value": "true",
                "category": "password_policy",
                "description": "密码必须包含数字",
            },
            {
                "key": "password_require_symbols",
                "value": "true",
                "category": "password_policy",
                "description": "密码必须包含特殊字符",
            },
            {
                "key": "password_history_count",
                "value": "5",
                "category": "password_policy",
                "description": "密码历史记录保留数量",
            },
            # 安全设置
            {
                "key": "session_timeout",
                "value": "3600",
                "category": "security",
                "description": "会话超时时间（秒）",
            },
            {
                "key": "max_login_attempts",
                "value": "5",
                "category": "security",
                "description": "最大登录尝试次数",
            },
            {
                "key": "account_lockout_duration",
                "value": "1800",
                "category": "security",
                "description": "账户锁定时间（秒）",
            },
            {
                "key": "enable_mfa",
                "value": "false",
                "category": "security",
                "description": "启用多因素认证",
            },
            {
                "key": "enable_ip_whitelist",
                "value": "false",
                "category": "security",
                "description": "启用IP白名单",
            },
            # 分享设置
            {
                "key": "default_share_expiry",
                "value": "24",
                "category": "sharing",
                "description": "默认分享过期时间（小时）",
            },
            {
                "key": "max_share_expiry",
                "value": "168",
                "category": "sharing",
                "description": "最大分享过期时间（小时）",
            },
            {
                "key": "enable_one_time_links",
                "value": "true",
                "category": "sharing",
                "description": "启用一次性链接",
            },
            # 备份设置
            {
                "key": "auto_backup_enabled",
                "value": "true",
                "category": "backup",
                "description": "启用自动备份",
            },
            {
                "key": "backup_frequency",
                "value": "daily",
                "category": "backup",
                "description": "备份频率",
            },
            {
                "key": "backup_retention_days",
                "value": "30",
                "category": "backup",
                "description": "备份保留天数",
            },
            # 通知设置
            {
                "key": "enable_email_notifications",
                "value": "true",
                "category": "notification",
                "description": "启用邮件通知",
            },
            {
                "key": "enable_security_alerts",
                "value": "true",
                "category": "notification",
                "description": "启用安全警报",
            },
        ]

        for setting_data in settings:
            if force:
                SystemSetting.objects.filter(key=setting_data["key"]).delete()

            setting, created = SystemSetting.objects.get_or_create(
                key=setting_data["key"],
                defaults={
                    "value": setting_data["value"],
                    "category": setting_data["category"],
                    "description": setting_data["description"],
                },
            )
            if created:
                self.stdout.write(f"  创建系统设置: {setting.key}")
            else:
                self.stdout.write(f"  系统设置已存在: {setting.key}")
