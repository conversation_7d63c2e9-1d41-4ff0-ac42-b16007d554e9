from rest_framework import serializers
from .models import SystemSetting


class TestSystemSettingSerializer(serializers.Serializer):
    """测试系统设置序列化器"""

    id = serializers.IntegerField(read_only=True)
    key = serializers.CharField(max_length=100)
    name = serializers.CharField(max_length=200)
    value = serializers.CharField()
    description = serializers.CharField(required=False, allow_blank=True)
    category = serializers.ChoiceField(choices=SystemSetting.CATEGORIES, required=False)
    value_type = serializers.ChoiceField(choices=SystemSetting.SETTING_TYPES, required=False)
    is_public = serializers.BooleanField(required=False)
    is_readonly = serializers.BooleanField(required=False)
    requires_restart = serializers.BooleanField(required=False)
    order = serializers.IntegerField(required=False)
    group = serializers.CharField(max_length=50, required=False, allow_blank=True)
    modified_by = serializers.PrimaryKeyRelatedField(read_only=True)
    modified_by_name = serializers.CharField(
        source="modified_by.get_full_name", read_only=True
    )
    created_at = serializers.DateTimeField(read_only=True)
    updated_at = serializers.DateTimeField(read_only=True)

    def create(self, validated_data):
        return SystemSetting.objects.create(**validated_data)

    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance
