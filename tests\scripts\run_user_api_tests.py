#!/usr/bin/env python3
"""
用户管理CRUD API自动化测试运行脚本
"""
import os
import sys
import subprocess
import time
import json
from datetime import datetime
import argparse

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")

import django

django.setup()

from django.test.utils import get_runner
from django.conf import settings
from django.core.management import call_command
from io import StringIO


class TestRunner:
    """测试运行器"""

    def __init__(self, verbosity=2, keepdb=False, parallel=1):
        self.verbosity = verbosity
        self.keepdb = keepdb
        self.parallel = parallel
        self.results = {}
        self.start_time = None
        self.end_time = None

    def run_test_suite(self, test_labels=None, pattern=None):
        """运行测试套件"""
        print("🚀 开始运行用户管理CRUD API自动化测试...")
        print("=" * 80)

        self.start_time = time.time()

        # 默认测试标签
        if not test_labels:
            test_labels = [
                "tests.unit.test_user_crud_automation",
                "tests.unit.test_user_crud_api",
                "tests.integration.test_user_crud_compatibility",
            ]

        # 运行每个测试模块
        for test_label in test_labels:
            print(f"\n📋 运行测试模块: {test_label}")
            print("-" * 60)

            result = self._run_single_test(test_label, pattern)
            self.results[test_label] = result

        self.end_time = time.time()
        self._generate_report()

    def _run_single_test(self, test_label, pattern=None):
        """运行单个测试模块"""
        try:
            # 捕获输出
            output = StringIO()

            # 构建测试命令参数
            cmd_args = [
                "test",
                test_label,
                f"--verbosity={self.verbosity}",
                "--noinput",
            ]

            if self.keepdb:
                cmd_args.append("--keepdb")

            if self.parallel > 1:
                cmd_args.extend(["--parallel", str(self.parallel)])

            if pattern:
                cmd_args.extend(["--pattern", pattern])

            # 运行测试
            start_time = time.time()

            try:
                call_command(*cmd_args, stdout=output, stderr=output)
                success = True
                error_message = None
            except SystemExit as e:
                success = e.code == 0
                error_message = output.getvalue() if e.code != 0 else None
            except Exception as e:
                success = False
                error_message = str(e)

            end_time = time.time()
            duration = end_time - start_time

            output_content = output.getvalue()

            # 解析测试结果
            test_count = self._parse_test_count(output_content)
            failure_count = self._parse_failure_count(output_content)
            error_count = self._parse_error_count(output_content)

            result = {
                "success": success,
                "duration": duration,
                "test_count": test_count,
                "failure_count": failure_count,
                "error_count": error_count,
                "output": output_content,
                "error_message": error_message,
            }

            # 打印结果摘要
            status = "✅ 通过" if success else "❌ 失败"
            print(f"状态: {status}")
            print(f"测试数量: {test_count}")
            print(f"失败数量: {failure_count}")
            print(f"错误数量: {error_count}")
            print(f"耗时: {duration:.2f}秒")

            if not success and error_message:
                print(f"错误信息: {error_message}")

            return result

        except Exception as e:
            print(f"❌ 运行测试时发生错误: {e}")
            return {
                "success": False,
                "duration": 0,
                "test_count": 0,
                "failure_count": 0,
                "error_count": 1,
                "output": "",
                "error_message": str(e),
            }

    def _parse_test_count(self, output):
        """解析测试数量"""
        import re

        match = re.search(r"Ran (\d+) test", output)
        return int(match.group(1)) if match else 0

    def _parse_failure_count(self, output):
        """解析失败数量"""
        import re

        match = re.search(r"failures=(\d+)", output)
        return int(match.group(1)) if match else 0

    def _parse_error_count(self, output):
        """解析错误数量"""
        import re

        match = re.search(r"errors=(\d+)", output)
        return int(match.group(1)) if match else 0

    def _generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("📊 测试报告")
        print("=" * 80)

        total_duration = self.end_time - self.start_time
        total_tests = sum(r["test_count"] for r in self.results.values())
        total_failures = sum(r["failure_count"] for r in self.results.values())
        total_errors = sum(r["error_count"] for r in self.results.values())
        success_count = sum(1 for r in self.results.values() if r["success"])

        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  总耗时: {total_duration:.2f}秒")
        print(f"📦 测试模块: {len(self.results)}")
        print(f"✅ 成功模块: {success_count}")
        print(f"❌ 失败模块: {len(self.results) - success_count}")
        print(f"🧪 总测试数: {total_tests}")
        print(f"❌ 总失败数: {total_failures}")
        print(f"⚠️  总错误数: {total_errors}")

        # 详细结果
        print("\n📋 详细结果:")
        print("-" * 80)

        for test_label, result in self.results.items():
            status = "✅" if result["success"] else "❌"
            print(f"{status} {test_label}")
            print(
                f"   测试: {result['test_count']}, 失败: {result['failure_count']}, 错误: {result['error_count']}, 耗时: {result['duration']:.2f}s"
            )

            if not result["success"] and result["error_message"]:
                print(f"   错误: {result['error_message'][:100]}...")

        # 生成JSON报告
        self._save_json_report(
            total_duration, total_tests, total_failures, total_errors
        )

        # 总结
        print("\n" + "=" * 80)
        if total_failures == 0 and total_errors == 0:
            print("🎉 所有测试通过！")
        else:
            print(f"⚠️  测试完成，但有 {total_failures} 个失败和 {total_errors} 个错误")
        print("=" * 80)

    def _save_json_report(
        self, total_duration, total_tests, total_failures, total_errors
    ):
        """保存JSON格式的测试报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_duration": total_duration,
                "total_tests": total_tests,
                "total_failures": total_failures,
                "total_errors": total_errors,
                "success_rate": (
                    (total_tests - total_failures - total_errors) / total_tests * 100
                    if total_tests > 0
                    else 0
                ),
            },
            "modules": {},
        }

        for test_label, result in self.results.items():
            report["modules"][test_label] = {
                "success": result["success"],
                "duration": result["duration"],
                "test_count": result["test_count"],
                "failure_count": result["failure_count"],
                "error_count": result["error_count"],
            }

        # 确保reports目录存在
        reports_dir = "reports"
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # 保存报告
        report_file = os.path.join(
            reports_dir,
            f'user_api_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json',
        )
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"📄 JSON报告已保存到: {report_file}")


def run_specific_tests():
    """运行特定类型的测试"""
    test_categories = {
        "crud": ["tests.unit.test_user_crud_automation.UserCRUDTestCase"],
        "permission": ["tests.unit.test_user_crud_automation.PermissionTestCase"],
        "validation": ["tests.unit.test_user_crud_automation.ValidationTestCase"],
        "search": ["tests.unit.test_user_crud_automation.SearchAndFilterTestCase"],
        "organization": [
            "tests.unit.test_user_crud_automation.DepartmentCRUDTestCase",
            "tests.unit.test_user_crud_automation.TeamCRUDTestCase",
            "tests.unit.test_user_crud_automation.RoleCRUDTestCase",
        ],
        "group": ["tests.unit.test_user_crud_automation.GroupCRUDTestCase"],
        "audit": ["tests.unit.test_user_crud_automation.AuditLogTestCase"],
        "performance": ["tests.unit.test_user_crud_automation.PerformanceTestCase"],
        "concurrency": ["tests.unit.test_user_crud_automation.ConcurrencyTestCase"],
        "edge": ["tests.unit.test_user_crud_automation.EdgeCaseTestCase"],
        "security": ["tests.unit.test_user_crud_automation.SecurityTestCase"],
        "compatibility": ["tests.integration.test_user_crud_compatibility"],
    }

    print("可用的测试类别:")
    for category, tests in test_categories.items():
        print(f"  {category}: {len(tests)} 个测试类")

    return test_categories


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="用户管理CRUD API自动化测试运行器")
    parser.add_argument("--category", "-c", help="运行特定类别的测试")
    parser.add_argument(
        "--verbosity", "-v", type=int, default=2, help="输出详细程度 (0-3)"
    )
    parser.add_argument("--keepdb", action="store_true", help="保留测试数据库")
    parser.add_argument(
        "--parallel", "-p", type=int, default=1, help="并行运行测试的进程数"
    )
    parser.add_argument("--pattern", help="测试文件名模式")
    parser.add_argument(
        "--list-categories", action="store_true", help="列出所有测试类别"
    )

    args = parser.parse_args()

    if args.list_categories:
        test_categories = run_specific_tests()
        return

    runner = TestRunner(
        verbosity=args.verbosity, keepdb=args.keepdb, parallel=args.parallel
    )

    if args.category:
        test_categories = run_specific_tests()
        if args.category in test_categories:
            test_labels = test_categories[args.category]
            print(f"运行 '{args.category}' 类别的测试...")
        else:
            print(f"错误: 未知的测试类别 '{args.category}'")
            print("使用 --list-categories 查看可用类别")
            return
    else:
        test_labels = None

    runner.run_test_suite(test_labels, args.pattern)


if __name__ == "__main__":
    main()
