# Generated by Django 5.2.4 on 2025-08-04 16:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0007_user_name'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='is_first_login',
            field=models.BooleanField(default=True, verbose_name='是否首次登录'),
        ),
        migrations.AddField(
            model_name='user',
            name='password_expires_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='密码过期时间'),
        ),
        migrations.AddField(
            model_name='user',
            name='password_must_change',
            field=models.BooleanField(default=False, verbose_name='必须修改密码'),
        ),
        migrations.AddField(
            model_name='user',
            name='temp_password_token',
            field=models.CharField(blank=True, max_length=64, verbose_name='临时密码令牌'),
        ),
    ]
