"""
用户管理CRUD API测试
"""
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth.models import Group
from rest_framework.test import APIClient
from rest_framework import status
from .models import User, Department, Team, Role


class UserCRUDAPITest(TestCase):
    """用户CRUD API测试"""

    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True
        )
        
        # 创建普通用户
        self.normal_user = User.objects.create_user(
            username='user',
            email='<EMAIL>',
            password='user123'
        )
        
        # 创建部门
        self.department = Department.objects.create(
            name='技术部',
            description='技术开发部门'
        )
        
        # 创建团队
        self.team = Team.objects.create(
            name='后端团队',
            description='后端开发团队',
            department=self.department
        )
        
        # 创建角色
        self.role = Role.objects.create(
            name='developer',
            description='开发人员'
        )
        
        # 创建用户组
        self.group = Group.objects.create(name='开发组')

    def test_user_list_api(self):
        """测试用户列表API"""
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)

    def test_user_create_api(self):
        """测试用户创建API"""
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('user-list')
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'name': '新用户',
            'password': 'NewPassword123!',
            'password_confirm': 'NewPassword123!',
            'department': self.department.id,
            'team': self.team.id,
            'role': self.role.id,
            'groups': [self.group.id]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['username'], 'newuser')

    def test_user_detail_api(self):
        """测试用户详情API"""
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('user-detail', kwargs={'pk': self.normal_user.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['username'], 'user')

    def test_user_update_api(self):
        """测试用户更新API"""
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('user-detail', kwargs={'pk': self.normal_user.id})
        data = {
            'name': '更新后的用户名',
            'department': self.department.id,
            'groups': [self.group.id]
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['name'], '更新后的用户名')

    def test_user_delete_api(self):
        """测试用户删除API（软删除）"""
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('user-detail', kwargs={'pk': self.normal_user.id})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # 验证用户被软删除（is_active=False）
        self.normal_user.refresh_from_db()
        self.assertFalse(self.normal_user.is_active)


class DepartmentCRUDAPITest(TestCase):
    """部门CRUD API测试"""

    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True
        )
        self.client.force_authenticate(user=self.admin_user)

    def test_department_list_api(self):
        """测试部门列表API"""
        Department.objects.create(name='技术部', description='技术开发部门')
        Department.objects.create(name='产品部', description='产品设计部门')
        
        url = reverse('department-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 2)

    def test_department_create_api(self):
        """测试部门创建API"""
        url = reverse('department-list')
        data = {
            'name': '新部门',
            'description': '新部门描述'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['name'], '新部门')

    def test_department_with_parent(self):
        """测试创建有上级部门的部门"""
        parent_dept = Department.objects.create(name='总部', description='公司总部')
        
        url = reverse('department-list')
        data = {
            'name': '分部',
            'description': '分部描述',
            'parent': parent_dept.id
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['parent'], parent_dept.id)


class TeamCRUDAPITest(TestCase):
    """团队CRUD API测试"""

    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True
        )
        self.department = Department.objects.create(
            name='技术部',
            description='技术开发部门'
        )
        self.client.force_authenticate(user=self.admin_user)

    def test_team_create_api(self):
        """测试团队创建API"""
        url = reverse('team-list')
        data = {
            'name': '前端团队',
            'description': '前端开发团队',
            'department': self.department.id
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['name'], '前端团队')


class GroupCRUDAPITest(TestCase):
    """用户组CRUD API测试"""

    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True
        )
        self.normal_user = User.objects.create_user(
            username='user',
            email='<EMAIL>',
            password='user123'
        )
        self.client.force_authenticate(user=self.admin_user)

    def test_group_create_api(self):
        """测试用户组创建API"""
        url = reverse('group-list')
        data = {
            'name': '测试组',
            'users': [self.normal_user.id],
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['name'], '测试组')

    def test_group_add_users_api(self):
        """测试向用户组添加用户API"""
        group = Group.objects.create(name='测试组')
        
        url = reverse('group-add-users', kwargs={'pk': group.id})
        data = {
            'user_ids': [self.normal_user.id]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['added_count'], 1)

    def test_group_remove_users_api(self):
        """测试从用户组移除用户API"""
        group = Group.objects.create(name='测试组')
        group.user_set.add(self.normal_user)
        
        url = reverse('group-remove-users', kwargs={'pk': group.id})
        data = {
            'user_ids': [self.normal_user.id]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['removed_count'], 1)


class PermissionTest(TestCase):
    """权限测试"""

    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True
        )
        self.normal_user = User.objects.create_user(
            username='user',
            email='<EMAIL>',
            password='user123'
        )

    def test_normal_user_cannot_create_user(self):
        """测试普通用户不能创建用户"""
        self.client.force_authenticate(user=self.normal_user)
        
        url = reverse('user-list')
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'password123'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_normal_user_can_view_own_profile(self):
        """测试普通用户可以查看自己的信息"""
        self.client.force_authenticate(user=self.normal_user)
        
        url = reverse('user-detail', kwargs={'pk': self.normal_user.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
