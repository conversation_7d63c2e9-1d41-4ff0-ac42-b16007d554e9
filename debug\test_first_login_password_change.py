#!/usr/bin/env python
"""
测试首次登录密码修改功能
"""
import os
import sys
import django
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def test_first_login_password_change():
    """测试首次登录密码修改功能"""
    print("🔍 测试首次登录密码修改功能")
    print("=" * 60)
    
    try:
        from django.test import Client
        from django.contrib.auth import get_user_model
        from apps.users.models import User
        import secrets
        
        User = get_user_model()
        client = Client()
        
        # 1. 创建一个需要首次登录的测试用户
        print("1. 创建测试用户...")
        test_username = f"test_first_login_{secrets.token_hex(4)}"
        test_email = f"{test_username}@example.com"
        temp_token = secrets.token_urlsafe(32)
        
        test_user = User.objects.create_user(
            username=test_username,
            email=test_email,
            password="temp_password_123",
            name="首次登录测试用户",
            is_first_login=True,
            password_must_change=True,
            temp_password_token=temp_token
        )
        
        print(f"   ✅ 创建用户: {test_username}")
        print(f"   📧 邮箱: {test_email}")
        print(f"   🔑 临时令牌: {temp_token}")
        print(f"   🆔 用户ID: {test_user.id}")
        
        # 2. 验证用户状态
        print("\n2. 验证用户初始状态...")
        print(f"   is_first_login: {test_user.is_first_login}")
        print(f"   password_must_change: {test_user.password_must_change}")
        print(f"   temp_password_token: {test_user.temp_password_token}")
        print(f"   requires_password_change: {test_user.requires_password_change}")
        
        # 3. 测试首次登录密码修改API
        print("\n3. 测试首次登录密码修改API...")
        
        change_data = {
            "temp_token": temp_token,
            "user_id": test_user.id,
            "new_password": "NewPassword123!",
            "new_password_confirm": "NewPassword123!"
        }
        
        response = client.post(
            "/api/auth/password/first-login-change/",
            data=json.dumps(change_data),
            content_type="application/json"
        )
        
        print(f"   📡 请求URL: /api/auth/password/first-login-change/")
        print(f"   📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("   ✅ 密码修改成功")
            print(f"   🔑 获得访问令牌: {response_data.get('access_token', 'N/A')[:20]}...")
            print(f"   🔄 获得刷新令牌: {response_data.get('refresh_token', 'N/A')[:20]}...")
            print(f"   👤 用户信息: {response_data.get('user', {}).get('username', 'N/A')}")
            print(f"   💬 消息: {response_data.get('message', 'N/A')}")
            
            # 4. 验证用户状态是否正确更新
            print("\n4. 验证用户状态更新...")
            test_user.refresh_from_db()
            print(f"   is_first_login: {test_user.is_first_login} (应该是 False)")
            print(f"   password_must_change: {test_user.password_must_change} (应该是 False)")
            print(f"   temp_password_token: '{test_user.temp_password_token}' (应该是空)")
            print(f"   requires_password_change: {test_user.requires_password_change} (应该是 False)")
            print(f"   last_password_change: {test_user.last_password_change}")
            print(f"   password_expires_at: {test_user.password_expires_at}")
            
            # 验证密码是否正确更改
            if test_user.check_password("NewPassword123!"):
                print("   ✅ 密码已正确更改")
            else:
                print("   ❌ 密码更改失败")
            
            success = (
                not test_user.is_first_login and
                not test_user.password_must_change and
                test_user.temp_password_token == "" and
                not test_user.requires_password_change and
                test_user.check_password("NewPassword123!")
            )
            
            if success:
                print("   🎉 所有状态验证通过！")
            else:
                print("   ⚠️ 部分状态验证失败")
            
        else:
            print("   ❌ 密码修改失败")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data}")
            except:
                print(f"   响应内容: {response.content.decode()}")
            success = False
        
        # 5. 清理测试数据
        print("\n5. 清理测试数据...")
        test_user.delete()
        print("   ✅ 测试用户已删除")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_invalid_token():
    """测试无效令牌的情况"""
    print("\n🔍 测试无效令牌处理")
    print("=" * 60)
    
    try:
        from django.test import Client
        import json
        
        client = Client()
        
        # 使用无效的令牌
        invalid_data = {
            "temp_token": "invalid_token_123",
            "user_id": 999999,  # 不存在的用户ID
            "new_password": "NewPassword123!",
            "new_password_confirm": "NewPassword123!"
        }
        
        response = client.post(
            "/api/auth/password/first-login-change/",
            data=json.dumps(invalid_data),
            content_type="application/json"
        )
        
        print(f"   📊 响应状态码: {response.status_code}")
        
        if response.status_code == 400:
            error_data = response.json()
            print("   ✅ 正确拒绝无效令牌")
            print(f"   错误信息: {error_data}")
            return True
        else:
            print("   ❌ 应该返回400错误")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_with_provided_token():
    """使用提供的令牌进行测试"""
    print("\n🔍 测试提供的令牌")
    print("=" * 60)
    
    try:
        from django.test import Client
        from django.contrib.auth import get_user_model
        import json
        
        User = get_user_model()
        client = Client()
        
        # 查找具有指定令牌的用户
        provided_token = "SQZ2HNJvkA6maV-EGvlaab4Q25d50ZQCac0kaSqNg0M"
        
        try:
            user_with_token = User.objects.get(temp_password_token=provided_token)
            print(f"   ✅ 找到用户: {user_with_token.username} (ID: {user_with_token.id})")
            print(f"   📧 邮箱: {user_with_token.email}")
            print(f"   🔄 需要修改密码: {user_with_token.requires_password_change}")
            
            # 尝试使用这个令牌修改密码
            change_data = {
                "temp_token": provided_token,
                "user_id": user_with_token.id,
                "new_password": "NewPassword123!",
                "new_password_confirm": "NewPassword123!"
            }
            
            response = client.post(
                "/api/auth/password/first-login-change/",
                data=json.dumps(change_data),
                content_type="application/json"
            )
            
            print(f"   📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print("   ✅ 密码修改成功")
                print(f"   💬 消息: {response_data.get('message', 'N/A')}")
                return True
            else:
                print("   ❌ 密码修改失败")
                try:
                    error_data = response.json()
                    print(f"   错误信息: {error_data}")
                except:
                    print(f"   响应内容: {response.content.decode()}")
                return False
                
        except User.DoesNotExist:
            print(f"   ❌ 未找到具有令牌 {provided_token} 的用户")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试首次登录密码修改功能")
    print("=" * 80)
    
    tests = [
        test_first_login_password_change,
        test_invalid_token,
        test_with_provided_token,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            print("-" * 50)
    
    print("=" * 80)
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 首次登录密码修改功能测试通过！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
