from rest_framework import serializers
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema_field
from .models import BusinessOperationLog, PasswordAccessLog, SecurityEvent

User = get_user_model()


class BusinessOperationLogSerializer(serializers.ModelSerializer):
    """业务操作日志序列化器"""

    user_name = serializers.CharField(
        source="user.name", read_only=True, help_text="操作用户的姓名"
    )
    user_email = serializers.CharField(
        source="user.email", read_only=True, help_text="操作用户的邮箱地址"
    )
    action_display = serializers.CharField(
        source="get_action_type_display", read_only=True, help_text="操作类型的显示名称"
    )
    result_display = serializers.CharField(
        source="get_result_display", read_only=True, help_text="操作结果的显示名称"
    )

    class Meta:
        model = BusinessOperationLog
        fields = [
            "id",
            "user",
            "user_name",
            "user_email",
            "action_type",
            "action_display",
            "result",
            "result_display",
            "description",
            "target_type",
            "target_id",
            "target_name",
            "ip_address",
            "user_agent",
            "request_method",
            "request_path",
            "extra_data",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]


class PasswordAccessLogSerializer(serializers.ModelSerializer):
    """密码访问日志序列化器"""

    user_name = serializers.CharField(
        source="user.name", read_only=True, help_text="访问用户的姓名"
    )
    password_title = serializers.CharField(
        source="password_entry.title", read_only=True, help_text="密码条目标题"
    )
    access_display = serializers.CharField(
        source="get_access_type_display", read_only=True, help_text="访问类型的显示名称"
    )

    class Meta:
        model = PasswordAccessLog
        fields = [
            "id",
            "password_entry",
            "password_title",
            "user",
            "user_name",
            "access_type",
            "access_display",
            "ip_address",
            "user_agent",
            "access_source",
            "source_id",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]


class SecurityEventSerializer(serializers.ModelSerializer):
    """安全事件序列化器"""

    user_name = serializers.CharField(
        source="user.name", read_only=True, help_text="相关用户的姓名"
    )
    assigned_to_name = serializers.CharField(
        source="assigned_to.name", read_only=True, help_text="分配处理人的姓名"
    )
    event_type_display = serializers.CharField(
        source="get_event_type_display", read_only=True, help_text="事件类型的显示名称"
    )
    severity_display = serializers.CharField(
        source="get_severity_display", read_only=True, help_text="严重程度的显示名称"
    )
    status_display = serializers.CharField(
        source="get_status_display", read_only=True, help_text="状态的显示名称"
    )

    class Meta:
        model = SecurityEvent
        fields = [
            "id",
            "event_type",
            "event_type_display",
            "severity",
            "severity_display",
            "status",
            "status_display",
            "title",
            "description",
            "user",
            "user_name",
            "affected_resources",
            "event_data",
            "assigned_to",
            "assigned_to_name",
            "resolution_notes",
            "resolved_at",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class AuditLogEntrySerializer(serializers.Serializer):
    """Auditlog日志条目序列化器"""

    id = serializers.IntegerField(read_only=True)
    content_type = serializers.CharField(read_only=True)
    object_pk = serializers.CharField(read_only=True)
    object_id = serializers.IntegerField(read_only=True)
    object_repr = serializers.CharField(read_only=True)
    action = serializers.IntegerField(read_only=True)
    action_display = serializers.SerializerMethodField()
    changes = serializers.JSONField(read_only=True)
    actor = serializers.CharField(source="actor.username", read_only=True)
    actor_name = serializers.CharField(source="actor.name", read_only=True)
    remote_addr = serializers.IPAddressField(read_only=True)
    timestamp = serializers.DateTimeField(read_only=True)
    additional_data = serializers.JSONField(read_only=True)

    def get_action_display(self, obj):
        """获取操作类型显示名称"""
        action_map = {0: "创建", 1: "更新", 2: "删除"}
        return action_map.get(obj.action, "未知")


class AxesAccessAttemptSerializer(serializers.Serializer):
    """Axes访问尝试序列化器"""

    id = serializers.IntegerField(read_only=True)
    user_agent = serializers.CharField(read_only=True)
    ip_address = serializers.IPAddressField(read_only=True)
    username = serializers.CharField(read_only=True)
    http_accept = serializers.CharField(read_only=True)
    path_info = serializers.CharField(read_only=True)
    attempt_time = serializers.DateTimeField(read_only=True)
    get_data = serializers.CharField(read_only=True)
    post_data = serializers.CharField(read_only=True)
    failures_since_start = serializers.IntegerField(read_only=True)


class AxesAccessFailureLogSerializer(serializers.Serializer):
    """Axes访问失败日志序列化器"""

    id = serializers.IntegerField(read_only=True)
    user_agent = serializers.CharField(read_only=True)
    ip_address = serializers.IPAddressField(read_only=True)
    username = serializers.CharField(read_only=True)
    http_accept = serializers.CharField(read_only=True)
    path_info = serializers.CharField(read_only=True)
    attempt_time = serializers.DateTimeField(read_only=True)
    locked_out = serializers.BooleanField(read_only=True)


class AxesAccessLogSerializer(serializers.Serializer):
    """Axes访问日志序列化器"""

    id = serializers.IntegerField(read_only=True)
    user_agent = serializers.CharField(read_only=True)
    ip_address = serializers.IPAddressField(read_only=True)
    username = serializers.CharField(read_only=True)
    http_accept = serializers.CharField(read_only=True)
    path_info = serializers.CharField(read_only=True)
    attempt_time = serializers.DateTimeField(read_only=True)
    logout_time = serializers.DateTimeField(read_only=True)


class UnifiedAuditTrailSerializer(serializers.Serializer):
    """统一审计轨迹序列化器"""

    user = serializers.CharField(source="user.username", read_only=True)
    user_name = serializers.CharField(source="user.name", read_only=True)
    model_changes = AuditLogEntrySerializer(many=True, read_only=True)
    business_operations = BusinessOperationLogSerializer(many=True, read_only=True)
    security_events = SecurityEventSerializer(many=True, read_only=True)
    login_attempts = AxesAccessAttemptSerializer(many=True, read_only=True)


# 为了兼容现有代码，保留别名
OperationLogSerializer = BusinessOperationLogSerializer
AccessLogSerializer = PasswordAccessLogSerializer
