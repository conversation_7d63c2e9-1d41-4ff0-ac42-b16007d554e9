#!/usr/bin/env python
"""
简化审计日志调用，只保留必需字段
"""
import re

def simplify_audit_logs():
    """简化审计日志调用"""
    files_to_fix = [
        'apps/passwords/views.py',
        'apps/sharing/views.py',
        'apps/system/views.py'
    ]
    
    for file_path in files_to_fix:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 简化BusinessOperationLog调用，只保留必需字段
            # 匹配BusinessOperationLog.objects.create(...)的完整调用
            pattern = r'BusinessOperationLog\.objects\.create\(\s*([^)]+?)\s*\)'
            
            def replace_log_call(match):
                call_content = match.group(1)
                
                # 提取必需字段
                user_match = re.search(r'user=([^,\n]+)', call_content)
                action_type_match = re.search(r'action_type="([^"]+)"', call_content)
                target_type_match = re.search(r'target_type="([^"]+)"', call_content)
                target_id_match = re.search(r'target_id=([^,\n]+)', call_content)
                
                # 构建简化的调用
                simplified_call = "BusinessOperationLog.objects.create(\n"
                
                if user_match:
                    simplified_call += f"                user={user_match.group(1)},\n"
                
                if action_type_match:
                    simplified_call += f'                action_type="{action_type_match.group(1)}",\n'
                
                if target_type_match:
                    simplified_call += f'                target_type="{target_type_match.group(1)}",\n'
                
                if target_id_match:
                    simplified_call += f"                target_id={target_id_match.group(1)},\n"
                
                # 添加默认的IP地址
                simplified_call += '                ip_address="127.0.0.1",\n'
                simplified_call += '                extra_data={}\n'
                simplified_call += "            )"
                
                return simplified_call
            
            # 应用替换
            content = re.sub(pattern, replace_log_call, content, flags=re.DOTALL)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已简化 {file_path} 中的审计日志调用")
            
        except Exception as e:
            print(f"❌ 处理 {file_path} 时出错: {e}")

if __name__ == '__main__':
    simplify_audit_logs()
