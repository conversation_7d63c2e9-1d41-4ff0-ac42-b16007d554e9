from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class CategoriesConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.categories'
    verbose_name = _("分类管理")
    
    def ready(self):
        """应用准备就绪时的初始化操作"""
        # 导入信号处理器
        try:
            from . import signals
        except ImportError:
            pass
        
        # 初始化默认分类模板
        try:
            from .utils import initialize_default_templates
            initialize_default_templates()
        except ImportError:
            pass
        
        # 启动分类统计更新任务
        try:
            from .tasks import start_statistics_updater
            start_statistics_updater()
        except ImportError:
            pass