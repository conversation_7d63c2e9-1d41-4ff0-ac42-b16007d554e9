from django.urls import path
from . import views


# 分享链接相关URL
share_link_urlpatterns = [
    path(
        "share-links/",
        views.ShareLinkListCreateView.as_view(),
        name="share_link_list_create",
    ),
    path(
        "share-links/<int:pk>/",
        views.ShareLinkDetailView.as_view(),
        name="share_link_detail",
    ),
    path(
        "share-links/<int:pk>/stats/",
        views.ShareLinkStatsView.as_view(),
        name="share_link_stats",
    ),
]

# 公开访问URL（无需认证）
public_urlpatterns = [
    path("share/<str:token>/", views.share_link_access, name="share_link_access"),
]

# 已认证用户访问URL
authenticated_urlpatterns = [
    path(
        "share/<str:token>/auth/",
        views.AuthenticatedShareLinkAccessView.as_view(),
        name="authenticated_share_link_access",
    ),
]

urlpatterns = share_link_urlpatterns + public_urlpatterns + authenticated_urlpatterns
