openapi: 3.0.3
info:
  title: Password Locker API
  version: 1.0.0
  description: 密码管理系统API文档
paths:
  /audit/access-logs/:
    get:
      operationId: audit_access_logs_list
      description: 密码访问日志列表视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - audit
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPasswordAccessLogList'
          description: ''
  /audit/export/:
    post:
      operationId: audit_export_create
      description: 导出审计日志
      tags:
      - audit
      responses:
        '200':
          description: No response body
  /audit/operation-logs/:
    get:
      operationId: audit_operation_logs_list
      description: 获取系统操作日志列表，支持分页和过滤。非管理员用户只能查看自己的操作日志。
      summary: 获取操作日志列表
      parameters:
      - in: query
        name: action_type
        schema:
          type: string
        description: 操作类型过滤
      - in: query
        name: end_time
        schema:
          type: string
          format: date-time
        description: 结束时间过滤（ISO 8601格式）
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: query
        name: result
        schema:
          type: string
        description: 操作结果过滤（success/failure）
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      - in: query
        name: start_time
        schema:
          type: string
          format: date-time
        description: 开始时间过滤（ISO 8601格式）
      - in: query
        name: target_type
        schema:
          type: string
        description: 目标对象类型过滤
      tags:
      - 审计日志
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedOperationLogList'
          description: ''
  /audit/security-events/:
    get:
      operationId: audit_security_events_list
      description: 安全事件列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - audit
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedSecurityEventList'
          description: ''
    post:
      operationId: audit_security_events_create
      description: 安全事件列表和创建视图
      tags:
      - audit
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SecurityEventCreateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SecurityEventCreateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SecurityEventCreateRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecurityEventCreate'
          description: ''
  /audit/security-events/{id}/:
    get:
      operationId: audit_security_events_retrieve
      description: 安全事件详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - audit
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecurityEvent'
          description: ''
    put:
      operationId: audit_security_events_update
      description: 安全事件详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - audit
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SecurityEventRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SecurityEventRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SecurityEventRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecurityEvent'
          description: ''
    patch:
      operationId: audit_security_events_partial_update
      description: 安全事件详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - audit
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedSecurityEventRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedSecurityEventRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedSecurityEventRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecurityEvent'
          description: ''
  /audit/security-events/{id}/resolve/:
    post:
      operationId: audit_security_events_resolve_create
      description: 标记安全事件为已解决
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - audit
      responses:
        '200':
          description: No response body
  /audit/stats/:
    get:
      operationId: audit_stats_retrieve
      description: 获取审计统计信息
      tags:
      - audit
      responses:
        '200':
          description: No response body
  /audit/user-activity/:
    get:
      operationId: audit_user_activity_retrieve
      description: 获取用户活动信息
      tags:
      - audit
      responses:
        '200':
          description: No response body
  /audit/user-activity/{user_id}/:
    get:
      operationId: audit_user_activity_retrieve_2
      description: 获取用户活动信息
      parameters:
      - in: path
        name: user_id
        schema:
          type: integer
        required: true
      tags:
      - audit
      responses:
        '200':
          description: No response body
  /auth/codes/:
    get:
      operationId: auth_codes_retrieve
      description: 获取用户权限码视图
      tags:
      - auth
      responses:
        '200':
          description: No response body
  /auth/departments/:
    get:
      operationId: auth_departments_list
      description: 部门列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - auth
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDepartmentList'
          description: ''
    post:
      operationId: auth_departments_create
      description: 部门列表和创建视图
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DepartmentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DepartmentRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Department'
          description: ''
  /auth/departments/{id}/:
    get:
      operationId: auth_departments_retrieve
      description: 部门详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Department'
          description: ''
    put:
      operationId: auth_departments_update
      description: 部门详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DepartmentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DepartmentRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Department'
          description: ''
    patch:
      operationId: auth_departments_partial_update
      description: 部门详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDepartmentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDepartmentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDepartmentRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Department'
          description: ''
    delete:
      operationId: auth_departments_destroy
      description: 部门详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      responses:
        '204':
          description: No response body
  /auth/login/:
    post:
      operationId: auth_login_create
      description: 用户登录视图
      tags:
      - auth
      security:
      - {}
      responses:
        '200':
          description: No response body
  /auth/logout/:
    post:
      operationId: auth_logout_create
      description: 用户登出视图
      tags:
      - auth
      responses:
        '200':
          description: No response body
  /auth/menu/all/:
    get:
      operationId: auth_menu_all_retrieve
      description: 根据用户权限返回菜单列表
      tags:
      - auth
      responses:
        '200':
          description: No response body
  /auth/mfa/qrcode/:
    get:
      operationId: auth_mfa_qrcode_retrieve
      description: MFA二维码视图
      tags:
      - auth
      responses:
        '200':
          description: No response body
  /auth/mfa/setup/:
    post:
      operationId: auth_mfa_setup_create
      description: MFA设置视图
      tags:
      - auth
      responses:
        '200':
          description: No response body
  /auth/password/change/:
    post:
      operationId: auth_password_change_create
      description: 密码修改视图
      tags:
      - auth
      responses:
        '200':
          description: No response body
  /auth/password/reset/:
    post:
      operationId: auth_password_reset_create
      description: 密码重置请求视图
      tags:
      - auth
      security:
      - {}
      responses:
        '200':
          description: No response body
  /auth/password/reset/confirm/{uidb64}/{token}/:
    post:
      operationId: auth_password_reset_confirm_create
      description: 密码重置确认视图
      parameters:
      - in: path
        name: token
        schema:
          type: string
        required: true
      - in: path
        name: uidb64
        schema:
          type: string
        required: true
      tags:
      - auth
      security:
      - {}
      responses:
        '200':
          description: No response body
  /auth/profile/:
    get:
      operationId: auth_profile_retrieve
      description: 用户个人资料视图
      tags:
      - auth
      responses:
        '200':
          description: No response body
  /auth/roles/:
    get:
      operationId: auth_roles_list
      description: 角色列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - auth
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedRoleList'
          description: ''
    post:
      operationId: auth_roles_create
      description: 角色列表和创建视图
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/RoleRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/RoleRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
          description: ''
  /auth/roles/{id}/:
    get:
      operationId: auth_roles_retrieve
      description: 角色详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
          description: ''
    put:
      operationId: auth_roles_update
      description: 角色详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/RoleRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/RoleRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
          description: ''
    patch:
      operationId: auth_roles_partial_update
      description: 角色详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedRoleRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedRoleRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedRoleRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
          description: ''
    delete:
      operationId: auth_roles_destroy
      description: 角色详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      responses:
        '204':
          description: No response body
  /auth/teams/:
    get:
      operationId: auth_teams_list
      description: 团队列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - auth
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTeamList'
          description: ''
    post:
      operationId: auth_teams_create
      description: 团队列表和创建视图
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TeamRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TeamRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TeamRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
          description: ''
  /auth/teams/{id}/:
    get:
      operationId: auth_teams_retrieve
      description: 团队详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
          description: ''
    put:
      operationId: auth_teams_update
      description: 团队详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TeamRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TeamRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TeamRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
          description: ''
    patch:
      operationId: auth_teams_partial_update
      description: 团队详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedTeamRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedTeamRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedTeamRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
          description: ''
    delete:
      operationId: auth_teams_destroy
      description: 团队详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      responses:
        '204':
          description: No response body
  /auth/token/refresh/:
    post:
      operationId: auth_token_refresh_create
      description: 自定义令牌刷新视图
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenRefresh'
          description: ''
  /auth/users/:
    get:
      operationId: auth_users_list
      description: 用户列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - auth
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserList'
          description: ''
    post:
      operationId: auth_users_create
      description: 用户列表和创建视图
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserCreateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserCreateRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserCreate'
          description: ''
  /auth/users/{id}/:
    get:
      operationId: auth_users_retrieve
      description: 用户详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    put:
      operationId: auth_users_update
      description: 用户详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    patch:
      operationId: auth_users_partial_update
      description: 用户详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    delete:
      operationId: auth_users_destroy
      description: 用户详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - auth
      responses:
        '204':
          description: No response body
  /docs/legacy/:
    get:
      operationId: docs_legacy_retrieve
      description: API文档视图 - 返回所有可用的API端点
      tags:
      - docs
      responses:
        '200':
          description: No response body
  /passwords/attachments/{id}/:
    get:
      operationId: passwords_attachments_retrieve
      description: 附件详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Attachment'
          description: ''
    put:
      operationId: passwords_attachments_update
      description: 附件详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AttachmentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AttachmentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AttachmentRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Attachment'
          description: ''
    patch:
      operationId: passwords_attachments_partial_update
      description: 附件详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedAttachmentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedAttachmentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedAttachmentRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Attachment'
          description: ''
    delete:
      operationId: passwords_attachments_destroy
      description: 附件详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '204':
          description: No response body
  /passwords/batch-delete/:
    post:
      operationId: passwords_batch_delete_create
      description: 批量删除密码（移动到回收站）
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/categories/:
    get:
      operationId: passwords_categories_list
      description: 分类列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedCategoryList'
          description: ''
    post:
      operationId: passwords_categories_create
      description: 分类列表和创建视图
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoryRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CategoryRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CategoryRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
          description: ''
  /passwords/categories/{id}/:
    get:
      operationId: passwords_categories_retrieve
      description: 分类详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
          description: ''
    put:
      operationId: passwords_categories_update
      description: 分类详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoryRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CategoryRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CategoryRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
          description: ''
    patch:
      operationId: passwords_categories_partial_update
      description: 分类详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCategoryRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCategoryRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCategoryRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Category'
          description: ''
    delete:
      operationId: passwords_categories_destroy
      description: 分类详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '204':
          description: No response body
  /passwords/custom-fields/{id}/:
    get:
      operationId: passwords_custom_fields_retrieve
      description: 自定义字段详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomField'
          description: ''
    put:
      operationId: passwords_custom_fields_update
      description: 自定义字段详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomFieldRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CustomFieldRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CustomFieldRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomField'
          description: ''
    patch:
      operationId: passwords_custom_fields_partial_update
      description: 自定义字段详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCustomFieldRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCustomFieldRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCustomFieldRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomField'
          description: ''
    delete:
      operationId: passwords_custom_fields_destroy
      description: 自定义字段详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '204':
          description: No response body
  /passwords/database-types/:
    get:
      operationId: passwords_database_types_retrieve
      description: 获取数据库类型选项
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/environments/:
    get:
      operationId: passwords_environments_retrieve
      description: 获取环境类型选项
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/group-memberships/:
    get:
      operationId: passwords_group_memberships_list
      description: 密码条目组成员关系列表和创建视图
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPasswordEntryGroupMembershipList'
          description: ''
    post:
      operationId: passwords_group_memberships_create
      description: 密码条目组成员关系列表和创建视图
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordEntryGroupMembershipRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordEntryGroupMembershipRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordEntryGroupMembershipRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordEntryGroupMembership'
          description: ''
  /passwords/group-memberships/{id}/:
    get:
      operationId: passwords_group_memberships_retrieve
      description: 密码条目组成员关系详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordEntryGroupMembership'
          description: ''
    delete:
      operationId: passwords_group_memberships_destroy
      description: 密码条目组成员关系详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '204':
          description: No response body
  /passwords/group-permissions/:
    get:
      operationId: passwords_group_permissions_list
      description: 组权限列表和创建视图
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedGroupPermissionList'
          description: ''
    post:
      operationId: passwords_group_permissions_create
      description: 组权限列表和创建视图
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupPermissionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/GroupPermissionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/GroupPermissionRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupPermission'
          description: ''
  /passwords/group-permissions/{id}/:
    get:
      operationId: passwords_group_permissions_retrieve
      description: 组权限详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupPermission'
          description: ''
    put:
      operationId: passwords_group_permissions_update
      description: 组权限详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupPermissionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/GroupPermissionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/GroupPermissionRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupPermission'
          description: ''
    patch:
      operationId: passwords_group_permissions_partial_update
      description: 组权限详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedGroupPermissionRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedGroupPermissionRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedGroupPermissionRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupPermission'
          description: ''
    delete:
      operationId: passwords_group_permissions_destroy
      description: 组权限详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '204':
          description: No response body
  /passwords/groups/:
    get:
      operationId: passwords_groups_list
      description: 密码组列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPasswordEntryGroupList'
          description: ''
    post:
      operationId: passwords_groups_create
      description: 密码组列表和创建视图
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordEntryGroupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordEntryGroupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordEntryGroupRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordEntryGroup'
          description: ''
  /passwords/groups/{id}/:
    get:
      operationId: passwords_groups_retrieve
      description: 密码组详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordEntryGroup'
          description: ''
    put:
      operationId: passwords_groups_update
      description: 密码组详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordEntryGroupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordEntryGroupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordEntryGroupRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordEntryGroup'
          description: ''
    patch:
      operationId: passwords_groups_partial_update
      description: 密码组详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPasswordEntryGroupRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPasswordEntryGroupRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPasswordEntryGroupRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordEntryGroup'
          description: ''
    delete:
      operationId: passwords_groups_destroy
      description: 密码组详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '204':
          description: No response body
  /passwords/mdw-types/:
    get:
      operationId: passwords_mdw_types_retrieve
      description: 获取中间件类型选项
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/os-types/:
    get:
      operationId: passwords_os_types_retrieve
      description: 获取操作系统类型选项
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/password-generator/:
    post:
      operationId: passwords_password_generator_create
      description: 密码生成器视图
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/password-policies/:
    get:
      operationId: passwords_password_policies_list
      description: 密码策略列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPasswordPolicyList'
          description: ''
    post:
      operationId: passwords_password_policies_create
      description: 密码策略列表和创建视图
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordPolicyRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordPolicyRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordPolicyRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordPolicy'
          description: ''
  /passwords/password-policies/{id}/:
    get:
      operationId: passwords_password_policies_retrieve
      description: 密码策略详情、更新和删除视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordPolicy'
          description: ''
    put:
      operationId: passwords_password_policies_update
      description: 密码策略详情、更新和删除视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordPolicyRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordPolicyRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordPolicyRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordPolicy'
          description: ''
    patch:
      operationId: passwords_password_policies_partial_update
      description: 密码策略详情、更新和删除视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPasswordPolicyRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPasswordPolicyRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPasswordPolicyRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordPolicy'
          description: ''
    delete:
      operationId: passwords_password_policies_destroy
      description: 密码策略详情、更新和删除视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - passwords
      responses:
        '204':
          description: No response body
  /passwords/password-policies/generate/:
    post:
      operationId: passwords_password_policies_generate_create
      description: 根据密码策略生成密码
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/password-policies/validate/:
    post:
      operationId: passwords_password_policies_validate_create
      description: 根据密码策略验证密码
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/passwords/:
    get:
      operationId: passwords_passwords_list
      description: 密码条目列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPasswordEntryList'
          description: ''
    post:
      operationId: passwords_passwords_create
      description: 密码条目列表和创建视图
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordEntryCreateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordEntryCreateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordEntryCreateRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordEntryCreate'
          description: ''
  /passwords/passwords/{password_entry_id}/attachments/:
    get:
      operationId: passwords_passwords_attachments_list
      description: 附件列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: path
        name: password_entry_id
        schema:
          type: string
          format: uuid
        required: true
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAttachmentList'
          description: ''
    post:
      operationId: passwords_passwords_attachments_create
      description: 附件列表和创建视图
      parameters:
      - in: path
        name: password_entry_id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AttachmentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AttachmentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AttachmentRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Attachment'
          description: ''
  /passwords/passwords/{password_entry_id}/custom-fields/:
    get:
      operationId: passwords_passwords_custom_fields_list
      description: 自定义字段列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: path
        name: password_entry_id
        schema:
          type: string
          format: uuid
        required: true
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedCustomFieldList'
          description: ''
    post:
      operationId: passwords_passwords_custom_fields_create
      description: 自定义字段列表和创建视图
      parameters:
      - in: path
        name: password_entry_id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomFieldRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CustomFieldRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CustomFieldRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomField'
          description: ''
  /passwords/passwords/{id}/:
    get:
      operationId: passwords_passwords_retrieve
      description: 密码条目详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordEntryDetail'
          description: ''
    put:
      operationId: passwords_passwords_update
      description: 密码条目详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordEntryDetailRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordEntryDetailRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordEntryDetailRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordEntryDetail'
          description: ''
    patch:
      operationId: passwords_passwords_partial_update
      description: 密码条目详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - passwords
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPasswordEntryDetailRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPasswordEntryDetailRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPasswordEntryDetailRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordEntryDetail'
          description: ''
    delete:
      operationId: passwords_passwords_destroy
      description: 密码条目详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - passwords
      responses:
        '204':
          description: No response body
  /passwords/passwords/{id}/copy/:
    post:
      operationId: passwords_passwords_copy_create
      description: 密码复制视图（记录复制操作）
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/passwords/{id}/toggle-favorite/:
    post:
      operationId: passwords_passwords_toggle_favorite_create
      description: 密码收藏切换视图
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/passwords/{id}/update-info/:
    post:
      operationId: passwords_passwords_update_info_create
      description: 仅更新密码信息字段
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/passwords/{id}/update-password/:
    post:
      operationId: passwords_passwords_update_password_create
      description: 仅更新密码字段
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/protocols/:
    get:
      operationId: passwords_protocols_retrieve
      description: 获取协议类型选项
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/recycle-bin/:
    get:
      operationId: passwords_recycle_bin_list
      description: 回收站列表视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPasswordEntryList'
          description: ''
  /passwords/recycle-bin/{id}/permanent-delete/:
    delete:
      operationId: passwords_recycle_bin_permanent_delete_destroy
      description: 永久删除密码
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - passwords
      responses:
        '204':
          description: No response body
  /passwords/recycle-bin/{id}/restore/:
    post:
      operationId: passwords_recycle_bin_restore_create
      description: 恢复已删除的密码
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/security-analysis/:
    get:
      operationId: passwords_security_analysis_retrieve
      description: 密码安全分析视图
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/stats/:
    get:
      operationId: passwords_stats_retrieve
      description: 获取系统统计信息
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/system-types/:
    get:
      operationId: passwords_system_types_retrieve
      description: 获取系统类型选项
      tags:
      - passwords
      responses:
        '200':
          description: No response body
  /passwords/systems/:
    get:
      operationId: passwords_systems_list
      description: 系统密码列表视图 - 支持运维专业筛选
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - passwords
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPasswordEntryList'
          description: ''
  /sharing/share-links/:
    get:
      operationId: sharing_share_links_list
      description: 分享链接列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - sharing
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedShareLinkList'
          description: ''
    post:
      operationId: sharing_share_links_create
      description: 分享链接列表和创建视图
      tags:
      - sharing
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ShareLinkRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ShareLinkRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ShareLinkRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShareLink'
          description: ''
  /sharing/share-links/{id}/:
    get:
      operationId: sharing_share_links_retrieve
      description: 分享链接详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - sharing
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShareLink'
          description: ''
    put:
      operationId: sharing_share_links_update
      description: 分享链接详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - sharing
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ShareLinkRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ShareLinkRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ShareLinkRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShareLink'
          description: ''
    patch:
      operationId: sharing_share_links_partial_update
      description: 分享链接详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - sharing
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedShareLinkRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedShareLinkRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedShareLinkRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShareLink'
          description: ''
    delete:
      operationId: sharing_share_links_destroy
      description: 分享链接详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - sharing
      responses:
        '204':
          description: No response body
  /sharing/share-links/{id}/stats/:
    get:
      operationId: sharing_share_links_stats_retrieve
      description: 获取分享链接统计信息
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - sharing
      responses:
        '200':
          description: No response body
  /sharing/share/{token}/:
    get:
      operationId: sharing_share_retrieve
      description: 分享链接访问接口
      parameters:
      - in: path
        name: token
        schema:
          type: string
        required: true
      tags:
      - sharing
      security:
      - {}
      responses:
        '200':
          description: No response body
    post:
      operationId: sharing_share_create
      description: 分享链接访问接口
      parameters:
      - in: path
        name: token
        schema:
          type: string
        required: true
      tags:
      - sharing
      security:
      - {}
      responses:
        '200':
          description: No response body
  /sharing/share/{token}/auth/:
    post:
      operationId: sharing_share_auth_create
      description: 已认证用户访问分享链接获取密码
      parameters:
      - in: path
        name: token
        schema:
          type: string
        required: true
      tags:
      - sharing
      responses:
        '200':
          description: No response body
  /system/backup-configs/:
    get:
      operationId: system_backup_configs_list
      description: 备份配置列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - system
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedBackupConfigList'
          description: ''
    post:
      operationId: system_backup_configs_create
      description: 备份配置列表和创建视图
      tags:
      - system
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BackupConfigRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/BackupConfigRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/BackupConfigRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackupConfig'
          description: ''
  /system/backup-configs/{id}/:
    get:
      operationId: system_backup_configs_retrieve
      description: 备份配置详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - system
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackupConfig'
          description: ''
    put:
      operationId: system_backup_configs_update
      description: 备份配置详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - system
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BackupConfigRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/BackupConfigRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/BackupConfigRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackupConfig'
          description: ''
    patch:
      operationId: system_backup_configs_partial_update
      description: 备份配置详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - system
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedBackupConfigRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedBackupConfigRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedBackupConfigRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackupConfig'
          description: ''
    delete:
      operationId: system_backup_configs_destroy
      description: 备份配置详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - system
      responses:
        '204':
          description: No response body
  /system/email-templates/:
    get:
      operationId: system_email_templates_list
      description: 邮件模板列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - system
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedEmailTemplateList'
          description: ''
    post:
      operationId: system_email_templates_create
      description: 邮件模板列表和创建视图
      tags:
      - system
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailTemplateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailTemplateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailTemplateRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailTemplate'
          description: ''
  /system/email-templates/{id}/:
    get:
      operationId: system_email_templates_retrieve
      description: 邮件模板详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - system
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailTemplate'
          description: ''
    put:
      operationId: system_email_templates_update
      description: 邮件模板详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - system
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailTemplateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailTemplateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailTemplateRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailTemplate'
          description: ''
    patch:
      operationId: system_email_templates_partial_update
      description: 邮件模板详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - system
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedEmailTemplateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedEmailTemplateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedEmailTemplateRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailTemplate'
          description: ''
    delete:
      operationId: system_email_templates_destroy
      description: 邮件模板详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - system
      responses:
        '204':
          description: No response body
  /system/maintenance/:
    post:
      operationId: system_maintenance_create
      description: 执行系统维护操作
      tags:
      - system
      responses:
        '200':
          description: No response body
  /system/settings/:
    get:
      operationId: system_settings_list
      description: 获取系统设置列表，非管理员只能查看公开设置
      summary: 获取系统设置列表
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - 系统设置
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedSystemSettingList'
          description: ''
  /system/settings/{key}/:
    get:
      operationId: system_settings_retrieve
      description: 系统设置详情视图
      parameters:
      - in: path
        name: key
        schema:
          type: string
        required: true
      tags:
      - system
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemSetting'
          description: ''
    put:
      operationId: system_settings_update
      description: 系统设置详情视图
      parameters:
      - in: path
        name: key
        schema:
          type: string
        required: true
      tags:
      - system
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SystemSettingRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SystemSettingRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SystemSettingRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemSetting'
          description: ''
    patch:
      operationId: system_settings_partial_update
      description: 系统设置详情视图
      parameters:
      - in: path
        name: key
        schema:
          type: string
        required: true
      tags:
      - system
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedSystemSettingRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedSystemSettingRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedSystemSettingRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemSetting'
          description: ''
  /system/settings/batch/update/:
    post:
      operationId: system_settings_batch_update_create
      description: 批量更新系统设置
      tags:
      - system
      responses:
        '200':
          description: No response body
  /system/status/:
    get:
      operationId: system_status_retrieve
      description: 获取系统状态信息
      tags:
      - system
      responses:
        '200':
          description: No response body
  /users/codes/:
    get:
      operationId: users_codes_retrieve
      description: 获取用户权限码视图
      tags:
      - users
      responses:
        '200':
          description: No response body
  /users/departments/:
    get:
      operationId: users_departments_list
      description: 部门列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - users
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDepartmentList'
          description: ''
    post:
      operationId: users_departments_create
      description: 部门列表和创建视图
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DepartmentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DepartmentRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Department'
          description: ''
  /users/departments/{id}/:
    get:
      operationId: users_departments_retrieve
      description: 部门详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Department'
          description: ''
    put:
      operationId: users_departments_update
      description: 部门详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepartmentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DepartmentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DepartmentRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Department'
          description: ''
    patch:
      operationId: users_departments_partial_update
      description: 部门详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDepartmentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDepartmentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDepartmentRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Department'
          description: ''
    delete:
      operationId: users_departments_destroy
      description: 部门详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      responses:
        '204':
          description: No response body
  /users/login/:
    post:
      operationId: users_login_create
      description: 用户登录视图
      tags:
      - users
      security:
      - {}
      responses:
        '200':
          description: No response body
  /users/logout/:
    post:
      operationId: users_logout_create
      description: 用户登出视图
      tags:
      - users
      responses:
        '200':
          description: No response body
  /users/menu/all/:
    get:
      operationId: users_menu_all_retrieve
      description: 根据用户权限返回菜单列表
      tags:
      - users
      responses:
        '200':
          description: No response body
  /users/mfa/qrcode/:
    get:
      operationId: users_mfa_qrcode_retrieve
      description: MFA二维码视图
      tags:
      - users
      responses:
        '200':
          description: No response body
  /users/mfa/setup/:
    post:
      operationId: users_mfa_setup_create
      description: MFA设置视图
      tags:
      - users
      responses:
        '200':
          description: No response body
  /users/password/change/:
    post:
      operationId: users_password_change_create
      description: 密码修改视图
      tags:
      - users
      responses:
        '200':
          description: No response body
  /users/password/reset/:
    post:
      operationId: users_password_reset_create
      description: 密码重置请求视图
      tags:
      - users
      security:
      - {}
      responses:
        '200':
          description: No response body
  /users/password/reset/confirm/{uidb64}/{token}/:
    post:
      operationId: users_password_reset_confirm_create
      description: 密码重置确认视图
      parameters:
      - in: path
        name: token
        schema:
          type: string
        required: true
      - in: path
        name: uidb64
        schema:
          type: string
        required: true
      tags:
      - users
      security:
      - {}
      responses:
        '200':
          description: No response body
  /users/profile/:
    get:
      operationId: users_profile_retrieve
      description: 用户个人资料视图
      tags:
      - users
      responses:
        '200':
          description: No response body
  /users/roles/:
    get:
      operationId: users_roles_list
      description: 角色列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - users
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedRoleList'
          description: ''
    post:
      operationId: users_roles_create
      description: 角色列表和创建视图
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/RoleRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/RoleRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
          description: ''
  /users/roles/{id}/:
    get:
      operationId: users_roles_retrieve
      description: 角色详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
          description: ''
    put:
      operationId: users_roles_update
      description: 角色详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/RoleRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/RoleRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
          description: ''
    patch:
      operationId: users_roles_partial_update
      description: 角色详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedRoleRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedRoleRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedRoleRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
          description: ''
    delete:
      operationId: users_roles_destroy
      description: 角色详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      responses:
        '204':
          description: No response body
  /users/teams/:
    get:
      operationId: users_teams_list
      description: 团队列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - users
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTeamList'
          description: ''
    post:
      operationId: users_teams_create
      description: 团队列表和创建视图
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TeamRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TeamRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TeamRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
          description: ''
  /users/teams/{id}/:
    get:
      operationId: users_teams_retrieve
      description: 团队详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
          description: ''
    put:
      operationId: users_teams_update
      description: 团队详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TeamRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TeamRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TeamRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
          description: ''
    patch:
      operationId: users_teams_partial_update
      description: 团队详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedTeamRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedTeamRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedTeamRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
          description: ''
    delete:
      operationId: users_teams_destroy
      description: 团队详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      responses:
        '204':
          description: No response body
  /users/token/refresh/:
    post:
      operationId: users_token_refresh_create
      description: 自定义令牌刷新视图
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenRefresh'
          description: ''
  /users/users/:
    get:
      operationId: users_users_list
      description: 用户列表和创建视图
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - users
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserList'
          description: ''
    post:
      operationId: users_users_create
      description: 用户列表和创建视图
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserCreateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserCreateRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserCreate'
          description: ''
  /users/users/{id}/:
    get:
      operationId: users_users_retrieve
      description: 用户详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    put:
      operationId: users_users_update
      description: 用户详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    patch:
      operationId: users_users_partial_update
      description: 用户详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    delete:
      operationId: users_users_destroy
      description: 用户详情视图
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      responses:
        '204':
          description: No response body
components:
  schemas:
    Attachment:
      type: object
      description: 附件序列化器
      properties:
        id:
          type: integer
          readOnly: true
        file_name:
          type: string
          title: 文件名
          maxLength: 255
        file_path:
          type: string
          title: 文件路径
          maxLength: 500
        file_size:
          type: integer
          readOnly: true
          title: 文件大小
        file_size_display:
          type: string
          readOnly: true
        content_type:
          type: string
          title: 文件类型
          maxLength: 100
        password_entry:
          type: string
          format: uuid
          title: 密码条目
        uploaded_by:
          type: integer
          title: 上传用户
        uploaded_by_name:
          type: string
          readOnly: true
        uploaded_at:
          type: string
          format: date-time
          readOnly: true
          title: 上传时间
      required:
      - content_type
      - file_name
      - file_path
      - file_size
      - file_size_display
      - id
      - password_entry
      - uploaded_at
      - uploaded_by
      - uploaded_by_name
    AttachmentRequest:
      type: object
      description: 附件序列化器
      properties:
        file_name:
          type: string
          minLength: 1
          title: 文件名
          maxLength: 255
        file_path:
          type: string
          minLength: 1
          title: 文件路径
          maxLength: 500
        content_type:
          type: string
          minLength: 1
          title: 文件类型
          maxLength: 100
        password_entry:
          type: string
          format: uuid
          title: 密码条目
        uploaded_by:
          type: integer
          title: 上传用户
      required:
      - content_type
      - file_name
      - file_path
      - password_entry
      - uploaded_by
    BackupConfig:
      type: object
      description: 备份配置序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 备份名称
          maxLength: 200
        description:
          type: string
          title: 备份描述
        backup_type:
          enum:
          - full
          - incremental
          - differential
          - manual
          type: string
          description: |-
            * `full` - 完整备份
            * `incremental` - 增量备份
            * `differential` - 差异备份
            * `manual` - 手动备份
          x-spec-enum-id: f233531d2df4cfb9
          title: 备份类型
        status:
          enum:
          - pending
          - running
          - completed
          - failed
          - cancelled
          type: string
          description: |-
            * `pending` - 等待中
            * `running` - 运行中
            * `completed` - 已完成
            * `failed` - 失败
            * `cancelled` - 已取消
          x-spec-enum-id: fb752eb40d2b5767
          readOnly: true
          title: 备份状态
        include_passwords:
          type: boolean
          title: 包含密码
        include_categories:
          type: boolean
          title: 包含分类
        include_users:
          type: boolean
          title: 包含用户
        include_settings:
          type: boolean
          title: 包含设置
        include_logs:
          type: boolean
          title: 包含日志
        include_attachments:
          type: boolean
          title: 包含附件
        storage_type:
          enum:
          - local
          - s3
          - azure
          - gcs
          - ftp
          - sftp
          type: string
          description: |-
            * `local` - 本地存储
            * `s3` - Amazon S3
            * `azure` - Azure Blob
            * `gcs` - Google Cloud Storage
            * `ftp` - FTP服务器
            * `sftp` - SFTP服务器
          x-spec-enum-id: 1033db16b067d6b2
          title: 存储类型
        storage_path:
          type: string
          title: 存储路径
          maxLength: 500
        file_name:
          type: string
          title: 文件名
          maxLength: 255
        file_size:
          type: integer
          readOnly: true
          nullable: true
          title: 文件大小
        file_size_display:
          type: string
          readOnly: true
        is_encrypted:
          type: boolean
          title: 是否加密
        is_compressed:
          type: boolean
          title: 是否压缩
        created_by:
          type: integer
          readOnly: true
          nullable: true
          title: 创建者
        created_by_name:
          type: string
          readOnly: true
        started_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 开始时间
        completed_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 完成时间
        error_message:
          type: string
          readOnly: true
          title: 错误信息
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - completed_at
      - created_at
      - created_by
      - created_by_name
      - error_message
      - file_size
      - file_size_display
      - id
      - name
      - started_at
      - status
      - storage_path
      - updated_at
    BackupConfigRequest:
      type: object
      description: 备份配置序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 备份名称
          maxLength: 200
        description:
          type: string
          title: 备份描述
        backup_type:
          enum:
          - full
          - incremental
          - differential
          - manual
          type: string
          description: |-
            * `full` - 完整备份
            * `incremental` - 增量备份
            * `differential` - 差异备份
            * `manual` - 手动备份
          x-spec-enum-id: f233531d2df4cfb9
          title: 备份类型
        include_passwords:
          type: boolean
          title: 包含密码
        include_categories:
          type: boolean
          title: 包含分类
        include_users:
          type: boolean
          title: 包含用户
        include_settings:
          type: boolean
          title: 包含设置
        include_logs:
          type: boolean
          title: 包含日志
        include_attachments:
          type: boolean
          title: 包含附件
        storage_type:
          enum:
          - local
          - s3
          - azure
          - gcs
          - ftp
          - sftp
          type: string
          description: |-
            * `local` - 本地存储
            * `s3` - Amazon S3
            * `azure` - Azure Blob
            * `gcs` - Google Cloud Storage
            * `ftp` - FTP服务器
            * `sftp` - SFTP服务器
          x-spec-enum-id: 1033db16b067d6b2
          title: 存储类型
        storage_path:
          type: string
          minLength: 1
          title: 存储路径
          maxLength: 500
        file_name:
          type: string
          title: 文件名
          maxLength: 255
        is_encrypted:
          type: boolean
          title: 是否加密
        is_compressed:
          type: boolean
          title: 是否压缩
      required:
      - name
      - storage_path
    Category:
      type: object
      description: 分类序列化器
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          title: 分类名称
          maxLength: 100
        description:
          type: string
          title: 分类描述
        icon:
          type: string
          title: 图标
          maxLength: 50
        color:
          type: string
          title: 颜色
          maxLength: 7
        user:
          type: integer
          readOnly: true
          title: 创建用户
        password_count:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - id
      - name
      - password_count
      - updated_at
      - user
    CategoryRequest:
      type: object
      description: 分类序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 分类名称
          maxLength: 100
        description:
          type: string
          title: 分类描述
        icon:
          type: string
          title: 图标
          maxLength: 50
        color:
          type: string
          minLength: 1
          title: 颜色
          maxLength: 7
      required:
      - name
    CustomField:
      type: object
      description: 自定义字段序列化器
      properties:
        id:
          type: integer
          readOnly: true
        field_name:
          type: string
          title: 字段名称
          maxLength: 100
        field_type:
          enum:
          - text
          - password
          - email
          - url
          - phone
          - date
          - number
          type: string
          description: |-
            * `text` - 文本
            * `password` - 密码
            * `email` - 邮箱
            * `url` - 网址
            * `phone` - 电话
            * `date` - 日期
            * `number` - 数字
          x-spec-enum-id: fcf5e1c4b3332285
          title: 字段类型
        field_value:
          type: string
          title: 字段值
        is_sensitive:
          type: boolean
          title: 敏感数据
        order:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
          title: 排序
        password_entry:
          type: string
          format: uuid
          title: 密码条目
      required:
      - field_name
      - field_value
      - id
      - password_entry
    CustomFieldRequest:
      type: object
      description: 自定义字段序列化器
      properties:
        field_name:
          type: string
          minLength: 1
          title: 字段名称
          maxLength: 100
        field_type:
          enum:
          - text
          - password
          - email
          - url
          - phone
          - date
          - number
          type: string
          description: |-
            * `text` - 文本
            * `password` - 密码
            * `email` - 邮箱
            * `url` - 网址
            * `phone` - 电话
            * `date` - 日期
            * `number` - 数字
          x-spec-enum-id: fcf5e1c4b3332285
          title: 字段类型
        field_value:
          type: string
          minLength: 1
          title: 字段值
        is_sensitive:
          type: boolean
          title: 敏感数据
        order:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
          title: 排序
        password_entry:
          type: string
          format: uuid
          title: 密码条目
      required:
      - field_name
      - field_value
      - password_entry
    Department:
      type: object
      description: 部门序列化器
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          title: 部门名称
          maxLength: 100
        description:
          type: string
          title: 部门描述
        parent:
          type: integer
          nullable: true
          title: 上级部门
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - id
      - name
      - updated_at
    DepartmentRequest:
      type: object
      description: 部门序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 部门名称
          maxLength: 100
        description:
          type: string
          title: 部门描述
        parent:
          type: integer
          nullable: true
          title: 上级部门
      required:
      - name
    EmailTemplate:
      type: object
      description: 邮件模板序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          title: 模板名称
          maxLength: 200
        subject:
          type: string
          title: 邮件主题
          maxLength: 200
        content:
          type: string
          title: 邮件内容
        template_type:
          enum:
          - welcome
          - password_reset
          - account_locked
          - security_alert
          - backup_notification
          - system_maintenance
          - custom
          type: string
          description: |-
            * `welcome` - 欢迎邮件
            * `password_reset` - 密码重置
            * `account_locked` - 账户锁定
            * `security_alert` - 安全警报
            * `backup_notification` - 备份通知
            * `system_maintenance` - 系统维护
            * `custom` - 自定义
          x-spec-enum-id: 812fa624cc399c2a
          title: 模板类型
        is_active:
          type: boolean
          title: 是否启用
        is_default:
          type: boolean
          default: false
          title: 默认模板
        created_by:
          type: integer
          readOnly: true
          nullable: true
          title: 创建者
        created_by_name:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - content
      - created_at
      - created_by
      - created_by_name
      - id
      - name
      - subject
      - template_type
      - updated_at
    EmailTemplateRequest:
      type: object
      description: 邮件模板序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 模板名称
          maxLength: 200
        subject:
          type: string
          minLength: 1
          title: 邮件主题
          maxLength: 200
        content:
          type: string
          minLength: 1
          title: 邮件内容
        template_type:
          enum:
          - welcome
          - password_reset
          - account_locked
          - security_alert
          - backup_notification
          - system_maintenance
          - custom
          type: string
          description: |-
            * `welcome` - 欢迎邮件
            * `password_reset` - 密码重置
            * `account_locked` - 账户锁定
            * `security_alert` - 安全警报
            * `backup_notification` - 备份通知
            * `system_maintenance` - 系统维护
            * `custom` - 自定义
          x-spec-enum-id: 812fa624cc399c2a
          title: 模板类型
        is_active:
          type: boolean
          title: 是否启用
        is_default:
          type: boolean
          default: false
          title: 默认模板
      required:
      - content
      - name
      - subject
      - template_type
    GroupPermission:
      type: object
      description: 组权限序列化器
      properties:
        id:
          type: integer
          readOnly: true
        user:
          type: integer
          title: 用户
        username:
          type: string
          readOnly: true
        user_email:
          type: string
          readOnly: true
        group:
          type: integer
          title: 密码组
        group_name:
          type: string
          readOnly: true
        permission:
          enum:
          - view
          - edit
          - manage
          - admin
          type: string
          description: |-
            * `view` - 查看
            * `edit` - 编辑
            * `manage` - 管理
            * `admin` - 管理员
          x-spec-enum-id: 9332315630403d50
          title: 权限级别
        permission_display:
          type: string
          readOnly: true
        granted_by:
          type: integer
          readOnly: true
          title: 授权者
        granted_by_username:
          type: string
          readOnly: true
        granted_at:
          type: string
          format: date-time
          readOnly: true
          title: 授权时间
      required:
      - granted_at
      - granted_by
      - granted_by_username
      - group
      - group_name
      - id
      - permission
      - permission_display
      - user
      - user_email
      - username
    GroupPermissionRequest:
      type: object
      description: 组权限序列化器
      properties:
        user:
          type: integer
          title: 用户
        group:
          type: integer
          title: 密码组
        permission:
          enum:
          - view
          - edit
          - manage
          - admin
          type: string
          description: |-
            * `view` - 查看
            * `edit` - 编辑
            * `manage` - 管理
            * `admin` - 管理员
          x-spec-enum-id: 9332315630403d50
          title: 权限级别
      required:
      - group
      - permission
      - user
    OperationLog:
      type: object
      description: |-
        操作日志序列化器

        用于序列化系统操作日志，包含用户操作的详细信息。

        字段说明：
        - id: 日志唯一标识
        - user: 操作用户ID
        - user_name: 操作用户全名
        - user_email: 操作用户邮箱
        - action_type: 操作类型（如：create, update, delete等）
        - action_display: 操作类型显示名称
        - result: 操作结果（success/failure）
        - target_type: 目标对象类型
        - target_id: 目标对象ID
        - target_name: 目标对象名称
        - description: 操作描述
        - extra_data: 额外数据（JSON格式）
        - ip_address: 操作者IP地址
        - user_agent: 用户代理字符串
        - request_method: HTTP请求方法
        - request_path: 请求路径
        - created_at: 创建时间（ISO 8601格式）
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        user:
          type: integer
          nullable: true
          title: 操作用户
        user_name:
          type: string
          readOnly: true
          description: 操作用户的全名
        user_email:
          type: string
          readOnly: true
          description: 操作用户的邮箱地址
        action_type:
          enum:
          - user_login
          - user_logout
          - user_register
          - user_update
          - user_delete
          - password_change
          - password_create
          - password_view
          - password_update
          - password_delete
          - password_copy
          - password_export
          - password_import
          - password_share
          - share_revoke
          - onetime_link_create
          - onetime_link_access
          - category_create
          - category_update
          - category_delete
          - tag_create
          - tag_update
          - tag_delete
          - system_backup
          - system_restore
          - system_setting_update
          - mfa_enable
          - mfa_disable
          - account_lock
          - account_unlock
          type: string
          description: |-
            * `user_login` - 用户登录
            * `user_logout` - 用户登出
            * `user_register` - 用户注册
            * `user_update` - 用户信息更新
            * `user_delete` - 用户删除
            * `password_change` - 密码修改
            * `password_create` - 创建密码
            * `password_view` - 查看密码
            * `password_update` - 更新密码
            * `password_delete` - 删除密码
            * `password_copy` - 复制密码
            * `password_export` - 导出密码
            * `password_import` - 导入密码
            * `password_share` - 分享密码
            * `share_revoke` - 撤销分享
            * `onetime_link_create` - 创建一次性链接
            * `onetime_link_access` - 访问一次性链接
            * `category_create` - 创建分类
            * `category_update` - 更新分类
            * `category_delete` - 删除分类
            * `tag_create` - 创建标签
            * `tag_update` - 更新标签
            * `tag_delete` - 删除标签
            * `system_backup` - 系统备份
            * `system_restore` - 系统恢复
            * `system_setting_update` - 系统设置更新
            * `mfa_enable` - 启用多因素认证
            * `mfa_disable` - 禁用多因素认证
            * `account_lock` - 账户锁定
            * `account_unlock` - 账户解锁
          x-spec-enum-id: ca5be30d5e9037cd
          title: 操作类型
        action_display:
          type: string
          readOnly: true
          description: 操作类型的显示名称
        result:
          enum:
          - success
          - failed
          - warning
          type: string
          description: |-
            * `success` - 成功
            * `failed` - 失败
            * `warning` - 警告
          x-spec-enum-id: 55887e66a88fa390
          title: 操作结果
        target_type:
          type: string
          title: 目标类型
          maxLength: 50
        target_id:
          type: string
          title: 目标ID
          maxLength: 100
        target_name:
          type: string
          title: 目标名称
          maxLength: 200
        description:
          type: string
          title: 操作描述
        extra_data:
          title: 额外数据
        ip_address:
          type: string
          title: IP地址
        user_agent:
          type: string
          title: 用户代理
        request_method:
          type: string
          title: 请求方法
          maxLength: 10
        request_path:
          type: string
          title: 请求路径
          maxLength: 500
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 操作时间
      required:
      - action_display
      - action_type
      - created_at
      - id
      - ip_address
      - user_email
      - user_name
    PaginatedAttachmentList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Attachment'
    PaginatedBackupConfigList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/BackupConfig'
    PaginatedCategoryList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Category'
    PaginatedCustomFieldList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/CustomField'
    PaginatedDepartmentList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Department'
    PaginatedEmailTemplateList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/EmailTemplate'
    PaginatedGroupPermissionList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/GroupPermission'
    PaginatedOperationLogList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/OperationLog'
    PaginatedPasswordAccessLogList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PasswordAccessLog'
    PaginatedPasswordEntryGroupList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PasswordEntryGroup'
    PaginatedPasswordEntryGroupMembershipList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PasswordEntryGroupMembership'
    PaginatedPasswordEntryList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PasswordEntry'
    PaginatedPasswordPolicyList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PasswordPolicy'
    PaginatedRoleList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Role'
    PaginatedSecurityEventList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/SecurityEvent'
    PaginatedShareLinkList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ShareLink'
    PaginatedSystemSettingList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/SystemSetting'
    PaginatedTeamList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Team'
    PaginatedUserList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/User'
    PasswordAccessLog:
      type: object
      description: 密码访问日志序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        user:
          type: integer
          nullable: true
          title: 访问用户
        user_name:
          type: string
          readOnly: true
        user_email:
          type: string
          readOnly: true
        password_entry:
          type: string
          format: uuid
          title: 密码条目
        password_title:
          type: string
          readOnly: true
        access_type:
          enum:
          - view
          - copy_username
          - copy_password
          - copy_url
          - edit
          - delete
          - share
          - export
          type: string
          description: |-
            * `view` - 查看
            * `copy_username` - 复制用户名
            * `copy_password` - 复制密码
            * `copy_url` - 复制网址
            * `edit` - 编辑
            * `delete` - 删除
            * `share` - 分享
            * `export` - 导出
          x-spec-enum-id: 5d4f3bf9fdc2809a
          title: 访问类型
        access_type_display:
          type: string
          readOnly: true
        ip_address:
          type: string
          title: IP地址
        user_agent:
          type: string
          title: 用户代理
        access_source:
          type: string
          title: 访问来源
          description: direct, share, onetime_link
          maxLength: 50
        source_id:
          type: string
          title: 来源ID
          maxLength: 100
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 访问时间
      required:
      - access_type
      - access_type_display
      - created_at
      - id
      - ip_address
      - password_entry
      - password_title
      - user_email
      - user_name
    PasswordEntry:
      type: object
      description: 密码条目序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        title:
          type: string
          title: 标题
          maxLength: 200
        username:
          type: string
          title: 用户名
          maxLength: 200
        system_type:
          enum:
          - os
          - db
          - mdw
          - ftp
          - sftp
          - 网站
          - 网络设备
          - 其他
          type: string
          description: |-
            * `os` - 操作系统
            * `db` - 数据库
            * `mdw` - 中间件
            * `ftp` - FTP
            * `sftp` - SFTP
            * `网站` - 网站
            * `网络设备` - 网络设备
            * `其他` - 其他
          x-spec-enum-id: eb564d01ef968a96
          title: 系统类型
        system_type_display:
          type: string
          readOnly: true
        mdw_type:
          enum:
          - bes
          - was
          - redis
          - other
          - ''
          type: string
          description: |-
            * `bes` - BES
            * `was` - WAS
            * `redis` - Redis
            * `other` - 其他
          x-spec-enum-id: 313f6b2a8c8aa59a
          title: 中间件类型
        mdw_type_display:
          type: string
          readOnly: true
        ip_address:
          type: string
          nullable: true
          title: IP地址
        port:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
          title: 端口
        protocol:
          enum:
          - ssh
          - tcp
          - http
          - https
          - ftp
          - sftp
          - rdp
          - other
          - ''
          type: string
          description: |-
            * `ssh` - SSH
            * `tcp` - TCP
            * `http` - HTTP
            * `https` - HTTPS
            * `ftp` - FTP
            * `sftp` - SFTP
            * `rdp` - RDP
            * `other` - 其他
          x-spec-enum-id: 3ab17e2e1c01f5c0
          title: 连接协议
        protocol_display:
          type: string
          readOnly: true
        url:
          type: string
          format: uri
          title: 访问地址
          maxLength: 200
        database_type:
          enum:
          - gaussdb
          - mysql
          - postgresql
          - oracle
          - other
          - ''
          type: string
          description: |-
            * `gaussdb` - GaussDB
            * `mysql` - MySQL
            * `postgresql` - PostgreSQL
            * `oracle` - Oracle
            * `other` - 其他
          x-spec-enum-id: 821c921aa1b3922b
          title: 数据库类型
        database_type_display:
          type: string
          readOnly: true
        database_name:
          type: string
          title: 数据库名
          maxLength: 100
        environment:
          enum:
          - dev
          - test
          - staging
          - prod
          - ''
          type: string
          description: |-
            * `dev` - 开发环境
            * `test` - 测试环境
            * `staging` - 预发布环境
            * `prod` - 生产环境
          x-spec-enum-id: 33bbbf07afde5a9f
          title: 环境类型
        environment_display:
          type: string
          readOnly: true
        project_name:
          type: string
          title: 应用/项目名称
          maxLength: 100
        created_by:
          type: string
          title: 创建者
          maxLength: 100
        notes:
          type: string
          title: 备注
        category:
          type: integer
          nullable: true
          title: 分类
        category_name:
          type: string
          readOnly: true
        groups:
          type: array
          items:
            type: integer
            title: 所属组
          readOnly: true
          title: 所属组
        groups_data:
          type: string
          readOnly: true
        user_groups_permissions:
          type: string
          readOnly: true
        custom_fields:
          type: array
          items:
            $ref: '#/components/schemas/CustomField'
          readOnly: true
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/Attachment'
          readOnly: true
        strength:
          enum:
          - weak
          - medium
          - strong
          - very_strong
          type: string
          description: |-
            * `weak` - 弱
            * `medium` - 中等
            * `strong` - 强
            * `very_strong` - 非常强
          x-spec-enum-id: 0c01acd80669ed34
          title: 密码强度
        is_favorite:
          type: boolean
          title: 收藏
        is_pinned:
          type: boolean
          title: 置顶
        expires_at:
          type: string
          format: date-time
          nullable: true
          title: 过期时间
        last_used:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 最后使用时间
        password_strength:
          type: string
          readOnly: true
        connection_string:
          type: string
          readOnly: true
        full_url:
          type: string
          readOnly: true
        database_connection_string:
          type: string
          readOnly: true
        ssh_command:
          type: string
          readOnly: true
        system_info:
          type: string
          readOnly: true
        owner:
          type: integer
          readOnly: true
          title: 所有者
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        is_deleted:
          type: boolean
          readOnly: true
          title: 是否已删除
        deleted_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 删除时间
        deleted_by:
          type: integer
          readOnly: true
          nullable: true
          title: 删除者
        deleted_by_username:
          type: string
          readOnly: true
      required:
      - attachments
      - category_name
      - connection_string
      - created_at
      - custom_fields
      - database_connection_string
      - database_type_display
      - deleted_at
      - deleted_by
      - deleted_by_username
      - environment_display
      - full_url
      - groups
      - groups_data
      - id
      - is_deleted
      - last_used
      - mdw_type_display
      - owner
      - password_strength
      - project_name
      - protocol_display
      - ssh_command
      - system_info
      - system_type_display
      - title
      - updated_at
      - user_groups_permissions
      - username
    PasswordEntryCreate:
      type: object
      description: 密码条目创建序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        title:
          type: string
          title: 标题
          maxLength: 200
        username:
          type: string
          title: 用户名
          maxLength: 200
        password:
          type: string
          title: 密码
        system_type:
          enum:
          - os
          - db
          - mdw
          - ftp
          - sftp
          - 网站
          - 网络设备
          - 其他
          type: string
          description: |-
            * `os` - 操作系统
            * `db` - 数据库
            * `mdw` - 中间件
            * `ftp` - FTP
            * `sftp` - SFTP
            * `网站` - 网站
            * `网络设备` - 网络设备
            * `其他` - 其他
          x-spec-enum-id: eb564d01ef968a96
          title: 系统类型
        mdw_type:
          enum:
          - bes
          - was
          - redis
          - other
          - ''
          type: string
          description: |-
            * `bes` - BES
            * `was` - WAS
            * `redis` - Redis
            * `other` - 其他
          x-spec-enum-id: 313f6b2a8c8aa59a
          title: 中间件类型
        ip_address:
          type: string
          nullable: true
          title: IP地址
        port:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
          title: 端口
        protocol:
          enum:
          - ssh
          - tcp
          - http
          - https
          - ftp
          - sftp
          - rdp
          - other
          - ''
          type: string
          description: |-
            * `ssh` - SSH
            * `tcp` - TCP
            * `http` - HTTP
            * `https` - HTTPS
            * `ftp` - FTP
            * `sftp` - SFTP
            * `rdp` - RDP
            * `other` - 其他
          x-spec-enum-id: 3ab17e2e1c01f5c0
          title: 连接协议
        url:
          type: string
          format: uri
          title: 访问地址
          maxLength: 200
        database_type:
          enum:
          - gaussdb
          - mysql
          - postgresql
          - oracle
          - other
          - ''
          type: string
          description: |-
            * `gaussdb` - GaussDB
            * `mysql` - MySQL
            * `postgresql` - PostgreSQL
            * `oracle` - Oracle
            * `other` - 其他
          x-spec-enum-id: 821c921aa1b3922b
          title: 数据库类型
        database_name:
          type: string
          title: 数据库名
          maxLength: 100
        environment:
          enum:
          - dev
          - test
          - staging
          - prod
          - ''
          type: string
          description: |-
            * `dev` - 开发环境
            * `test` - 测试环境
            * `staging` - 预发布环境
            * `prod` - 生产环境
          x-spec-enum-id: 33bbbf07afde5a9f
          title: 环境类型
        project_name:
          type: string
          title: 应用/项目名称
          maxLength: 100
        created_by:
          type: string
          title: 创建者
          maxLength: 100
        notes:
          type: string
          title: 备注
        category:
          type: integer
          nullable: true
          title: 分类
        category_name:
          type: string
          readOnly: true
        is_favorite:
          type: boolean
          title: 收藏
        expires_at:
          type: string
          format: date-time
          nullable: true
          title: 过期时间
        owner:
          type: integer
          readOnly: true
          title: 所有者
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - category_name
      - created_at
      - id
      - owner
      - password
      - project_name
      - title
      - updated_at
      - username
    PasswordEntryCreateRequest:
      type: object
      description: 密码条目创建序列化器
      properties:
        title:
          type: string
          minLength: 1
          title: 标题
          maxLength: 200
        username:
          type: string
          minLength: 1
          title: 用户名
          maxLength: 200
        password:
          type: string
          minLength: 1
          title: 密码
        system_type:
          enum:
          - os
          - db
          - mdw
          - ftp
          - sftp
          - 网站
          - 网络设备
          - 其他
          type: string
          description: |-
            * `os` - 操作系统
            * `db` - 数据库
            * `mdw` - 中间件
            * `ftp` - FTP
            * `sftp` - SFTP
            * `网站` - 网站
            * `网络设备` - 网络设备
            * `其他` - 其他
          x-spec-enum-id: eb564d01ef968a96
          title: 系统类型
        mdw_type:
          enum:
          - bes
          - was
          - redis
          - other
          - ''
          type: string
          description: |-
            * `bes` - BES
            * `was` - WAS
            * `redis` - Redis
            * `other` - 其他
          x-spec-enum-id: 313f6b2a8c8aa59a
          title: 中间件类型
        ip_address:
          type: string
          nullable: true
          minLength: 1
          title: IP地址
        port:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
          title: 端口
        protocol:
          enum:
          - ssh
          - tcp
          - http
          - https
          - ftp
          - sftp
          - rdp
          - other
          - ''
          type: string
          description: |-
            * `ssh` - SSH
            * `tcp` - TCP
            * `http` - HTTP
            * `https` - HTTPS
            * `ftp` - FTP
            * `sftp` - SFTP
            * `rdp` - RDP
            * `other` - 其他
          x-spec-enum-id: 3ab17e2e1c01f5c0
          title: 连接协议
        url:
          type: string
          format: uri
          title: 访问地址
          maxLength: 200
        database_type:
          enum:
          - gaussdb
          - mysql
          - postgresql
          - oracle
          - other
          - ''
          type: string
          description: |-
            * `gaussdb` - GaussDB
            * `mysql` - MySQL
            * `postgresql` - PostgreSQL
            * `oracle` - Oracle
            * `other` - 其他
          x-spec-enum-id: 821c921aa1b3922b
          title: 数据库类型
        database_name:
          type: string
          title: 数据库名
          maxLength: 100
        environment:
          enum:
          - dev
          - test
          - staging
          - prod
          - ''
          type: string
          description: |-
            * `dev` - 开发环境
            * `test` - 测试环境
            * `staging` - 预发布环境
            * `prod` - 生产环境
          x-spec-enum-id: 33bbbf07afde5a9f
          title: 环境类型
        project_name:
          type: string
          minLength: 1
          title: 应用/项目名称
          maxLength: 100
        created_by:
          type: string
          title: 创建者
          maxLength: 100
        notes:
          type: string
          title: 备注
        category:
          type: integer
          nullable: true
          title: 分类
        group_ids:
          type: array
          items:
            type: integer
          writeOnly: true
          description: 密码组ID列表
        is_favorite:
          type: boolean
          title: 收藏
        expires_at:
          type: string
          format: date-time
          nullable: true
          title: 过期时间
      required:
      - password
      - project_name
      - title
      - username
    PasswordEntryDetail:
      type: object
      description: 密码条目详情序列化器（包含密码）
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        title:
          type: string
          title: 标题
          maxLength: 200
        username:
          type: string
          title: 用户名
          maxLength: 200
        system_type:
          enum:
          - os
          - db
          - mdw
          - ftp
          - sftp
          - 网站
          - 网络设备
          - 其他
          type: string
          description: |-
            * `os` - 操作系统
            * `db` - 数据库
            * `mdw` - 中间件
            * `ftp` - FTP
            * `sftp` - SFTP
            * `网站` - 网站
            * `网络设备` - 网络设备
            * `其他` - 其他
          x-spec-enum-id: eb564d01ef968a96
          title: 系统类型
        system_type_display:
          type: string
          readOnly: true
        mdw_type:
          enum:
          - bes
          - was
          - redis
          - other
          - ''
          type: string
          description: |-
            * `bes` - BES
            * `was` - WAS
            * `redis` - Redis
            * `other` - 其他
          x-spec-enum-id: 313f6b2a8c8aa59a
          title: 中间件类型
        mdw_type_display:
          type: string
          readOnly: true
        ip_address:
          type: string
          nullable: true
          title: IP地址
        port:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
          title: 端口
        protocol:
          enum:
          - ssh
          - tcp
          - http
          - https
          - ftp
          - sftp
          - rdp
          - other
          - ''
          type: string
          description: |-
            * `ssh` - SSH
            * `tcp` - TCP
            * `http` - HTTP
            * `https` - HTTPS
            * `ftp` - FTP
            * `sftp` - SFTP
            * `rdp` - RDP
            * `other` - 其他
          x-spec-enum-id: 3ab17e2e1c01f5c0
          title: 连接协议
        protocol_display:
          type: string
          readOnly: true
        url:
          type: string
          format: uri
          title: 访问地址
          maxLength: 200
        database_type:
          enum:
          - gaussdb
          - mysql
          - postgresql
          - oracle
          - other
          - ''
          type: string
          description: |-
            * `gaussdb` - GaussDB
            * `mysql` - MySQL
            * `postgresql` - PostgreSQL
            * `oracle` - Oracle
            * `other` - 其他
          x-spec-enum-id: 821c921aa1b3922b
          title: 数据库类型
        database_type_display:
          type: string
          readOnly: true
        database_name:
          type: string
          title: 数据库名
          maxLength: 100
        environment:
          enum:
          - dev
          - test
          - staging
          - prod
          - ''
          type: string
          description: |-
            * `dev` - 开发环境
            * `test` - 测试环境
            * `staging` - 预发布环境
            * `prod` - 生产环境
          x-spec-enum-id: 33bbbf07afde5a9f
          title: 环境类型
        environment_display:
          type: string
          readOnly: true
        project_name:
          type: string
          title: 应用/项目名称
          maxLength: 100
        created_by:
          type: string
          title: 创建者
          maxLength: 100
        notes:
          type: string
          title: 备注
        category:
          type: integer
          nullable: true
          title: 分类
        category_name:
          type: string
          readOnly: true
        groups:
          type: array
          items:
            type: integer
            title: 所属组
          readOnly: true
          title: 所属组
        groups_data:
          type: string
          readOnly: true
        user_groups_permissions:
          type: string
          readOnly: true
        custom_fields:
          type: array
          items:
            $ref: '#/components/schemas/CustomField'
          readOnly: true
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/Attachment'
          readOnly: true
        strength:
          enum:
          - weak
          - medium
          - strong
          - very_strong
          type: string
          description: |-
            * `weak` - 弱
            * `medium` - 中等
            * `strong` - 强
            * `very_strong` - 非常强
          x-spec-enum-id: 0c01acd80669ed34
          title: 密码强度
        is_favorite:
          type: boolean
          title: 收藏
        is_pinned:
          type: boolean
          title: 置顶
        expires_at:
          type: string
          format: date-time
          nullable: true
          title: 过期时间
        last_used:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 最后使用时间
        password_strength:
          type: string
          readOnly: true
        connection_string:
          type: string
          readOnly: true
        full_url:
          type: string
          readOnly: true
        database_connection_string:
          type: string
          readOnly: true
        ssh_command:
          type: string
          readOnly: true
        system_info:
          type: string
          readOnly: true
        owner:
          type: integer
          readOnly: true
          title: 所有者
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        is_deleted:
          type: boolean
          readOnly: true
          title: 是否已删除
        deleted_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 删除时间
        deleted_by:
          type: integer
          readOnly: true
          nullable: true
          title: 删除者
        deleted_by_username:
          type: string
          readOnly: true
        password_history:
          type: array
          items:
            $ref: '#/components/schemas/PasswordHistory'
          readOnly: true
      required:
      - attachments
      - category_name
      - connection_string
      - created_at
      - custom_fields
      - database_connection_string
      - database_type_display
      - deleted_at
      - deleted_by
      - deleted_by_username
      - environment_display
      - full_url
      - groups
      - groups_data
      - id
      - is_deleted
      - last_used
      - mdw_type_display
      - owner
      - password_history
      - password_strength
      - project_name
      - protocol_display
      - ssh_command
      - system_info
      - system_type_display
      - title
      - updated_at
      - user_groups_permissions
      - username
    PasswordEntryDetailRequest:
      type: object
      description: 密码条目详情序列化器（包含密码）
      properties:
        title:
          type: string
          minLength: 1
          title: 标题
          maxLength: 200
        username:
          type: string
          minLength: 1
          title: 用户名
          maxLength: 200
        password:
          type: string
          writeOnly: true
          minLength: 1
          title: 密码
        system_type:
          enum:
          - os
          - db
          - mdw
          - ftp
          - sftp
          - 网站
          - 网络设备
          - 其他
          type: string
          description: |-
            * `os` - 操作系统
            * `db` - 数据库
            * `mdw` - 中间件
            * `ftp` - FTP
            * `sftp` - SFTP
            * `网站` - 网站
            * `网络设备` - 网络设备
            * `其他` - 其他
          x-spec-enum-id: eb564d01ef968a96
          title: 系统类型
        mdw_type:
          enum:
          - bes
          - was
          - redis
          - other
          - ''
          type: string
          description: |-
            * `bes` - BES
            * `was` - WAS
            * `redis` - Redis
            * `other` - 其他
          x-spec-enum-id: 313f6b2a8c8aa59a
          title: 中间件类型
        ip_address:
          type: string
          nullable: true
          minLength: 1
          title: IP地址
        port:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
          title: 端口
        protocol:
          enum:
          - ssh
          - tcp
          - http
          - https
          - ftp
          - sftp
          - rdp
          - other
          - ''
          type: string
          description: |-
            * `ssh` - SSH
            * `tcp` - TCP
            * `http` - HTTP
            * `https` - HTTPS
            * `ftp` - FTP
            * `sftp` - SFTP
            * `rdp` - RDP
            * `other` - 其他
          x-spec-enum-id: 3ab17e2e1c01f5c0
          title: 连接协议
        url:
          type: string
          format: uri
          title: 访问地址
          maxLength: 200
        database_type:
          enum:
          - gaussdb
          - mysql
          - postgresql
          - oracle
          - other
          - ''
          type: string
          description: |-
            * `gaussdb` - GaussDB
            * `mysql` - MySQL
            * `postgresql` - PostgreSQL
            * `oracle` - Oracle
            * `other` - 其他
          x-spec-enum-id: 821c921aa1b3922b
          title: 数据库类型
        database_name:
          type: string
          title: 数据库名
          maxLength: 100
        environment:
          enum:
          - dev
          - test
          - staging
          - prod
          - ''
          type: string
          description: |-
            * `dev` - 开发环境
            * `test` - 测试环境
            * `staging` - 预发布环境
            * `prod` - 生产环境
          x-spec-enum-id: 33bbbf07afde5a9f
          title: 环境类型
        project_name:
          type: string
          minLength: 1
          title: 应用/项目名称
          maxLength: 100
        created_by:
          type: string
          title: 创建者
          maxLength: 100
        notes:
          type: string
          title: 备注
        category:
          type: integer
          nullable: true
          title: 分类
        group_ids:
          type: array
          items:
            type: integer
          writeOnly: true
          description: 密码组ID列表
        strength:
          enum:
          - weak
          - medium
          - strong
          - very_strong
          type: string
          description: |-
            * `weak` - 弱
            * `medium` - 中等
            * `strong` - 强
            * `very_strong` - 非常强
          x-spec-enum-id: 0c01acd80669ed34
          title: 密码强度
        is_favorite:
          type: boolean
          title: 收藏
        is_pinned:
          type: boolean
          title: 置顶
        expires_at:
          type: string
          format: date-time
          nullable: true
          title: 过期时间
      required:
      - password
      - project_name
      - title
      - username
    PasswordEntryGroup:
      type: object
      description: 密码组序列化器
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          title: 组名称
          maxLength: 100
        description:
          type: string
          title: 组描述
        created_by:
          type: integer
          readOnly: true
          title: 创建者
        created_by_username:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        is_active:
          type: boolean
          title: 是否激活
        members_count:
          type: string
          readOnly: true
        password_entries_count:
          type: string
          readOnly: true
        user_permission:
          type: string
          readOnly: true
      required:
      - created_at
      - created_by
      - created_by_username
      - id
      - members_count
      - name
      - password_entries_count
      - updated_at
      - user_permission
    PasswordEntryGroupMembership:
      type: object
      description: 密码条目组成员关系序列化器
      properties:
        id:
          type: integer
          readOnly: true
        password_entry:
          type: string
          format: uuid
          title: 密码条目
        password_entry_title:
          type: string
          readOnly: true
        group:
          type: integer
          title: 密码组
        group_name:
          type: string
          readOnly: true
        added_by:
          type: integer
          readOnly: true
          title: 添加者
        added_by_username:
          type: string
          readOnly: true
        added_at:
          type: string
          format: date-time
          readOnly: true
          title: 添加时间
      required:
      - added_at
      - added_by
      - added_by_username
      - group
      - group_name
      - id
      - password_entry
      - password_entry_title
    PasswordEntryGroupMembershipRequest:
      type: object
      description: 密码条目组成员关系序列化器
      properties:
        password_entry:
          type: string
          format: uuid
          title: 密码条目
        group:
          type: integer
          title: 密码组
      required:
      - group
      - password_entry
    PasswordEntryGroupRequest:
      type: object
      description: 密码组序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 组名称
          maxLength: 100
        description:
          type: string
          title: 组描述
        is_active:
          type: boolean
          title: 是否激活
      required:
      - name
    PasswordHistory:
      type: object
      description: 密码历史序列化器
      properties:
        id:
          type: integer
          readOnly: true
        old_password:
          type: string
          title: 旧密码
        changed_at:
          type: string
          format: date-time
          readOnly: true
          title: 修改时间
        changed_by:
          type: integer
          title: 修改用户
      required:
      - changed_at
      - changed_by
      - id
      - old_password
    PasswordHistoryRequest:
      type: object
      description: 密码历史序列化器
      properties:
        old_password:
          type: string
          minLength: 1
          title: 旧密码
        changed_by:
          type: integer
          title: 修改用户
      required:
      - changed_by
      - old_password
    PasswordPolicy:
      type: object
      description: 密码策略序列化器
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          title: 策略名称
          maxLength: 100
        description:
          type: string
          title: 策略描述
        min_length:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 密码最小长度
          description: 密码的最小字符数
        max_length:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 密码最大长度
          description: 密码的最大字符数
        uppercase_count:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 大写字母最少个数
          description: 密码中必须包含的大写字母最少个数
        lowercase_count:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 小写字母最少个数
          description: 密码中必须包含的小写字母最少个数
        digit_count:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 数字最少个数
          description: 密码中必须包含的数字最少个数
        special_char_count:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 特殊字符最少个数
          description: 密码中必须包含的特殊字符最少个数
        allowed_special_chars:
          type: string
          title: 允许的特殊字符
          description: 密码中允许使用的特殊字符集合
          maxLength: 100
        allow_repeated_chars:
          type: boolean
          title: 是否允许重复字符
          description: 是否允许密码中出现连续重复的字符
        max_repeated_chars:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 最大重复字符数
          description: 允许连续重复字符的最大个数
        forbid_common_passwords:
          type: boolean
          title: 禁止常见密码
          description: 是否禁止使用常见的弱密码
        forbid_personal_info:
          type: boolean
          title: 禁止个人信息
          description: 是否禁止密码中包含用户名、邮箱等个人信息
        is_active:
          type: boolean
          title: 是否启用
          description: 是否启用此密码策略
        is_default:
          type: boolean
          title: 是否为默认策略
          description: 是否为系统默认的密码策略
        created_by:
          type: integer
          readOnly: true
          title: 创建者
        created_by_username:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - created_by
      - created_by_username
      - id
      - name
      - updated_at
    PasswordPolicyRequest:
      type: object
      description: 密码策略序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 策略名称
          maxLength: 100
        description:
          type: string
          title: 策略描述
        min_length:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 密码最小长度
          description: 密码的最小字符数
        max_length:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 密码最大长度
          description: 密码的最大字符数
        uppercase_count:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 大写字母最少个数
          description: 密码中必须包含的大写字母最少个数
        lowercase_count:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 小写字母最少个数
          description: 密码中必须包含的小写字母最少个数
        digit_count:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 数字最少个数
          description: 密码中必须包含的数字最少个数
        special_char_count:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 特殊字符最少个数
          description: 密码中必须包含的特殊字符最少个数
        allowed_special_chars:
          type: string
          minLength: 1
          title: 允许的特殊字符
          description: 密码中允许使用的特殊字符集合
          maxLength: 100
        allow_repeated_chars:
          type: boolean
          title: 是否允许重复字符
          description: 是否允许密码中出现连续重复的字符
        max_repeated_chars:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 最大重复字符数
          description: 允许连续重复字符的最大个数
        forbid_common_passwords:
          type: boolean
          title: 禁止常见密码
          description: 是否禁止使用常见的弱密码
        forbid_personal_info:
          type: boolean
          title: 禁止个人信息
          description: 是否禁止密码中包含用户名、邮箱等个人信息
        is_active:
          type: boolean
          title: 是否启用
          description: 是否启用此密码策略
        is_default:
          type: boolean
          title: 是否为默认策略
          description: 是否为系统默认的密码策略
      required:
      - name
    PatchedAttachmentRequest:
      type: object
      description: 附件序列化器
      properties:
        file_name:
          type: string
          minLength: 1
          title: 文件名
          maxLength: 255
        file_path:
          type: string
          minLength: 1
          title: 文件路径
          maxLength: 500
        content_type:
          type: string
          minLength: 1
          title: 文件类型
          maxLength: 100
        password_entry:
          type: string
          format: uuid
          title: 密码条目
        uploaded_by:
          type: integer
          title: 上传用户
    PatchedBackupConfigRequest:
      type: object
      description: 备份配置序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 备份名称
          maxLength: 200
        description:
          type: string
          title: 备份描述
        backup_type:
          enum:
          - full
          - incremental
          - differential
          - manual
          type: string
          description: |-
            * `full` - 完整备份
            * `incremental` - 增量备份
            * `differential` - 差异备份
            * `manual` - 手动备份
          x-spec-enum-id: f233531d2df4cfb9
          title: 备份类型
        include_passwords:
          type: boolean
          title: 包含密码
        include_categories:
          type: boolean
          title: 包含分类
        include_users:
          type: boolean
          title: 包含用户
        include_settings:
          type: boolean
          title: 包含设置
        include_logs:
          type: boolean
          title: 包含日志
        include_attachments:
          type: boolean
          title: 包含附件
        storage_type:
          enum:
          - local
          - s3
          - azure
          - gcs
          - ftp
          - sftp
          type: string
          description: |-
            * `local` - 本地存储
            * `s3` - Amazon S3
            * `azure` - Azure Blob
            * `gcs` - Google Cloud Storage
            * `ftp` - FTP服务器
            * `sftp` - SFTP服务器
          x-spec-enum-id: 1033db16b067d6b2
          title: 存储类型
        storage_path:
          type: string
          minLength: 1
          title: 存储路径
          maxLength: 500
        file_name:
          type: string
          title: 文件名
          maxLength: 255
        is_encrypted:
          type: boolean
          title: 是否加密
        is_compressed:
          type: boolean
          title: 是否压缩
    PatchedCategoryRequest:
      type: object
      description: 分类序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 分类名称
          maxLength: 100
        description:
          type: string
          title: 分类描述
        icon:
          type: string
          title: 图标
          maxLength: 50
        color:
          type: string
          minLength: 1
          title: 颜色
          maxLength: 7
    PatchedCustomFieldRequest:
      type: object
      description: 自定义字段序列化器
      properties:
        field_name:
          type: string
          minLength: 1
          title: 字段名称
          maxLength: 100
        field_type:
          enum:
          - text
          - password
          - email
          - url
          - phone
          - date
          - number
          type: string
          description: |-
            * `text` - 文本
            * `password` - 密码
            * `email` - 邮箱
            * `url` - 网址
            * `phone` - 电话
            * `date` - 日期
            * `number` - 数字
          x-spec-enum-id: fcf5e1c4b3332285
          title: 字段类型
        field_value:
          type: string
          minLength: 1
          title: 字段值
        is_sensitive:
          type: boolean
          title: 敏感数据
        order:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
          title: 排序
        password_entry:
          type: string
          format: uuid
          title: 密码条目
    PatchedDepartmentRequest:
      type: object
      description: 部门序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 部门名称
          maxLength: 100
        description:
          type: string
          title: 部门描述
        parent:
          type: integer
          nullable: true
          title: 上级部门
    PatchedEmailTemplateRequest:
      type: object
      description: 邮件模板序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 模板名称
          maxLength: 200
        subject:
          type: string
          minLength: 1
          title: 邮件主题
          maxLength: 200
        content:
          type: string
          minLength: 1
          title: 邮件内容
        template_type:
          enum:
          - welcome
          - password_reset
          - account_locked
          - security_alert
          - backup_notification
          - system_maintenance
          - custom
          type: string
          description: |-
            * `welcome` - 欢迎邮件
            * `password_reset` - 密码重置
            * `account_locked` - 账户锁定
            * `security_alert` - 安全警报
            * `backup_notification` - 备份通知
            * `system_maintenance` - 系统维护
            * `custom` - 自定义
          x-spec-enum-id: 812fa624cc399c2a
          title: 模板类型
        is_active:
          type: boolean
          title: 是否启用
        is_default:
          type: boolean
          default: false
          title: 默认模板
    PatchedGroupPermissionRequest:
      type: object
      description: 组权限序列化器
      properties:
        user:
          type: integer
          title: 用户
        group:
          type: integer
          title: 密码组
        permission:
          enum:
          - view
          - edit
          - manage
          - admin
          type: string
          description: |-
            * `view` - 查看
            * `edit` - 编辑
            * `manage` - 管理
            * `admin` - 管理员
          x-spec-enum-id: 9332315630403d50
          title: 权限级别
    PatchedPasswordEntryDetailRequest:
      type: object
      description: 密码条目详情序列化器（包含密码）
      properties:
        title:
          type: string
          minLength: 1
          title: 标题
          maxLength: 200
        username:
          type: string
          minLength: 1
          title: 用户名
          maxLength: 200
        password:
          type: string
          writeOnly: true
          minLength: 1
          title: 密码
        system_type:
          enum:
          - os
          - db
          - mdw
          - ftp
          - sftp
          - 网站
          - 网络设备
          - 其他
          type: string
          description: |-
            * `os` - 操作系统
            * `db` - 数据库
            * `mdw` - 中间件
            * `ftp` - FTP
            * `sftp` - SFTP
            * `网站` - 网站
            * `网络设备` - 网络设备
            * `其他` - 其他
          x-spec-enum-id: eb564d01ef968a96
          title: 系统类型
        mdw_type:
          enum:
          - bes
          - was
          - redis
          - other
          - ''
          type: string
          description: |-
            * `bes` - BES
            * `was` - WAS
            * `redis` - Redis
            * `other` - 其他
          x-spec-enum-id: 313f6b2a8c8aa59a
          title: 中间件类型
        ip_address:
          type: string
          nullable: true
          minLength: 1
          title: IP地址
        port:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
          title: 端口
        protocol:
          enum:
          - ssh
          - tcp
          - http
          - https
          - ftp
          - sftp
          - rdp
          - other
          - ''
          type: string
          description: |-
            * `ssh` - SSH
            * `tcp` - TCP
            * `http` - HTTP
            * `https` - HTTPS
            * `ftp` - FTP
            * `sftp` - SFTP
            * `rdp` - RDP
            * `other` - 其他
          x-spec-enum-id: 3ab17e2e1c01f5c0
          title: 连接协议
        url:
          type: string
          format: uri
          title: 访问地址
          maxLength: 200
        database_type:
          enum:
          - gaussdb
          - mysql
          - postgresql
          - oracle
          - other
          - ''
          type: string
          description: |-
            * `gaussdb` - GaussDB
            * `mysql` - MySQL
            * `postgresql` - PostgreSQL
            * `oracle` - Oracle
            * `other` - 其他
          x-spec-enum-id: 821c921aa1b3922b
          title: 数据库类型
        database_name:
          type: string
          title: 数据库名
          maxLength: 100
        environment:
          enum:
          - dev
          - test
          - staging
          - prod
          - ''
          type: string
          description: |-
            * `dev` - 开发环境
            * `test` - 测试环境
            * `staging` - 预发布环境
            * `prod` - 生产环境
          x-spec-enum-id: 33bbbf07afde5a9f
          title: 环境类型
        project_name:
          type: string
          minLength: 1
          title: 应用/项目名称
          maxLength: 100
        created_by:
          type: string
          title: 创建者
          maxLength: 100
        notes:
          type: string
          title: 备注
        category:
          type: integer
          nullable: true
          title: 分类
        group_ids:
          type: array
          items:
            type: integer
          writeOnly: true
          description: 密码组ID列表
        strength:
          enum:
          - weak
          - medium
          - strong
          - very_strong
          type: string
          description: |-
            * `weak` - 弱
            * `medium` - 中等
            * `strong` - 强
            * `very_strong` - 非常强
          x-spec-enum-id: 0c01acd80669ed34
          title: 密码强度
        is_favorite:
          type: boolean
          title: 收藏
        is_pinned:
          type: boolean
          title: 置顶
        expires_at:
          type: string
          format: date-time
          nullable: true
          title: 过期时间
    PatchedPasswordEntryGroupRequest:
      type: object
      description: 密码组序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 组名称
          maxLength: 100
        description:
          type: string
          title: 组描述
        is_active:
          type: boolean
          title: 是否激活
    PatchedPasswordPolicyRequest:
      type: object
      description: 密码策略序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 策略名称
          maxLength: 100
        description:
          type: string
          title: 策略描述
        min_length:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 密码最小长度
          description: 密码的最小字符数
        max_length:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 密码最大长度
          description: 密码的最大字符数
        uppercase_count:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 大写字母最少个数
          description: 密码中必须包含的大写字母最少个数
        lowercase_count:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 小写字母最少个数
          description: 密码中必须包含的小写字母最少个数
        digit_count:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 数字最少个数
          description: 密码中必须包含的数字最少个数
        special_char_count:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 特殊字符最少个数
          description: 密码中必须包含的特殊字符最少个数
        allowed_special_chars:
          type: string
          minLength: 1
          title: 允许的特殊字符
          description: 密码中允许使用的特殊字符集合
          maxLength: 100
        allow_repeated_chars:
          type: boolean
          title: 是否允许重复字符
          description: 是否允许密码中出现连续重复的字符
        max_repeated_chars:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          title: 最大重复字符数
          description: 允许连续重复字符的最大个数
        forbid_common_passwords:
          type: boolean
          title: 禁止常见密码
          description: 是否禁止使用常见的弱密码
        forbid_personal_info:
          type: boolean
          title: 禁止个人信息
          description: 是否禁止密码中包含用户名、邮箱等个人信息
        is_active:
          type: boolean
          title: 是否启用
          description: 是否启用此密码策略
        is_default:
          type: boolean
          title: 是否为默认策略
          description: 是否为系统默认的密码策略
    PatchedRoleRequest:
      type: object
      description: 角色序列化器
      properties:
        name:
          enum:
          - admin
          - manager
          - user
          - viewer
          type: string
          description: |-
            * `admin` - 系统管理员
            * `manager` - 部门管理员
            * `user` - 普通用户
            * `viewer` - 只读用户
          x-spec-enum-id: ef56bc8fa4bca39c
          title: 角色名称
        description:
          type: string
          title: 角色描述
        permissions:
          title: 权限列表
    PatchedSecurityEventRequest:
      type: object
      description: 安全事件序列化器
      properties:
        user:
          type: integer
          nullable: true
          title: 相关用户
        event_type:
          enum:
          - suspicious_login
          - multiple_failed_logins
          - account_locked
          - password_breach
          - unusual_access
          - data_export
          - bulk_operation
          - privilege_escalation
          - unauthorized_access
          type: string
          description: |-
            * `suspicious_login` - 可疑登录
            * `multiple_failed_logins` - 多次登录失败
            * `account_locked` - 账户锁定
            * `password_breach` - 密码泄露
            * `unusual_access` - 异常访问
            * `data_export` - 数据导出
            * `bulk_operation` - 批量操作
            * `privilege_escalation` - 权限提升
            * `unauthorized_access` - 未授权访问
          x-spec-enum-id: cee6c44d15059ce1
          title: 事件类型
        severity:
          enum:
          - low
          - medium
          - high
          - critical
          type: string
          description: |-
            * `low` - 低
            * `medium` - 中
            * `high` - 高
            * `critical` - 严重
          x-spec-enum-id: b253d69e6bd28d93
          title: 严重程度
        status:
          enum:
          - open
          - investigating
          - resolved
          - false_positive
          type: string
          description: |-
            * `open` - 开放
            * `investigating` - 调查中
            * `resolved` - 已解决
            * `false_positive` - 误报
          x-spec-enum-id: ea9171956908bc0e
          title: 状态
        title:
          type: string
          minLength: 1
          title: 事件标题
          maxLength: 200
        description:
          type: string
          minLength: 1
          title: 事件描述
        event_data:
          title: 事件数据
        affected_resources:
          title: 受影响资源
        assigned_to:
          type: integer
          nullable: true
          title: 分配给
        resolution_notes:
          type: string
          title: 解决备注
        resolved_at:
          type: string
          format: date-time
          nullable: true
          title: 解决时间
    PatchedShareLinkRequest:
      type: object
      description: 分享链接序列化器
      properties:
        password_entry:
          type: string
          format: uuid
          title: 密码条目
        expires_at:
          type: string
          format: date-time
          title: 过期时间
        require_password:
          type: boolean
          title: 需要密码
        access_password:
          type: string
          writeOnly: true
          title: 访问密码
          maxLength: 128
        max_access_count:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
          nullable: true
          title: 最大访问次数
        status:
          enum:
          - active
          - used
          - expired
          - revoked
          type: string
          description: |-
            * `active` - 活跃
            * `used` - 已使用
            * `expired` - 已过期
            * `revoked` - 已撤销
          x-spec-enum-id: e8afef328b54ddf8
          title: 状态
    PatchedSystemSettingRequest:
      type: object
      description: 系统设置序列化器
      properties:
        key:
          type: string
          minLength: 1
          title: 设置键
          maxLength: 100
        name:
          type: string
          minLength: 1
          title: 设置名称
          maxLength: 200
        value:
          type: string
          minLength: 1
          title: 设置值
        description:
          type: string
          title: 设置描述
        category:
          enum:
          - password_policy
          - security
          - sharing
          - backup
          - notification
          - ui
          - integration
          - audit
          - system
          type: string
          description: |-
            * `password_policy` - 密码策略
            * `security` - 安全设置
            * `sharing` - 分享设置
            * `backup` - 备份设置
            * `notification` - 通知设置
            * `ui` - 界面设置
            * `integration` - 集成设置
            * `audit` - 审计设置
            * `system` - 系统设置
          x-spec-enum-id: 0df82e37475dc88c
          title: 设置分类
        value_type:
          enum:
          - string
          - integer
          - float
          - boolean
          - json
          - text
          type: string
          description: |-
            * `string` - 字符串
            * `integer` - 整数
            * `float` - 浮点数
            * `boolean` - 布尔值
            * `json` - JSON
            * `text` - 文本
          x-spec-enum-id: 3fc99d4101744697
          title: 值类型
        is_public:
          type: boolean
          title: 公开设置
        is_readonly:
          type: boolean
          title: 只读设置
        requires_restart:
          type: boolean
          title: 需要重启
        order:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
          title: 排序
        group:
          type: string
          title: 分组
          maxLength: 50
    PatchedTeamRequest:
      type: object
      description: 团队序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 团队名称
          maxLength: 100
        description:
          type: string
          title: 团队描述
        department:
          type: integer
          title: 所属部门
    PatchedUserUpdateRequest:
      type: object
      description: 用户更新序列化器
      properties:
        name:
          type: string
          title: 姓名
          maxLength: 100
        first_name:
          type: string
          title: 名字
          maxLength: 150
        last_name:
          type: string
          title: 姓氏
          maxLength: 150
        phone:
          type: string
          title: 手机号
          maxLength: 20
        avatar:
          type: string
          format: binary
          nullable: true
          title: 头像
        department:
          type: integer
          nullable: true
          title: 部门
        team:
          type: integer
          nullable: true
          title: 团队
        role:
          type: integer
          nullable: true
          title: 角色
    Role:
      type: object
      description: 角色序列化器
      properties:
        id:
          type: integer
          readOnly: true
        name:
          enum:
          - admin
          - manager
          - user
          - viewer
          type: string
          description: |-
            * `admin` - 系统管理员
            * `manager` - 部门管理员
            * `user` - 普通用户
            * `viewer` - 只读用户
          x-spec-enum-id: ef56bc8fa4bca39c
          title: 角色名称
        description:
          type: string
          title: 角色描述
        permissions:
          title: 权限列表
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - id
      - name
      - updated_at
    RoleRequest:
      type: object
      description: 角色序列化器
      properties:
        name:
          enum:
          - admin
          - manager
          - user
          - viewer
          type: string
          description: |-
            * `admin` - 系统管理员
            * `manager` - 部门管理员
            * `user` - 普通用户
            * `viewer` - 只读用户
          x-spec-enum-id: ef56bc8fa4bca39c
          title: 角色名称
        description:
          type: string
          title: 角色描述
        permissions:
          title: 权限列表
      required:
      - name
    SecurityEvent:
      type: object
      description: 安全事件序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        user:
          type: integer
          nullable: true
          title: 相关用户
        user_name:
          type: string
          readOnly: true
        user_email:
          type: string
          readOnly: true
        event_type:
          enum:
          - suspicious_login
          - multiple_failed_logins
          - account_locked
          - password_breach
          - unusual_access
          - data_export
          - bulk_operation
          - privilege_escalation
          - unauthorized_access
          type: string
          description: |-
            * `suspicious_login` - 可疑登录
            * `multiple_failed_logins` - 多次登录失败
            * `account_locked` - 账户锁定
            * `password_breach` - 密码泄露
            * `unusual_access` - 异常访问
            * `data_export` - 数据导出
            * `bulk_operation` - 批量操作
            * `privilege_escalation` - 权限提升
            * `unauthorized_access` - 未授权访问
          x-spec-enum-id: cee6c44d15059ce1
          title: 事件类型
        event_type_display:
          type: string
          readOnly: true
        severity:
          enum:
          - low
          - medium
          - high
          - critical
          type: string
          description: |-
            * `low` - 低
            * `medium` - 中
            * `high` - 高
            * `critical` - 严重
          x-spec-enum-id: b253d69e6bd28d93
          title: 严重程度
        severity_display:
          type: string
          readOnly: true
        status:
          enum:
          - open
          - investigating
          - resolved
          - false_positive
          type: string
          description: |-
            * `open` - 开放
            * `investigating` - 调查中
            * `resolved` - 已解决
            * `false_positive` - 误报
          x-spec-enum-id: ea9171956908bc0e
          title: 状态
        title:
          type: string
          title: 事件标题
          maxLength: 200
        description:
          type: string
          title: 事件描述
        event_data:
          title: 事件数据
        affected_resources:
          title: 受影响资源
        assigned_to:
          type: integer
          nullable: true
          title: 分配给
        resolution_notes:
          type: string
          title: 解决备注
        resolved_at:
          type: string
          format: date-time
          nullable: true
          title: 解决时间
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - description
      - event_type
      - event_type_display
      - id
      - severity_display
      - title
      - updated_at
      - user_email
      - user_name
    SecurityEventCreate:
      type: object
      description: 安全事件创建序列化器
      properties:
        user:
          type: integer
          nullable: true
          title: 相关用户
        event_type:
          enum:
          - suspicious_login
          - multiple_failed_logins
          - account_locked
          - password_breach
          - unusual_access
          - data_export
          - bulk_operation
          - privilege_escalation
          - unauthorized_access
          type: string
          description: |-
            * `suspicious_login` - 可疑登录
            * `multiple_failed_logins` - 多次登录失败
            * `account_locked` - 账户锁定
            * `password_breach` - 密码泄露
            * `unusual_access` - 异常访问
            * `data_export` - 数据导出
            * `bulk_operation` - 批量操作
            * `privilege_escalation` - 权限提升
            * `unauthorized_access` - 未授权访问
          x-spec-enum-id: cee6c44d15059ce1
          title: 事件类型
        severity:
          enum:
          - low
          - medium
          - high
          - critical
          type: string
          description: |-
            * `low` - 低
            * `medium` - 中
            * `high` - 高
            * `critical` - 严重
          x-spec-enum-id: b253d69e6bd28d93
          title: 严重程度
        title:
          type: string
          title: 事件标题
          maxLength: 200
        description:
          type: string
          title: 事件描述
        event_data:
          title: 事件数据
        affected_resources:
          title: 受影响资源
      required:
      - description
      - event_type
      - title
    SecurityEventCreateRequest:
      type: object
      description: 安全事件创建序列化器
      properties:
        user:
          type: integer
          nullable: true
          title: 相关用户
        event_type:
          enum:
          - suspicious_login
          - multiple_failed_logins
          - account_locked
          - password_breach
          - unusual_access
          - data_export
          - bulk_operation
          - privilege_escalation
          - unauthorized_access
          type: string
          description: |-
            * `suspicious_login` - 可疑登录
            * `multiple_failed_logins` - 多次登录失败
            * `account_locked` - 账户锁定
            * `password_breach` - 密码泄露
            * `unusual_access` - 异常访问
            * `data_export` - 数据导出
            * `bulk_operation` - 批量操作
            * `privilege_escalation` - 权限提升
            * `unauthorized_access` - 未授权访问
          x-spec-enum-id: cee6c44d15059ce1
          title: 事件类型
        severity:
          enum:
          - low
          - medium
          - high
          - critical
          type: string
          description: |-
            * `low` - 低
            * `medium` - 中
            * `high` - 高
            * `critical` - 严重
          x-spec-enum-id: b253d69e6bd28d93
          title: 严重程度
        title:
          type: string
          minLength: 1
          title: 事件标题
          maxLength: 200
        description:
          type: string
          minLength: 1
          title: 事件描述
        event_data:
          title: 事件数据
        affected_resources:
          title: 受影响资源
      required:
      - description
      - event_type
      - title
    SecurityEventRequest:
      type: object
      description: 安全事件序列化器
      properties:
        user:
          type: integer
          nullable: true
          title: 相关用户
        event_type:
          enum:
          - suspicious_login
          - multiple_failed_logins
          - account_locked
          - password_breach
          - unusual_access
          - data_export
          - bulk_operation
          - privilege_escalation
          - unauthorized_access
          type: string
          description: |-
            * `suspicious_login` - 可疑登录
            * `multiple_failed_logins` - 多次登录失败
            * `account_locked` - 账户锁定
            * `password_breach` - 密码泄露
            * `unusual_access` - 异常访问
            * `data_export` - 数据导出
            * `bulk_operation` - 批量操作
            * `privilege_escalation` - 权限提升
            * `unauthorized_access` - 未授权访问
          x-spec-enum-id: cee6c44d15059ce1
          title: 事件类型
        severity:
          enum:
          - low
          - medium
          - high
          - critical
          type: string
          description: |-
            * `low` - 低
            * `medium` - 中
            * `high` - 高
            * `critical` - 严重
          x-spec-enum-id: b253d69e6bd28d93
          title: 严重程度
        status:
          enum:
          - open
          - investigating
          - resolved
          - false_positive
          type: string
          description: |-
            * `open` - 开放
            * `investigating` - 调查中
            * `resolved` - 已解决
            * `false_positive` - 误报
          x-spec-enum-id: ea9171956908bc0e
          title: 状态
        title:
          type: string
          minLength: 1
          title: 事件标题
          maxLength: 200
        description:
          type: string
          minLength: 1
          title: 事件描述
        event_data:
          title: 事件数据
        affected_resources:
          title: 受影响资源
        assigned_to:
          type: integer
          nullable: true
          title: 分配给
        resolution_notes:
          type: string
          title: 解决备注
        resolved_at:
          type: string
          format: date-time
          nullable: true
          title: 解决时间
      required:
      - description
      - event_type
      - title
    ShareLink:
      type: object
      description: 分享链接序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        password_entry:
          type: string
          format: uuid
          title: 密码条目
        token:
          type: string
          readOnly: true
          title: 访问令牌
        expires_at:
          type: string
          format: date-time
          title: 过期时间
        require_password:
          type: boolean
          title: 需要密码
        max_access_count:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
          nullable: true
          title: 最大访问次数
        access_count:
          type: integer
          readOnly: true
          title: 访问次数
        status:
          enum:
          - active
          - used
          - expired
          - revoked
          type: string
          description: |-
            * `active` - 活跃
            * `used` - 已使用
            * `expired` - 已过期
            * `revoked` - 已撤销
          x-spec-enum-id: e8afef328b54ddf8
          title: 状态
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        accessed_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 访问时间
        created_by_name:
          type: string
          readOnly: true
        password_entry_title:
          type: string
          readOnly: true
        is_expired:
          type: string
          readOnly: true
        share_url:
          type: string
          readOnly: true
      required:
      - access_count
      - accessed_at
      - created_at
      - created_by_name
      - expires_at
      - id
      - is_expired
      - password_entry
      - password_entry_title
      - share_url
      - token
    ShareLinkRequest:
      type: object
      description: 分享链接序列化器
      properties:
        password_entry:
          type: string
          format: uuid
          title: 密码条目
        expires_at:
          type: string
          format: date-time
          title: 过期时间
        require_password:
          type: boolean
          title: 需要密码
        access_password:
          type: string
          writeOnly: true
          title: 访问密码
          maxLength: 128
        max_access_count:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
          nullable: true
          title: 最大访问次数
        status:
          enum:
          - active
          - used
          - expired
          - revoked
          type: string
          description: |-
            * `active` - 活跃
            * `used` - 已使用
            * `expired` - 已过期
            * `revoked` - 已撤销
          x-spec-enum-id: e8afef328b54ddf8
          title: 状态
      required:
      - expires_at
      - password_entry
    SystemSetting:
      type: object
      description: 系统设置序列化器
      properties:
        id:
          type: integer
          readOnly: true
        key:
          type: string
          title: 设置键
          maxLength: 100
        name:
          type: string
          title: 设置名称
          maxLength: 200
        value:
          type: string
          title: 设置值
        description:
          type: string
          title: 设置描述
        category:
          enum:
          - password_policy
          - security
          - sharing
          - backup
          - notification
          - ui
          - integration
          - audit
          - system
          type: string
          description: |-
            * `password_policy` - 密码策略
            * `security` - 安全设置
            * `sharing` - 分享设置
            * `backup` - 备份设置
            * `notification` - 通知设置
            * `ui` - 界面设置
            * `integration` - 集成设置
            * `audit` - 审计设置
            * `system` - 系统设置
          x-spec-enum-id: 0df82e37475dc88c
          title: 设置分类
        value_type:
          enum:
          - string
          - integer
          - float
          - boolean
          - json
          - text
          type: string
          description: |-
            * `string` - 字符串
            * `integer` - 整数
            * `float` - 浮点数
            * `boolean` - 布尔值
            * `json` - JSON
            * `text` - 文本
          x-spec-enum-id: 3fc99d4101744697
          title: 值类型
        is_public:
          type: boolean
          title: 公开设置
        is_readonly:
          type: boolean
          title: 只读设置
        requires_restart:
          type: boolean
          title: 需要重启
        order:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
          title: 排序
        group:
          type: string
          title: 分组
          maxLength: 50
        modified_by:
          type: integer
          readOnly: true
          nullable: true
          title: 修改者
        modified_by_name:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
      required:
      - created_at
      - id
      - key
      - modified_by
      - modified_by_name
      - name
      - value
    SystemSettingRequest:
      type: object
      description: 系统设置序列化器
      properties:
        key:
          type: string
          minLength: 1
          title: 设置键
          maxLength: 100
        name:
          type: string
          minLength: 1
          title: 设置名称
          maxLength: 200
        value:
          type: string
          minLength: 1
          title: 设置值
        description:
          type: string
          title: 设置描述
        category:
          enum:
          - password_policy
          - security
          - sharing
          - backup
          - notification
          - ui
          - integration
          - audit
          - system
          type: string
          description: |-
            * `password_policy` - 密码策略
            * `security` - 安全设置
            * `sharing` - 分享设置
            * `backup` - 备份设置
            * `notification` - 通知设置
            * `ui` - 界面设置
            * `integration` - 集成设置
            * `audit` - 审计设置
            * `system` - 系统设置
          x-spec-enum-id: 0df82e37475dc88c
          title: 设置分类
        value_type:
          enum:
          - string
          - integer
          - float
          - boolean
          - json
          - text
          type: string
          description: |-
            * `string` - 字符串
            * `integer` - 整数
            * `float` - 浮点数
            * `boolean` - 布尔值
            * `json` - JSON
            * `text` - 文本
          x-spec-enum-id: 3fc99d4101744697
          title: 值类型
        is_public:
          type: boolean
          title: 公开设置
        is_readonly:
          type: boolean
          title: 只读设置
        requires_restart:
          type: boolean
          title: 需要重启
        order:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
          title: 排序
        group:
          type: string
          title: 分组
          maxLength: 50
      required:
      - key
      - name
      - value
    Team:
      type: object
      description: 团队序列化器
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          title: 团队名称
          maxLength: 100
        description:
          type: string
          title: 团队描述
        department:
          type: integer
          title: 所属部门
        department_name:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - department
      - department_name
      - id
      - name
      - updated_at
    TeamRequest:
      type: object
      description: 团队序列化器
      properties:
        name:
          type: string
          minLength: 1
          title: 团队名称
          maxLength: 100
        description:
          type: string
          title: 团队描述
        department:
          type: integer
          title: 所属部门
      required:
      - department
      - name
    TokenRefresh:
      type: object
      properties:
        access:
          type: string
          readOnly: true
        refresh:
          type: string
      required:
      - access
      - refresh
    TokenRefreshRequest:
      type: object
      properties:
        refresh:
          type: string
          minLength: 1
      required:
      - refresh
    User:
      type: object
      description: 用户序列化器
      properties:
        id:
          type: integer
          readOnly: true
        username:
          type: string
          title: 用户名
          description: 必填；长度为150个字符或以下；只能包含字母、数字、特殊字符“@”、“.”、“-”和“_”。
          pattern: ^[\w.@+-]+$
          maxLength: 150
        email:
          type: string
          format: email
          nullable: true
          title: 邮箱
          maxLength: 254
        name:
          type: string
          title: 姓名
          maxLength: 100
        first_name:
          type: string
          title: 名字
          maxLength: 150
        last_name:
          type: string
          title: 姓氏
          maxLength: 150
        phone:
          type: string
          title: 手机号
          maxLength: 20
        avatar:
          type: string
          format: uri
          nullable: true
          title: 头像
        department:
          type: integer
          nullable: true
          title: 部门
        department_name:
          type: string
          readOnly: true
        team:
          type: integer
          nullable: true
          title: 团队
        team_name:
          type: string
          readOnly: true
        role:
          type: integer
          nullable: true
          title: 角色
        role_name:
          type: string
          readOnly: true
        homePath:
          type: string
          readOnly: true
        is_mfa_enabled:
          type: boolean
          title: 启用多因素认证
        last_password_change:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 最后密码修改时间
        is_active:
          type: boolean
          title: 有效
          description: 指明用户是否被认为是活跃的。以反选代替删除帐号。
        is_staff:
          type: boolean
          title: 工作人员状态
          description: 指明用户是否可以登录到这个管理站点。
        date_joined:
          type: string
          format: date-time
          readOnly: true
          title: 加入日期
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - created_at
      - date_joined
      - department_name
      - homePath
      - id
      - last_password_change
      - role_name
      - team_name
      - updated_at
      - username
    UserCreate:
      type: object
      description: 用户创建序列化器
      properties:
        id:
          type: integer
          readOnly: true
        username:
          type: string
          title: 用户名
          description: 必填；长度为150个字符或以下；只能包含字母、数字、特殊字符“@”、“.”、“-”和“_”。
          pattern: ^[\w.@+-]+$
          maxLength: 150
        email:
          type: string
          format: email
          nullable: true
          title: 邮箱
          maxLength: 254
        name:
          type: string
          title: 姓名
          maxLength: 100
        first_name:
          type: string
          title: 名字
          maxLength: 150
        last_name:
          type: string
          title: 姓氏
          maxLength: 150
        phone:
          type: string
          title: 手机号
          maxLength: 20
        department:
          type: integer
          nullable: true
          title: 部门
        team:
          type: integer
          nullable: true
          title: 团队
        role:
          type: integer
          nullable: true
          title: 角色
      required:
      - id
      - username
    UserCreateRequest:
      type: object
      description: 用户创建序列化器
      properties:
        username:
          type: string
          minLength: 1
          title: 用户名
          description: 必填；长度为150个字符或以下；只能包含字母、数字、特殊字符“@”、“.”、“-”和“_”。
          pattern: ^[\w.@+-]+$
          maxLength: 150
        email:
          type: string
          format: email
          nullable: true
          title: 邮箱
          maxLength: 254
        name:
          type: string
          title: 姓名
          maxLength: 100
        password:
          type: string
          writeOnly: true
          minLength: 1
        password_confirm:
          type: string
          writeOnly: true
          minLength: 1
        first_name:
          type: string
          title: 名字
          maxLength: 150
        last_name:
          type: string
          title: 姓氏
          maxLength: 150
        phone:
          type: string
          title: 手机号
          maxLength: 20
        department:
          type: integer
          nullable: true
          title: 部门
        team:
          type: integer
          nullable: true
          title: 团队
        role:
          type: integer
          nullable: true
          title: 角色
      required:
      - password
      - password_confirm
      - username
    UserUpdate:
      type: object
      description: 用户更新序列化器
      properties:
        name:
          type: string
          title: 姓名
          maxLength: 100
        first_name:
          type: string
          title: 名字
          maxLength: 150
        last_name:
          type: string
          title: 姓氏
          maxLength: 150
        phone:
          type: string
          title: 手机号
          maxLength: 20
        avatar:
          type: string
          format: uri
          nullable: true
          title: 头像
        department:
          type: integer
          nullable: true
          title: 部门
        team:
          type: integer
          nullable: true
          title: 团队
        role:
          type: integer
          nullable: true
          title: 角色
    UserUpdateRequest:
      type: object
      description: 用户更新序列化器
      properties:
        name:
          type: string
          title: 姓名
          maxLength: 100
        first_name:
          type: string
          title: 名字
          maxLength: 150
        last_name:
          type: string
          title: 姓氏
          maxLength: 150
        phone:
          type: string
          title: 手机号
          maxLength: 20
        avatar:
          type: string
          format: binary
          nullable: true
          title: 头像
        department:
          type: integer
          nullable: true
          title: 部门
        team:
          type: integer
          nullable: true
          title: 团队
        role:
          type: integer
          nullable: true
          title: 角色
