# Generated by Django 5.2.4 on 2025-07-28 13:17

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('passwords', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CategoryTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='模板名称')),
                ('description', models.TextField(blank=True, verbose_name='模板描述')),
                ('icon', models.CharField(blank=True, max_length=50, verbose_name='图标')),
                ('color', models.CharField(default='#1890ff', max_length=7, verbose_name='颜色')),
                ('default_fields', models.JSONField(default=list, help_text='预定义的自定义字段配置', verbose_name='默认字段')),
                ('is_system', models.BooleanField(default=False, verbose_name='系统模板')),
                ('is_active', models.BooleanField(default=True, verbose_name='启用状态')),
                ('usage_count', models.IntegerField(default=0, verbose_name='使用次数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '分类模板',
                'verbose_name_plural': '分类模板',
                'db_table': 'category_templates',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CategoryGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='组名称')),
                ('description', models.TextField(blank=True, verbose_name='组描述')),
                ('icon', models.CharField(blank=True, max_length=50, verbose_name='图标')),
                ('color', models.CharField(default='#722ed1', max_length=7, verbose_name='颜色')),
                ('is_shared', models.BooleanField(default=False, verbose_name='共享组')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='所有者')),
            ],
            options={
                'verbose_name': '分类组',
                'verbose_name_plural': '分类组',
                'db_table': 'category_groups',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='CategoryGroupMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('permission', models.CharField(choices=[('view', '查看'), ('edit', '编辑'), ('admin', '管理')], default='view', max_length=10, verbose_name='权限')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='加入时间')),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='categories.categorygroup', verbose_name='分类组')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '分类组成员',
                'verbose_name_plural': '分类组成员',
                'db_table': 'category_group_members',
                'unique_together': {('group', 'user')},
            },
        ),
        migrations.AddField(
            model_name='categorygroup',
            name='shared_with',
            field=models.ManyToManyField(blank=True, related_name='shared_category_groups', through='categories.CategoryGroupMember', to=settings.AUTH_USER_MODEL, verbose_name='共享用户'),
        ),
        migrations.CreateModel(
            name='CategoryRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='规则名称')),
                ('description', models.TextField(blank=True, verbose_name='规则描述')),
                ('rule_type', models.CharField(choices=[('url_contains', 'URL包含'), ('url_domain', 'URL域名'), ('title_contains', '标题包含'), ('title_regex', '标题正则'), ('username_contains', '用户名包含'), ('email_domain', '邮箱域名'), ('custom_field', '自定义字段')], max_length=20, verbose_name='规则类型')),
                ('rule_value', models.CharField(max_length=500, verbose_name='规则值')),
                ('is_case_sensitive', models.BooleanField(default=False, verbose_name='区分大小写')),
                ('is_active', models.BooleanField(default=True, verbose_name='启用状态')),
                ('priority', models.IntegerField(default=0, verbose_name='优先级')),
                ('auto_apply', models.BooleanField(default=True, verbose_name='自动应用')),
                ('match_count', models.IntegerField(default=0, verbose_name='匹配次数')),
                ('last_matched', models.DateTimeField(blank=True, null=True, verbose_name='最后匹配时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('target_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='passwords.category', verbose_name='目标分类')),
            ],
            options={
                'verbose_name': '分类规则',
                'verbose_name_plural': '分类规则',
                'db_table': 'category_rules',
                'ordering': ['-priority', 'name'],
            },
        ),
        migrations.CreateModel(
            name='CategoryStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_passwords', models.IntegerField(default=0, verbose_name='密码总数')),
                ('weak_passwords', models.IntegerField(default=0, verbose_name='弱密码数')),
                ('medium_passwords', models.IntegerField(default=0, verbose_name='中等密码数')),
                ('strong_passwords', models.IntegerField(default=0, verbose_name='强密码数')),
                ('very_strong_passwords', models.IntegerField(default=0, verbose_name='非常强密码数')),
                ('compromised_passwords', models.IntegerField(default=0, verbose_name='泄露密码数')),
                ('expired_passwords', models.IntegerField(default=0, verbose_name='过期密码数')),
                ('duplicate_passwords', models.IntegerField(default=0, verbose_name='重复密码数')),
                ('total_accesses', models.IntegerField(default=0, verbose_name='总访问次数')),
                ('last_accessed', models.DateTimeField(blank=True, null=True, verbose_name='最后访问时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('category', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='statistics', to='passwords.category', verbose_name='分类')),
            ],
            options={
                'verbose_name': '分类统计',
                'verbose_name_plural': '分类统计',
                'db_table': 'category_statistics',
            },
        ),
        migrations.AlterUniqueTogether(
            name='categorygroup',
            unique_together={('name', 'owner')},
        ),
    ]
