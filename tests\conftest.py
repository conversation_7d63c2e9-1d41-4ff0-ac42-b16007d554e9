"""
pytest配置文件
"""
import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# 测试配置
TEST_CONFIG = {
    'API_BASE_URL': 'http://localhost:8001',
    'ADMIN_USERNAME': 'admin',
    'ADMIN_PASSWORD': 'admin123',
    'TEST_TIMEOUT': 30,
    'PARALLEL_WORKERS': 4,
}

# 测试数据库配置
if 'test' in sys.argv:
    settings.DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
