#!/usr/bin/env python
"""
检查数据库表
"""
import sqlite3

def check_database():
    """检查数据库表"""
    try:
        conn = sqlite3.connect('db.sqlite3')
        cursor = conn.cursor()
        
        # 检查auth_user表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='auth_user';")
        auth_user_result = cursor.fetchall()
        print(f"auth_user表存在: {bool(auth_user_result)}")
        
        # 检查所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"数据库中的表数量: {len(tables)}")
        
        # 显示前10个表
        print("前10个表:")
        for i, table in enumerate(tables[:10]):
            print(f"  {i+1}. {table[0]}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时发生错误: {e}")

if __name__ == "__main__":
    check_database()
