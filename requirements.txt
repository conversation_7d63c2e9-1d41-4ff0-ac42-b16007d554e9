# Django核心框架
Django>=5.1.0
djangorestframework>=3.15.0

# 数据库相关
mysqlclient>=2.2.4
django-cors-headers>=4.4.0
requests>=2.32.3
dj-database-url>=2.1.0

# 缓存和会话
redis>=5.1.0
django-redis>=5.4.0

# 异步任务
celery>=5.4.0
django-celery-beat>=2.7.0
django-celery-results>=2.5.1

# 其他工具
pyotp>=2.9.0
qrcode>=8.2.0

# 认证和权限
PyJWT>=2.9.0
djangorestframework-simplejwt>=5.3.0
django-guardian>=2.4.0
django-axes>=6.1.1
django-auditlog>=2.3.0

# 国密算法
gmssl>=3.2.2

# API文档
drf-spectacular>=0.27.0

# 环境变量管理
python-decouple>=3.8
django-environ>=0.11.0

# 数据验证
marshmallow>=3.22.0
django-filter>=24.3

# 文件处理
Pillow>=10.4.0

# 日期时间处理
python-dateutil>=2.9.0

# 安全相关
cryptography>=43.0.0

# 服务器
gunicorn>=22.0.0
psutil>=6.0.0

# 开发工具
ipython>=8.28.0
django-debug-toolbar>=4.4.0
django-extensions>=3.2.3

# 测试工具
pytest>=8.3.0
pytest-django>=4.9.0
factory-boy>=3.3.0
coverage>=7.6.0

# 代码质量
flake8>=7.1.0
black>=24.8.0
isort>=5.13.0
pylint>=3.2.0