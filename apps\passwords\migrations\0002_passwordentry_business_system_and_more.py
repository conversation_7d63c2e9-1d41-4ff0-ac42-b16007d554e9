# Generated by Django 5.2.4 on 2025-07-29 01:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("passwords", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="passwordentry",
            name="business_system",
            field=models.CharField(blank=True, max_length=100, verbose_name="业务系统"),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="database_name",
            field=models.CharField(blank=True, max_length=100, verbose_name="数据库名"),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="database_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("mysql", "MySQL"),
                    ("postgresql", "PostgreSQL"),
                    ("oracle", "Oracle"),
                    ("sqlserver", "SQL Server"),
                    ("mongodb", "MongoDB"),
                    ("redis", "Redis"),
                    ("elasticsearch", "Elasticsearch"),
                    ("other", "其他"),
                ],
                max_length=20,
                verbose_name="数据库类型",
            ),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="description",
            field=models.TextField(blank=True, verbose_name="系统描述"),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="environment",
            field=models.CharField(
                blank=True,
                choices=[
                    ("dev", "开发环境"),
                    ("test", "测试环境"),
                    ("staging", "预发布环境"),
                    ("prod", "生产环境"),
                ],
                max_length=20,
                verbose_name="环境类型",
            ),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="hostname",
            field=models.CharField(
                blank=True, max_length=255, verbose_name="主机名/IP"
            ),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="port",
            field=models.PositiveIntegerField(
                blank=True, null=True, verbose_name="端口"
            ),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="protocol",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ssh", "SSH"),
                    ("rdp", "RDP"),
                    ("telnet", "Telnet"),
                    ("http", "HTTP"),
                    ("https", "HTTPS"),
                    ("ftp", "FTP"),
                    ("sftp", "SFTP"),
                    ("vnc", "VNC"),
                    ("other", "其他"),
                ],
                max_length=10,
                verbose_name="连接协议",
            ),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="responsible_person",
            field=models.CharField(blank=True, max_length=100, verbose_name="负责人"),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="schema_name",
            field=models.CharField(blank=True, max_length=100, verbose_name="模式名"),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="system_type",
            field=models.CharField(
                choices=[
                    ("linux", "Linux服务器"),
                    ("windows", "Windows服务器"),
                    ("database", "数据库"),
                    ("middleware", "中间件"),
                    ("network", "网络设备"),
                    ("cloud", "云服务"),
                    ("application", "应用系统"),
                    ("website", "网站"),
                    ("other", "其他"),
                ],
                default="other",
                max_length=20,
                verbose_name="系统类型",
            ),
        ),
        migrations.AlterField(
            model_name="passwordentry",
            name="url",
            field=models.URLField(blank=True, verbose_name="访问地址"),
        ),
    ]
