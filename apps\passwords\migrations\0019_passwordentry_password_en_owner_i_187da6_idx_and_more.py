# Generated by Django 5.2.4 on 2025-08-01 18:20

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("passwords", "0018_alter_passwordentry_database_name_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddIndex(
            model_name="passwordentry",
            index=models.Index(
                fields=["owner", "is_deleted"], name="password_en_owner_i_187da6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="passwordentry",
            index=models.Index(
                fields=["is_deleted", "created_at"],
                name="password_en_is_dele_f6cbe9_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="passwordentry",
            index=models.Index(
                fields=["is_deleted", "updated_at"],
                name="password_en_is_dele_052f1a_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="passwordpermission",
            index=models.Index(
                fields=["permission_type", "target_id", "is_active"],
                name="password_pe_permiss_ce1feb_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="passwordpermission",
            index=models.Index(
                fields=["password", "is_active", "expires_at"],
                name="password_pe_passwor_d76c94_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="passwordpermission",
            index=models.Index(
                fields=["is_active", "expires_at"],
                name="password_pe_is_acti_e12d02_idx",
            ),
        ),
    ]
