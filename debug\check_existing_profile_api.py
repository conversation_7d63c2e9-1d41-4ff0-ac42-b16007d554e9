#!/usr/bin/env python
"""
检查现有的profile API是否满足前端需求
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()


def test_profile_api():
    """测试现有的profile API"""
    print("🧪 测试现有的 /api/auth/profile/ API")
    print("=" * 60)

    try:
        from django.test import Client
        import json

        client = Client()

        # 1. 先登录获取JWT令牌
        print("1. 登录获取JWT令牌...")
        login_data = {"username": "admin", "password": "admin123!"}

        response = client.post(
            "/api/auth/login/",
            data=json.dumps(login_data),
            content_type="application/json",
        )

        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return False

        data = response.json()
        access_token = data.get("access_token")
        print(f"✅ 登录成功，获得令牌")

        # 2. 测试profile API
        print("\n2. 测试 /api/auth/profile/ API...")

        response = client.get(
            "/api/auth/profile/", HTTP_AUTHORIZATION=f"Bearer {access_token}"
        )

        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功")
            print(f"响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))

            # 分析响应结构
            print(f"\n📊 响应结构分析:")
            print(f"   包含字段: {list(result.keys())}")

            # 检查是否包含权限信息
            has_permissions = "permissions" in result
            print(f"   包含权限字段: {'✅' if has_permissions else '❌'}")

            # 检查用户信息字段
            required_user_fields = ["id", "username", "email", "name", "phone"]
            missing_fields = [
                field for field in required_user_fields if field not in result
            ]

            if missing_fields:
                print(f"   缺少用户字段: {missing_fields}")
            else:
                print(f"   ✅ 包含所有必需的用户字段")

            return result
        else:
            print(f"❌ API调用失败")
            try:
                error = response.json()
                print(f"错误信息: {error}")
            except:
                print(f"响应内容: {response.content}")
            return None

    except Exception as e:
        print(f"❌ 测试profile API失败: {e}")
        import traceback

        traceback.print_exc()
        return None


def compare_with_frontend_expectation():
    """对比前端期望的响应格式"""
    import json

    print("\n📋 前端期望的响应格式")
    print("=" * 60)

    expected_format = {
        "userInfo": {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "name": "系统管理员",
            "phone": "",
        },
        "permissions": [
            "user:create",
            "user:read",
            "user:update",
            "user:delete",
            "user:change_password",
            "user:unlock",
            "role:create",
            "role:read",
            "role:update",
            "role:delete",
            "system:*",
        ],
    }

    print("前端期望的响应格式:")
    print(json.dumps(expected_format, indent=2, ensure_ascii=False))

    return expected_format


def analyze_compatibility():
    """分析兼容性"""
    print("\n🔍 兼容性分析")
    print("=" * 60)

    # 测试现有API
    current_response = test_profile_api()

    if not current_response:
        print("❌ 无法获取当前API响应")
        return False

    # 获取前端期望
    expected_response = compare_with_frontend_expectation()

    print("\n📊 兼容性对比:")

    # 检查响应结构
    if "userInfo" in expected_response and "permissions" in expected_response:
        # 前端期望嵌套结构
        if "userInfo" in current_response:
            print("   ✅ 响应结构匹配（嵌套格式）")
            structure_match = True
        else:
            print("   ❌ 响应结构不匹配（扁平 vs 嵌套）")
            structure_match = False
    else:
        structure_match = True

    # 检查权限字段
    has_permissions = "permissions" in current_response
    print(f"   权限字段: {'✅ 存在' if has_permissions else '❌ 缺失'}")

    # 检查用户信息字段
    expected_user_fields = expected_response["userInfo"].keys()
    if "userInfo" in current_response:
        current_user_fields = current_response["userInfo"].keys()
    else:
        current_user_fields = current_response.keys()

    missing_user_fields = set(expected_user_fields) - set(current_user_fields)
    if missing_user_fields:
        print(f"   缺少用户字段: {list(missing_user_fields)}")
    else:
        print("   ✅ 用户字段完整")

    # 总结兼容性
    is_compatible = structure_match and has_permissions and not missing_user_fields

    print(f"\n🎯 兼容性结论:")
    if is_compatible:
        print("   ✅ 现有API完全兼容前端需求")
        print("   💡 建议：前端直接使用 /api/auth/profile/ 端点")
    else:
        print("   ❌ 现有API不完全兼容前端需求")
        print("   💡 需要修改现有API或创建新的端点")

    return is_compatible


def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决方案建议")
    print("=" * 60)

    print("方案1: 修改现有 /api/auth/profile/ API")
    print("   优点: 不需要新增端点，保持API简洁")
    print("   缺点: 可能影响其他使用该API的地方")
    print("   实施: 修改UserProfileView，添加权限返回逻辑")

    print("\n方案2: 创建新的 /api/auth/user-info API")
    print("   优点: 不影响现有API，专门为前端需求设计")
    print("   缺点: 增加API端点数量")
    print("   实施: 新增UserInfoView和对应URL配置")

    print("\n方案3: 前端适配现有API格式")
    print("   优点: 后端无需修改")
    print("   缺点: 前端需要修改代码逻辑")
    print("   实施: 前端修改API调用和数据处理逻辑")

    print("\n🎯 推荐方案:")
    print("   基于兼容性分析结果，推荐最合适的方案")


def main():
    """主函数"""
    print("🚀 检查现有profile API是否满足前端需求")
    print("=" * 80)

    # 分析兼容性
    is_compatible = analyze_compatibility()

    # 建议解决方案
    suggest_solutions()

    print("\n" + "=" * 80)
    print("📋 检查总结")
    print("=" * 80)

    if is_compatible:
        print("🎉 现有API可以满足前端需求！")
        print("\n✅ 建议操作:")
        print("1. 前端修改API调用地址：/api/auth/user-info → /api/auth/profile/")
        print("2. 如果响应格式不匹配，可以考虑修改现有API")
        print("3. 测试确保功能正常")
    else:
        print("⚠️ 现有API不能完全满足前端需求")
        print("\n🔧 需要采取的行动:")
        print("1. 选择合适的解决方案")
        print("2. 实施相应的修改")
        print("3. 测试确保兼容性")

    return is_compatible


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
