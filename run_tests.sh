#!/bin/bash
# 用户管理CRUD API测试运行脚本 (Linux/Mac)

echo "========================================"
echo "用户管理CRUD API自动化测试"
echo "========================================"

# 检查Python是否可用
if ! command -v python &> /dev/null; then
    echo "❌ 错误: Python未安装或不在PATH中"
    exit 1
fi

# 检查Django项目
if [ ! -f "manage.py" ]; then
    echo "❌ 错误: 未找到manage.py文件，请在Django项目根目录运行此脚本"
    exit 1
fi

# 创建reports目录
mkdir -p reports

show_menu() {
    echo ""
    echo "选择测试类型:"
    echo "1. 快速API测试 (推荐)"
    echo "2. 完整测试套件"
    echo "3. 特定测试类别"
    echo "4. Django单元测试"
    echo "5. 测试覆盖率分析"
    echo "6. 退出"
    echo ""
}

run_quick_test() {
    echo ""
    echo "🚀 运行快速API测试..."
    echo "----------------------------------------"
    
    if python tests/scripts/quick_test.py; then
        echo ""
        echo "✅ 快速测试完成"
    else
        echo ""
        echo "❌ 快速测试失败"
        return 1
    fi
}

run_full_test() {
    echo ""
    echo "🚀 运行完整测试套件..."
    echo "----------------------------------------"
    
    if python tests/scripts/run_user_api_tests.py; then
        echo ""
        echo "✅ 完整测试完成"
    else
        echo ""
        echo "❌ 完整测试失败"
        return 1
    fi
}

run_category_test() {
    echo ""
    echo "可用的测试类别:"
    echo "- crud: CRUD操作测试"
    echo "- permission: 权限控制测试"
    echo "- validation: 数据验证测试"
    echo "- search: 搜索过滤测试"
    echo "- organization: 组织架构测试"
    echo "- group: 用户组测试"
    echo "- audit: 审计日志测试"
    echo "- performance: 性能测试"
    echo "- security: 安全测试"
    echo "- compatibility: 兼容性测试"
    echo ""
    
    read -p "请输入测试类别: " category
    
    echo ""
    echo "🚀 运行 $category 测试..."
    echo "----------------------------------------"
    
    if python tests/scripts/run_user_api_tests.py --category "$category"; then
        echo ""
        echo "✅ $category 测试完成"
    else
        echo ""
        echo "❌ $category 测试失败"
        return 1
    fi
}

run_django_test() {
    echo ""
    echo "Django测试选项:"
    echo "1. 所有用户相关测试"
    echo "2. CRUD自动化测试"
    echo "3. CRUD API测试"
    echo "4. 兼容性测试"
    echo "5. 返回主菜单"
    echo ""
    
    read -p "请输入选择 (1-5): " django_choice
    
    case $django_choice in
        1)
            echo ""
            echo "🚀 运行所有用户相关测试..."
            echo "----------------------------------------"
            python manage.py test tests.unit tests.integration --verbosity=2
            ;;
        2)
            echo ""
            echo "🚀 运行CRUD自动化测试..."
            echo "----------------------------------------"
            python manage.py test tests.unit.test_user_crud_automation --verbosity=2
            ;;
        3)
            echo ""
            echo "🚀 运行CRUD API测试..."
            echo "----------------------------------------"
            python manage.py test tests.unit.test_user_crud_api --verbosity=2
            ;;
        4)
            echo ""
            echo "🚀 运行兼容性测试..."
            echo "----------------------------------------"
            python manage.py test tests.integration.test_user_crud_compatibility --verbosity=2
            ;;
        5)
            return 0
            ;;
        *)
            echo "❌ 无效选择"
            return 1
            ;;
    esac
}

run_coverage_test() {
    echo ""
    echo "🚀 运行测试覆盖率分析..."
    echo "----------------------------------------"
    
    # 检查coverage是否安装
    if ! command -v coverage &> /dev/null; then
        echo "⚠️  coverage未安装，正在安装..."
        pip install coverage
    fi
    
    echo "运行测试并收集覆盖率数据..."
    coverage run --source='.' manage.py test apps.users --verbosity=1
    
    echo ""
    echo "生成覆盖率报告..."
    coverage report
    
    echo ""
    echo "生成HTML覆盖率报告..."
    coverage html
    
    echo ""
    echo "✅ 覆盖率分析完成"
    echo "📄 HTML报告位置: htmlcov/index.html"
}

# 主循环
while true; do
    show_menu
    read -p "请输入选择 (1-6): " choice
    
    case $choice in
        1)
            run_quick_test
            ;;
        2)
            run_full_test
            ;;
        3)
            run_category_test
            ;;
        4)
            run_django_test
            ;;
        5)
            run_coverage_test
            ;;
        6)
            echo ""
            echo "👋 退出测试"
            break
            ;;
        *)
            echo ""
            echo "❌ 无效选择，请重新输入"
            ;;
    esac
    
    echo ""
    echo "按Enter键继续..."
    read
done

echo ""
echo "========================================"
echo "测试完成"
echo "========================================"
echo ""
echo "📄 测试报告位置: reports/"
echo "📚 查看详细文档: docs/testing-guide.md"
echo ""
