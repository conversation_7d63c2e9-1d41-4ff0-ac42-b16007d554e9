#!/usr/bin/env python
"""
将BusinessOperationLog中的details字段替换为extra_data
"""
import re

def fix_details_field():
    """修复details字段为extra_data"""
    file_path = 'apps/passwords/views.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换字典格式的details
    content = re.sub(r'details=(\{[^}]+\})', r'extra_data=\1', content)
    
    # 替换字符串格式的details，需要转换为字典
    content = re.sub(
        r'details=f?"([^"]+)"',
        r'extra_data={"description": f"\1"}',
        content
    )
    
    # 处理没有f前缀的字符串
    content = re.sub(
        r'details="([^"]+)"',
        r'extra_data={"description": "\1"}',
        content
    )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已修复details字段为extra_data")

if __name__ == '__main__':
    fix_details_field()
