# 增强版用户管理API文档

## 概述

经过CRUD API功能合并，传统用户管理API现已具备完整的高级功能，包括高级过滤、查询优化、关联查询和用户组管理等特性。

## API端点总览

### 🔐 认证相关API (保持不变)
```
POST   /api/auth/login/                    - 用户登录
POST   /api/auth/logout/                   - 用户登出
POST   /api/auth/token/refresh/            - 令牌刷新
POST   /api/auth/password/change/          - 密码修改
POST   /api/auth/password/reset/           - 密码重置
POST   /api/auth/password/reset/confirm/   - 密码重置确认
POST   /api/auth/password/first-login-change/ - 首次登录密码修改
POST   /api/auth/mfa/setup/                - MFA设置
GET    /api/auth/mfa/qrcode/               - MFA二维码
GET    /api/auth/profile/                  - 用户个人资料
GET    /api/auth/codes/                    - 访问权限代码
GET    /api/auth/menu/all/                 - 菜单列表
```

### 👥 用户管理API (增强版)
```
GET    /api/auth/users/                    - 用户列表 (支持高级过滤)
POST   /api/auth/users/                    - 创建用户
GET    /api/auth/users/{id}/               - 用户详情
PUT    /api/auth/users/{id}/               - 更新用户
DELETE /api/auth/users/{id}/               - 删除用户
POST   /api/auth/users/{id}/reset-password/ - 重置用户密码 (新增)
POST   /api/auth/users/{id}/toggle-active/ - 切换激活状态 (新增)
```

### 🏢 组织架构API (增强版)
```
# 部门管理
GET    /api/auth/departments/              - 部门列表
POST   /api/auth/departments/              - 创建部门
GET    /api/auth/departments/{id}/         - 部门详情
PUT    /api/auth/departments/{id}/         - 更新部门
DELETE /api/auth/departments/{id}/         - 删除部门
GET    /api/auth/departments/{id}/users/   - 部门用户列表 (新增)
GET    /api/auth/departments/{id}/teams/   - 部门团队列表 (新增)

# 团队管理
GET    /api/auth/teams/                    - 团队列表
POST   /api/auth/teams/                    - 创建团队
GET    /api/auth/teams/{id}/               - 团队详情
PUT    /api/auth/teams/{id}/               - 更新团队
DELETE /api/auth/teams/{id}/               - 删除团队
GET    /api/auth/teams/{id}/users/         - 团队用户列表 (新增)

# 角色管理
GET    /api/auth/roles/                    - 角色列表
POST   /api/auth/roles/                    - 创建角色
GET    /api/auth/roles/{id}/               - 角色详情
PUT    /api/auth/roles/{id}/               - 更新角色
DELETE /api/auth/roles/{id}/               - 删除角色
GET    /api/auth/roles/{id}/users/         - 角色用户列表 (新增)
```

### 👥 用户组管理API (全新功能)
```
GET    /api/auth/groups/                   - 用户组列表
POST   /api/auth/groups/                   - 创建用户组
GET    /api/auth/groups/{id}/              - 用户组详情
PUT    /api/auth/groups/{id}/              - 更新用户组
DELETE /api/auth/groups/{id}/              - 删除用户组
GET    /api/auth/groups/{id}/users/        - 用户组成员列表
POST   /api/auth/groups/{id}/add-users/    - 批量添加用户
POST   /api/auth/groups/{id}/remove-users/ - 批量移除用户
```

## 高级功能

### 🔍 高级过滤 (用户列表)

支持以下过滤参数：
```
GET /api/auth/users/?department=1&team=2&role=3&is_active=true&is_staff=false
```

**过滤字段**：
- `department` - 部门ID
- `team` - 团队ID  
- `role` - 角色ID
- `is_active` - 激活状态 (true/false)
- `is_staff` - 员工状态 (true/false)

### 🔎 搜索功能

支持多字段搜索：
```
GET /api/auth/users/?search=张三
```

**搜索字段**：
- `username` - 用户名
- `email` - 邮箱
- `name` - 姓名
- `first_name` - 名字
- `last_name` - 姓氏

### 📊 排序功能

支持多字段排序：
```
GET /api/auth/users/?ordering=-date_joined,username
```

**排序字段**：
- `username` - 用户名
- `email` - 邮箱
- `name` - 姓名
- `date_joined` - 加入时间
- `created_at` - 创建时间

### 📄 分页

所有列表API都支持分页：
```
GET /api/auth/users/?page=1&page_size=20
```

## 使用示例

### 1. 获取部门用户列表
```bash
curl -X GET "http://localhost:8001/api/auth/departments/1/users/" \
  -H "Authorization: Bearer <token>"
```

### 2. 重置用户密码
```bash
curl -X POST "http://localhost:8001/api/auth/users/1/reset-password/" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"new_password": "NewPassword123!"}'
```

### 3. 切换用户激活状态
```bash
curl -X POST "http://localhost:8001/api/auth/users/1/toggle-active/" \
  -H "Authorization: Bearer <token>"
```

### 4. 向用户组添加用户
```bash
curl -X POST "http://localhost:8001/api/auth/groups/1/add-users/" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"user_ids": [1, 2, 3]}'
```

### 5. 高级过滤查询
```bash
curl -X GET "http://localhost:8001/api/auth/users/?department=1&is_active=true&search=admin&ordering=-date_joined" \
  -H "Authorization: Bearer <token>"
```

## 权限要求

- **认证要求**: 所有API都需要JWT令牌认证
- **权限控制**: 
  - 普通用户只能查看自己的信息
  - 管理员可以管理所有用户和组织架构
  - 用户组管理需要相应权限

## 响应格式

### 成功响应
```json
{
  "count": 10,
  "next": "http://localhost:8001/api/auth/users/?page=2",
  "previous": null,
  "results": [...]
}
```

### 错误响应
```json
{
  "error": "错误信息",
  "detail": "详细错误描述"
}
```

## 性能优化

### 查询优化
- 使用 `select_related()` 优化外键查询
- 使用 `prefetch_related()` 优化多对多关系
- 数据库索引优化

### 缓存策略
- 组织架构数据缓存
- 用户权限信息缓存
- 查询结果缓存

## 审计日志

所有重要操作都会记录审计日志：
- 用户创建、更新、删除
- 密码重置
- 激活状态变更
- 用户组成员变更

## 迁移说明

### 从CRUD API迁移

如果您之前使用了CRUD API，请注意：

1. **URL变更**: 
   - 旧: `/api/auth/crud/users/`
   - 新: `/api/auth/users/`

2. **功能保持**: 所有CRUD功能都已迁移到传统API中

3. **新增功能**: 
   - 用户密码重置
   - 用户激活状态切换
   - 关联查询功能
   - 用户组管理

## 总结

增强版用户管理API提供了：

✅ **统一的架构**: 所有功能集中在传统API中  
✅ **强大的功能**: 高级过滤、搜索、排序、关联查询  
✅ **优秀的性能**: 查询优化、缓存策略  
✅ **完善的权限**: 细粒度权限控制  
✅ **详细的日志**: 完整的审计轨迹  
✅ **向后兼容**: 保持现有API结构不变  

现在您拥有一个功能完整、性能优秀、易于维护的用户管理API系统！
