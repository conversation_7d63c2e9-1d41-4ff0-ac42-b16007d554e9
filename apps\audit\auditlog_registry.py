"""
Auditlog模型注册
注册需要进行审计跟踪的模型
"""

from auditlog.registry import auditlog
from django.contrib.auth import get_user_model

# 导入需要审计的模型
User = get_user_model()


def register_models():
    """注册需要审计的模型"""

    # 用户相关模型
    from apps.users.models import User, Department, Role

    auditlog.register(
        User,
        exclude_fields=[
            "password",
            "mfa_secret",
            "last_login",
            "failed_login_attempts",
            "locked_until",
        ],
    )
    auditlog.register(Department)
    auditlog.register(Role)

    # 密码相关模型
    try:
        from apps.passwords.models import PasswordEntry, Category

        auditlog.register(
            PasswordEntry,
            exclude_fields=[
                "password",  # 排除敏感的密码字段
            ],
        )
        auditlog.register(Category)
    except ImportError:
        pass

    # 分享相关模型
    try:
        from apps.sharing.models import PasswordShare, OneTimeLink

        auditlog.register(PasswordShare)
        auditlog.register(OneTimeLink)
    except ImportError:
        pass

    # 系统相关模型
    try:
        from apps.system.models import SystemSetting

        auditlog.register(SystemSetting)
    except ImportError:
        pass


# 自动注册
register_models()
