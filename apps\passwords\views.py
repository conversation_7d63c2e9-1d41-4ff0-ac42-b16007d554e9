from rest_framework import status, generics, permissions, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from django.core.exceptions import ValidationError
from django.db.models import Q, Count
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from datetime import timedelta
from django.shortcuts import get_object_or_404
from .models import (
    PasswordEntry,
    Category,
    CustomField,
    Attachment,
    PasswordHistory,
    PasswordEntryGroup,
    GroupPermission,
    PasswordEntryGroupMembership,
    PasswordPolicy,
)
from .serializers import (
    PasswordEntrySerializer,
    PasswordEntryDetailSerializer,
    PasswordEntryCreateSerializer,
    CategorySerializer,
    CustomFieldSerializer,
    AttachmentSerializer,
    PasswordGeneratorSerializer,
    PasswordSearchSerializer,
    PasswordEntryGroupSerializer,
    PasswordEntryGroupDetailSerializer,
    GroupPermissionSerializer,
    PasswordEntryGroupMembershipSerializer,
    PasswordPolicySerializer,
    PasswordGenerateSerializer,
    PasswordValidateSerializer,
)
from .filters import PasswordEntryFilter, PasswordEntrySystemFilter
from rest_framework import serializers
from apps.audit.models import BusinessOperationLog, PasswordAccessLog
from utils.encryption import decrypt_data
from utils.password_decorators import (
    require_password_permission_drf,
    check_password_owner,
    get_user_permission_level,
)
from utils.password_permissions import password_permission_service
import logging

logger = logging.getLogger(__name__)


class CustomPageNumberPagination(PageNumberPagination):
    """自定义分页类，支持动态页面大小"""

    page_size = 20
    page_size_query_param = "page_size"
    max_page_size = 100


class PasswordEntryListCreateView(generics.ListCreateAPIView):
    """密码条目列表和创建视图"""

    permission_classes = [permissions.IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = PasswordEntryFilter
    search_fields = ["title", "username", "ip_address", "url", "notes"]
    ordering_fields = ["title", "created_at", "updated_at", "last_accessed_at"]
    ordering = ["-updated_at"]

    def get_queryset(self):
        """获取当前用户有权限访问的密码条目"""
        user = self.request.user

        # 获取筛选类型参数
        filter_type = self.request.query_params.get("filter_type", "all")
        group_id = self.request.query_params.get("group_id")

        # 根据筛选类型构建基础查询集
        if filter_type == "personal":
            # 只显示个人密码
            queryset = PasswordEntry.objects.filter(owner=user, is_deleted=False)
        elif filter_type == "group" and group_id:
            # 显示指定密码组中的密码
            from .models import PasswordEntryGroupMembership

            group_password_ids = PasswordEntryGroupMembership.objects.filter(
                group_id=group_id
            ).values_list("password_entry_id", flat=True)

            queryset = PasswordEntry.objects.filter(
                id__in=group_password_ids, is_deleted=False
            )
        elif filter_type == "favorite":
            # 只显示收藏的密码（个人密码中的收藏）
            queryset = PasswordEntry.objects.filter(
                owner=user, is_deleted=False, is_favorite=True
            )
        else:
            # 显示所有有权限访问的密码（个人密码 + 权限密码）
            # 获取用户直接拥有的密码条目
            owned_passwords = Q(owner=user)

            # 获取用户有直接权限的密码条目
            user_permissions = (
                password_permission_service.PasswordPermission.objects.filter(
                    permission_type="user", target_id=user.id, is_active=True
                ).values_list("password_id", flat=True)
            )

            # 获取用户通过用户组获得权限的密码条目
            user_groups = (
                password_permission_service.UserGroupMembership.objects.filter(
                    user=user, is_active=True
                ).values_list("group_id", flat=True)
            )

            group_permissions = (
                password_permission_service.PasswordPermission.objects.filter(
                    permission_type="group", target_id__in=user_groups, is_active=True
                ).values_list("password_id", flat=True)
            )

            # 合并所有有权限的密码ID
            accessible_password_ids = list(user_permissions) + list(group_permissions)

            # 构建查询条件
            queryset = PasswordEntry.objects.filter(
                Q(owner=user) | Q(id__in=accessible_password_ids),
                is_deleted=False,  # 排除已删除的密码
            ).distinct()

        # 筛选逻辑现在由django-filters自动处理
        return queryset.select_related("category", "owner").prefetch_related(
            "custom_fields", "attachments"
        )

    def get_serializer_class(self):
        if self.request.method == "POST":
            return PasswordEntryCreateSerializer
        return PasswordEntrySerializer

    def perform_create(self, serializer):
        logger.info(f"创建密码条目，验证数据: {serializer.validated_data}")

        # 确保创建者成为owner
        password_entry = serializer.save(owner=self.request.user)

        # 记录操作日志
        try:
            BusinessOperationLog.objects.create(
                user=self.request.user,
                action_type="password_create",
                target_type="password_entry",
                target_id=str(password_entry.id, ip_address="127.0.0.1", extra_data={}),
                target_name=password_entry.title,
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
                extra_data={"title": password_entry.title},
            )
        except Exception as e:
            logger.error(f"记录密码创建日志失败: {e}")

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class PasswordEntryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """密码条目详情视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        return PasswordEntryDetailSerializer

    def get_queryset(self):
        # 移除owner过滤，改为使用权限检查
        return PasswordEntry.objects.filter(is_deleted=False)

    @require_password_permission_drf("browse")
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()

        # 更新访问记录
        instance.last_used = timezone.now()
        instance.save(update_fields=["last_used"])

        # 记录密码访问日志
        try:
            PasswordAccessLog.objects.create(
                user=request.user,
                password_entry=instance,
                access_type="view",
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
            )
        except Exception as e:
            logger.error(f"记录密码访问日志失败: {e}")

        # 使用权限感知序列化器，自动处理权限控制
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @require_password_permission_drf("write")
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @require_password_permission_drf("write")
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    def perform_update(self, serializer):
        password_entry = serializer.save()

        # 记录操作日志
        try:
            BusinessOperationLog.objects.create(
                user=self.request.user,
                action_type="password_update",
                target_type="password_entry",
                target_id=str(password_entry.id, ip_address="127.0.0.1", extra_data={}),
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
                extra_data={
                    "title": password_entry.title,
                    "updated_fields": list(serializer.validated_data.keys()),
                },
            )
        except Exception as e:
            logger.error(f"记录密码更新日志失败: {e}")

    @require_password_permission_drf("admin")
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    def perform_destroy(self, instance):
        # 记录操作日志
        try:
            BusinessOperationLog.objects.create(
                user=self.request.user,
                action_type="password_soft_delete",
                target_type="password_entry",
                target_id=str(instance.id, ip_address="127.0.0.1", extra_data={}),
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
                extra_data={"title": instance.title},
            )
        except Exception as e:
            logger.error(f"记录密码删除日志失败: {e}")

        # 软删除而不是物理删除
        instance.soft_delete(user=self.request.user)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class PasswordCopyView(APIView):
    """密码复制视图（记录复制操作）"""

    permission_classes = [permissions.IsAuthenticated]

    @require_password_permission_drf("read")
    def post(self, request, pk):
        try:
            password_entry = PasswordEntry.objects.get(pk=pk, is_deleted=False)
        except PasswordEntry.DoesNotExist:
            return Response(
                {"error": _("密码条目不存在")}, status=status.HTTP_404_NOT_FOUND
            )

        # 记录密码访问日志
        try:
            PasswordAccessLog.objects.create(
                user=request.user,
                password_entry=password_entry,
                access_type="copy",
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
            )
        except Exception as e:
            logger.error(f"记录密码复制日志失败: {e}")

        # 解密并返回密码
        try:
            logger.info(f"开始解密密码，加密数据长度: {len(password_entry.password)}")
            decrypted_password = decrypt_data(password_entry.password)
            logger.info(f"密码解密成功，解密后长度: {len(decrypted_password)}")
            return Response({"password": decrypted_password}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"密码解密失败: {e}")
            logger.error(f"加密数据: {password_entry.password[:100]}...")
            return Response(
                {"error": _("密码解密失败")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class PasswordToggleFavoriteView(APIView):
    """密码收藏切换视图"""

    permission_classes = [permissions.IsAuthenticated]

    @require_password_permission_drf("write")
    def post(self, request, pk):
        try:
            password_entry = PasswordEntry.objects.get(pk=pk, is_deleted=False)

            # 切换收藏状态
            password_entry.is_favorite = not password_entry.is_favorite
            password_entry.save(update_fields=["is_favorite"])

            # 记录操作日志
            try:
                BusinessOperationLog.objects.create(
                    user=request.user,
                    action_type="password_toggle_favorite",
                    target_type="password_entry",
                    target_id=str(
                        password_entry.id, ip_address="127.0.0.1", extra_data={}
                    ),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    extra_data={
                        "title": password_entry.title,
                        "is_favorite": password_entry.is_favorite,
                    },
                )
            except Exception as e:
                logger.error(f"记录收藏切换日志失败: {e}")

            return Response(
                {
                    "message": _("收藏状态已更新"),
                    "is_favorite": password_entry.is_favorite,
                },
                status=status.HTTP_200_OK,
            )

        except PasswordEntry.DoesNotExist:
            return Response(
                {"error": _("密码条目不存在")}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"收藏切换失败: {e}")
            return Response(
                {"error": _("操作失败")}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class PasswordGeneratorView(APIView):
    """密码生成器视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = PasswordGeneratorSerializer(data=request.data)

        if serializer.is_valid():
            try:
                result = serializer.generate_password()

                # 记录密码生成日志
                BusinessOperationLog.objects.create(
                    user=request.user,
                    action_type="password_generate",
                    target_type="password_generator",
                    ip_address="127.0.0.1",
                    extra_data={
                        "length": serializer.validated_data["length"],
                        "strength": result["strength"],
                    },
                )

                return Response(result, status=status.HTTP_200_OK)
            except Exception as e:
                logger.error(f"密码生成失败: {e}")
                return Response(
                    {"error": _("密码生成失败")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class CategoryListCreateView(generics.ListCreateAPIView):
    """分类列表和创建视图"""

    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return (
            Category.objects.filter(user=self.request.user)
            .annotate(password_count=Count("passwordentry"))
            .order_by("name")
        )

    def perform_create(self, serializer):
        category = serializer.save()

        # 记录操作日志
        try:
            BusinessOperationLog.objects.create(
                user=self.request.user,
                action_type="category_create",
                target_type="category",
                target_id=str(category.id, ip_address="127.0.0.1", extra_data={}),
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
                extra_data={"name": category.name},
            )
        except Exception as e:
            logger.error(f"记录分类创建日志失败: {e}")

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class CategoryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """分类详情视图"""

    serializer_class = CategorySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Category.objects.filter(user=self.request.user)

    def perform_update(self, serializer):
        category = serializer.save()

        # 记录操作日志
        try:
            BusinessOperationLog.objects.create(
                user=self.request.user,
                action_type="category_update",
                target_type="category",
                target_id=str(category.id, ip_address="127.0.0.1", extra_data={}),
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
                extra_data={"name": category.name},
            )
        except Exception as e:
            logger.error(f"记录分类更新日志失败: {e}")

    def perform_destroy(self, instance):
        # 检查是否有密码条目使用此分类
        if instance.passwordentry_set.exists():
            raise ValidationError(_("无法删除包含密码条目的分类"))

        # 记录操作日志
        try:
            BusinessOperationLog.objects.create(
                user=self.request.user,
                action_type="category_delete",
                target_type="category",
                target_id=str(instance.id, ip_address="127.0.0.1", extra_data={}),
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
                extra_data={"name": instance.name},
            )
        except Exception as e:
            logger.error(f"记录分类删除日志失败: {e}")

        instance.delete()

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class CustomFieldListCreateView(generics.ListCreateAPIView):
    """自定义字段列表和创建视图"""

    serializer_class = CustomFieldSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        password_entry_id = self.kwargs.get("password_entry_id")
        return CustomField.objects.filter(
            password_entry_id=password_entry_id, password_entry__owner=self.request.user
        )

    def perform_create(self, serializer):
        password_entry_id = self.kwargs.get("password_entry_id")
        try:
            password_entry = PasswordEntry.objects.get(
                id=password_entry_id, owner=self.request.user
            )
            serializer.save(password_entry=password_entry)
        except PasswordEntry.DoesNotExist:
            raise serializers.ValidationError(_("密码条目不存在"))


class CustomFieldDetailView(generics.RetrieveUpdateDestroyAPIView):
    """自定义字段详情视图"""

    serializer_class = CustomFieldSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return CustomField.objects.filter(password_entry__owner=self.request.user)


class AttachmentListCreateView(generics.ListCreateAPIView):
    """附件列表和创建视图"""

    serializer_class = AttachmentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        password_entry_id = self.kwargs.get("password_entry_id")
        return Attachment.objects.filter(
            password_entry_id=password_entry_id, password_entry__owner=self.request.user
        )

    def perform_create(self, serializer):
        password_entry_id = self.kwargs.get("password_entry_id")
        try:
            password_entry = PasswordEntry.objects.get(
                id=password_entry_id, owner=self.request.user
            )
            serializer.save(password_entry=password_entry)
        except PasswordEntry.DoesNotExist:
            raise serializers.ValidationError(_("密码条目不存在"))


class AttachmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """附件详情视图"""

    serializer_class = AttachmentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Attachment.objects.filter(password_entry__owner=self.request.user)


class PasswordSecurityAnalysisView(APIView):
    """密码安全分析视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        user = request.user
        password_entries = PasswordEntry.objects.filter(owner=user)

        analysis = {
            "total_passwords": password_entries.count(),
            "weak_passwords": 0,
            "duplicate_passwords": 0,
            "expired_passwords": 0,
            "expiring_soon": 0,
            "strength_distribution": {"weak": 0, "medium": 0, "strong": 0},
        }

        # 分析密码强度和重复情况
        password_hashes = {}
        now = timezone.now()
        next_month = now + timedelta(days=30)

        for entry in password_entries:
            # 检查过期
            if entry.expires_at:
                if entry.expires_at < now:
                    analysis["expired_passwords"] += 1
                elif entry.expires_at <= next_month:
                    analysis["expiring_soon"] += 1

            # 检查重复密码
            if entry.password in password_hashes:
                password_hashes[entry.password] += 1
            else:
                password_hashes[entry.password] = 1

            # 计算密码强度（简化版本）
            try:
                decrypted_password = decrypt_data(entry.password)
                strength = self.calculate_password_strength(decrypted_password)

                if strength < 40:
                    analysis["weak_passwords"] += 1
                    analysis["strength_distribution"]["weak"] += 1
                elif strength < 70:
                    analysis["strength_distribution"]["medium"] += 1
                else:
                    analysis["strength_distribution"]["strong"] += 1
            except Exception:
                analysis["weak_passwords"] += 1
                analysis["strength_distribution"]["weak"] += 1

        # 统计重复密码
        analysis["duplicate_passwords"] = sum(
            1 for count in password_hashes.values() if count > 1
        )

        return Response(analysis, status=status.HTTP_200_OK)

    def calculate_password_strength(self, password):
        """计算密码强度（0-100）"""
        if not password:
            return 0

        score = 0

        # 长度评分
        length = len(password)
        if length >= 12:
            score += 25
        elif length >= 8:
            score += 15
        elif length >= 6:
            score += 10

        # 字符类型评分
        import re

        if re.search(r"[a-z]", password):
            score += 15
        if re.search(r"[A-Z]", password):
            score += 15
        if re.search(r"\d", password):
            score += 15
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            score += 20

        # 复杂度评分
        if len(set(password)) > len(password) * 0.7:  # 字符多样性
            score += 10

        return min(score, 100)


# ============================================================================
# 系统密码管理视图
# ============================================================================


class PasswordEntrySystemListView(generics.ListAPIView):
    """密码条目系统列表视图 - 支持运维专业筛选"""

    serializer_class = PasswordEntrySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = PasswordEntrySystemFilter
    search_fields = ["title", "username", "ip_address", "url", "notes", "project_name"]
    ordering_fields = [
        "title",
        "created_at",
        "updated_at",
        "system_type",
        "environment",
    ]
    ordering = ["-is_pinned", "system_type", "environment", "ip_address"]

    def get_queryset(self):
        """获取当前用户的系统密码"""
        user = self.request.user
        return (
            PasswordEntry.objects.filter(owner=user, is_deleted=False)
            .select_related("category", "owner")
            .prefetch_related("custom_fields", "attachments")
        )


class PasswordEntrySystemStatsView(APIView):
    """密码条目系统统计视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取系统统计信息"""
        user = request.user
        queryset = PasswordEntry.objects.filter(owner=user)

        # 按系统类型统计
        system_stats = (
            queryset.values("system_type")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        # 按环境统计
        env_stats = (
            queryset.exclude(environment="")
            .values("environment")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        # 按数据库类型统计
        db_stats = (
            queryset.exclude(database_type="")
            .values("database_type")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        # 按协议统计
        protocol_stats = (
            queryset.exclude(protocol="")
            .values("protocol")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        # 安全统计
        total_passwords = queryset.count()
        weak_passwords = queryset.filter(strength__in=["weak", "very_weak"]).count()
        expired_passwords = queryset.filter(expires_at__lt=timezone.now()).count()
        expiring_soon = queryset.filter(
            expires_at__lte=timezone.now() + timezone.timedelta(days=30),
            expires_at__gte=timezone.now(),
        ).count()

        # 最近使用统计
        recently_used = queryset.filter(
            last_used__gte=timezone.now() - timezone.timedelta(days=7)
        ).count()

        return Response(
            {
                "total_passwords": total_passwords,
                "system_type_stats": list(system_stats),
                "environment_stats": list(env_stats),
                "database_type_stats": list(db_stats),
                "protocol_stats": list(protocol_stats),
                "security_stats": {
                    "weak_passwords": weak_passwords,
                    "expired_passwords": expired_passwords,
                    "expiring_soon": expiring_soon,
                    "weak_password_rate": (
                        round(weak_passwords / total_passwords * 100, 2)
                        if total_passwords > 0
                        else 0
                    ),
                },
                "usage_stats": {
                    "recently_used": recently_used,
                    "usage_rate": (
                        round(recently_used / total_passwords * 100, 2)
                        if total_passwords > 0
                        else 0
                    ),
                },
            }
        )


@api_view(["GET"])
@permission_classes([permissions.IsAuthenticated])
def get_system_types(request):
    """获取系统类型选项"""
    return Response(PasswordEntry.SYSTEM_TYPE_CHOICES)


@api_view(["GET"])
@permission_classes([permissions.IsAuthenticated])
def get_environments(request):
    """获取环境类型选项"""
    return Response(
        [
            ("dev", "开发环境"),
            ("test", "测试环境"),
            ("staging", "预发布环境"),
            ("prod", "生产环境"),
        ]
    )


@api_view(["GET"])
@permission_classes([permissions.IsAuthenticated])
def get_database_types(request):
    """获取数据库类型选项"""
    return Response(PasswordEntry.DATABASE_TYPE_CHOICES)


@api_view(["GET"])
@permission_classes([permissions.IsAuthenticated])
def get_os_types(request):
    """获取操作系统类型选项"""
    return Response(PasswordEntry.OS_TYPE_CHOICES)


@api_view(["GET"])
@permission_classes([permissions.IsAuthenticated])
def get_mdw_types(request):
    """获取中间件类型选项"""
    return Response(PasswordEntry.MDW_TYPE_CHOICES)


class PasswordEntryGroupListCreateView(generics.ListCreateAPIView):
    """密码组列表和创建视图"""

    serializer_class = PasswordEntryGroupSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name", "description"]
    ordering_fields = ["name", "created_at", "updated_at"]
    ordering = ["-updated_at"]

    def get_queryset(self):
        """获取用户有权限的组"""
        user = self.request.user

        # 获取用户创建的组
        created_groups = PasswordEntryGroup.objects.filter(
            created_by=user, is_active=True
        )

        # 获取用户有权限的组
        permission_groups = PasswordEntryGroup.objects.filter(
            grouppermission__user=user, is_active=True
        ).distinct()

        # 合并查询集
        return PasswordEntryGroup.objects.filter(
            Q(created_by=user) | Q(grouppermission__user=user), is_active=True
        ).distinct()

    def perform_create(self, serializer):
        """创建密码组时自动给创建者分配admin权限"""
        # 设置创建者
        serializer.validated_data["created_by"] = self.request.user

        # 保存密码组
        password_group = serializer.save()

        # 自动给创建者分配admin权限
        from .models import GroupPermission

        GroupPermission.objects.create(
            user=self.request.user,
            group=password_group,
            permission="admin",
            granted_by=self.request.user,
        )


class PasswordEntryGroupDetailView(generics.RetrieveUpdateDestroyAPIView):
    """密码组详情视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        """根据请求方法返回不同的序列化器"""
        if self.request.method == "GET":
            return PasswordEntryGroupDetailSerializer
        return PasswordEntryGroupSerializer

    def get_queryset(self):
        """获取用户有权限的组"""
        user = self.request.user
        return PasswordEntryGroup.objects.filter(
            Q(created_by=user) | Q(grouppermission__user=user), is_active=True
        ).distinct()

    def perform_update(self, serializer):
        """更新组时检查权限"""
        group = self.get_object()
        user = self.request.user

        # 检查用户是否有管理权限
        if group.created_by != user and not group.has_permission(user, "manage"):
            raise serializers.ValidationError(_("您没有权限修改此组"))

        serializer.save()

    def perform_destroy(self, instance):
        """删除组时检查权限"""
        user = self.request.user

        # 只有创建者或管理员可以删除组
        if instance.created_by != user and not instance.has_permission(user, "admin"):
            raise serializers.ValidationError(_("您没有权限删除此组"))

        # 软删除
        instance.is_active = False
        instance.save()


class GroupPermissionListCreateView(generics.ListCreateAPIView):
    """组权限列表和创建视图"""

    serializer_class = GroupPermissionSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    filter_backends = [filters.SearchFilter]
    search_fields = ["user__username", "group__name"]

    def get_queryset(self):
        """获取用户有权限管理的组的权限列表"""
        user = self.request.user

        # 获取用户有管理权限的组
        manageable_groups = PasswordEntryGroup.objects.filter(
            Q(created_by=user)
            | Q(
                grouppermission__user=user,
                grouppermission__permission__in=["manage", "admin"],
            ),
            is_active=True,
        ).distinct()

        return GroupPermission.objects.filter(group__in=manageable_groups)

    def perform_create(self, serializer):
        """创建权限时检查权限"""
        group = serializer.validated_data["group"]
        user = self.request.user

        # 检查用户是否有权限管理该组
        if group.created_by != user and not group.has_permission(user, "manage"):
            raise serializers.ValidationError(_("您没有权限管理此组的权限"))

        serializer.save()


class GroupPermissionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """组权限详情视图"""

    serializer_class = GroupPermissionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取用户有权限管理的组权限"""
        user = self.request.user

        manageable_groups = PasswordEntryGroup.objects.filter(
            Q(created_by=user)
            | Q(
                grouppermission__user=user,
                grouppermission__permission__in=["manage", "admin"],
            ),
            is_active=True,
        ).distinct()

        return GroupPermission.objects.filter(group__in=manageable_groups)

    def perform_update(self, serializer):
        """更新权限时检查权限"""
        permission = self.get_object()
        user = self.request.user

        # 检查用户是否有权限管理该组
        if permission.group.created_by != user and not permission.group.has_permission(
            user, "manage"
        ):
            raise serializers.ValidationError(_("您没有权限修改此权限"))

        serializer.save()

    def perform_destroy(self, instance):
        """删除权限时检查权限"""
        user = self.request.user

        # 检查用户是否有权限管理该组
        if instance.group.created_by != user and not instance.group.has_permission(
            user, "manage"
        ):
            raise serializers.ValidationError(_("您没有权限删除此权限"))

        instance.delete()


class PasswordEntryGroupMembershipListCreateView(generics.ListCreateAPIView):
    """密码条目组成员关系列表和创建视图"""

    serializer_class = PasswordEntryGroupMembershipSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ["password_entry__title", "group__name"]

    def get_queryset(self):
        """获取用户有权限管理的组成员关系"""
        user = self.request.user

        # 获取用户有管理权限的组
        manageable_groups = PasswordEntryGroup.objects.filter(
            Q(created_by=user)
            | Q(
                grouppermission__user=user,
                grouppermission__permission__in=["manage", "admin"],
            ),
            is_active=True,
        ).distinct()

        return PasswordEntryGroupMembership.objects.filter(group__in=manageable_groups)

    def perform_create(self, serializer):
        """创建成员关系时检查权限"""
        group = serializer.validated_data["group"]
        password_entry = serializer.validated_data["password_entry"]
        user = self.request.user

        # 检查用户是否有权限管理该组
        if group.created_by != user and not group.has_permission(user, "manage"):
            raise serializers.ValidationError(_("您没有权限管理此组"))

        # 检查用户是否有权限访问该密码条目
        if password_entry.owner != user:
            # 检查用户是否通过其他组有权限访问该密码条目
            user_groups = GroupPermission.objects.filter(user=user).values_list(
                "group", flat=True
            )
            if not password_entry.groups.filter(id__in=user_groups).exists():
                raise serializers.ValidationError(_("您没有权限访问此密码条目"))

        serializer.save()


class PasswordEntryGroupMembershipDetailView(generics.RetrieveDestroyAPIView):
    """密码条目组成员关系详情视图"""

    serializer_class = PasswordEntryGroupMembershipSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取用户有权限管理的组成员关系"""
        user = self.request.user

        manageable_groups = PasswordEntryGroup.objects.filter(
            Q(created_by=user)
            | Q(
                grouppermission__user=user,
                grouppermission__permission__in=["manage", "admin"],
            ),
            is_active=True,
        ).distinct()

        return PasswordEntryGroupMembership.objects.filter(group__in=manageable_groups)

    def perform_destroy(self, instance):
        """删除成员关系时检查权限"""
        user = self.request.user

        # 检查用户是否有权限管理该组
        if instance.group.created_by != user and not instance.group.has_permission(
            user, "manage"
        ):
            raise serializers.ValidationError(_("您没有权限管理此组"))

        instance.delete()


# PasswordPolicy Views
class PasswordPolicyListView(generics.ListCreateAPIView):
    """密码策略列表和创建视图"""

    serializer_class = PasswordPolicySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name", "description"]
    ordering_fields = ["name", "created_at", "is_default"]
    ordering = ["-is_default", "-created_at"]

    def get_queryset(self):
        return PasswordPolicy.objects.filter(is_active=True)


class PasswordPolicyDetailView(generics.RetrieveUpdateDestroyAPIView):
    """密码策略详情、更新和删除视图"""

    serializer_class = PasswordPolicySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return PasswordPolicy.objects.filter(is_active=True)

    def perform_destroy(self, instance):
        # 不能删除默认策略
        if instance.is_default:
            raise ValidationError(_("不能删除默认密码策略"))

        # 软删除
        instance.is_active = False
        instance.save()


@api_view(["POST"])
@permission_classes([permissions.IsAuthenticated])
def generate_password_by_policy(request):
    """根据密码策略生成密码"""
    serializer = PasswordGenerateSerializer(data=request.data)
    if serializer.is_valid():
        data = serializer.validated_data
        policy_id = data.get("policy_id")

        # 获取密码策略
        if policy_id:
            try:
                policy = PasswordPolicy.objects.get(id=policy_id, is_active=True)
            except PasswordPolicy.DoesNotExist:
                return Response(
                    {"error": _("密码策略不存在")}, status=status.HTTP_404_NOT_FOUND
                )
        else:
            # 使用默认策略
            policy = PasswordPolicy.objects.filter(
                is_default=True, is_active=True
            ).first()
            if not policy:
                return Response(
                    {"error": _("未找到默认密码策略")}, status=status.HTTP_404_NOT_FOUND
                )

        # 如果提供了自定义参数，创建临时策略对象
        if any(
            key in data
            for key in [
                "length",
                "include_uppercase",
                "include_lowercase",
                "include_digits",
                "include_symbols",
            ]
        ):
            # 复制策略并应用自定义参数
            temp_policy = PasswordPolicy(
                name=policy.name,
                min_length=data.get("length", policy.min_length),
                max_length=max(
                    data.get("length", policy.min_length), policy.max_length
                ),
                uppercase_count=1 if data.get("include_uppercase", True) else 0,
                lowercase_count=1 if data.get("include_lowercase", True) else 0,
                digit_count=1 if data.get("include_digits", True) else 0,
                special_char_count=1 if data.get("include_symbols", True) else 0,
                allowed_special_chars=data.get(
                    "custom_symbols", policy.allowed_special_chars
                ),
                allow_repeated_chars=policy.allow_repeated_chars,
                max_repeated_chars=policy.max_repeated_chars,
            )
            policy = temp_policy

        try:
            # 生成密码
            password = policy.generate_password()

            return Response(
                {
                    "password": password,
                    "policy": {
                        "name": policy.name,
                        "min_length": policy.min_length,
                        "uppercase_count": policy.uppercase_count,
                        "lowercase_count": policy.lowercase_count,
                        "digit_count": policy.digit_count,
                        "special_char_count": policy.special_char_count,
                        "allowed_special_chars": policy.allowed_special_chars,
                    },
                }
            )
        except Exception as e:
            return Response(
                {"error": f"密码生成失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(["POST"])
@permission_classes([permissions.IsAuthenticated])
def validate_password_by_policy(request):
    """根据密码策略验证密码"""
    serializer = PasswordValidateSerializer(data=request.data)
    if serializer.is_valid():
        data = serializer.validated_data
        password = data["password"]
        policy_id = data.get("policy_id")

        # 获取密码策略
        if policy_id:
            try:
                policy = PasswordPolicy.objects.get(id=policy_id, is_active=True)
            except PasswordPolicy.DoesNotExist:
                return Response(
                    {"error": _("密码策略不存在")}, status=status.HTTP_404_NOT_FOUND
                )
        else:
            # 使用默认策略
            policy = PasswordPolicy.objects.filter(
                is_default=True, is_active=True
            ).first()
            if not policy:
                return Response(
                    {"error": _("未找到默认密码策略")}, status=status.HTTP_404_NOT_FOUND
                )

        # 创建模拟用户对象（用于个人信息检查）
        class MockUser:
            def __init__(self, username=None, email=None):
                self.username = username or ""
                self.email = email or ""

        mock_user = MockUser(username=data.get("username"), email=data.get("email"))

        # 验证密码
        is_valid, errors = policy.validate_password(password, mock_user)

        return Response(
            {
                "is_valid": is_valid,
                "errors": errors,
                "policy": {
                    "name": policy.name,
                    "requirements": {
                        "min_length": policy.min_length,
                        "uppercase_count": policy.uppercase_count,
                        "lowercase_count": policy.lowercase_count,
                        "digit_count": policy.digit_count,
                        "special_char_count": policy.special_char_count,
                    },
                },
            }
        )

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PasswordUpdateOnlyView(APIView):
    """仅更新密码字段的视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        """仅更新密码字段"""
        try:
            # 获取密码条目
            password_entry = get_object_or_404(PasswordEntry, pk=pk)

            # 检查权限
            user = request.user
            user_groups = GroupPermission.objects.filter(user=user).values_list(
                "group", flat=True
            )

            if not (
                password_entry.owner == user
                or password_entry.groups.filter(id__in=user_groups).exists()
            ):
                return Response(
                    {"error": "无权限访问此密码条目"}, status=status.HTTP_403_FORBIDDEN
                )

            # 验证新密码
            new_password = request.data.get("password")
            if not new_password:
                return Response(
                    {"error": "密码不能为空"}, status=status.HTTP_400_BAD_REQUEST
                )

            # 保存旧密码到历史记录
            old_password = password_entry.password
            if old_password:
                PasswordHistory.objects.create(
                    password_entry=password_entry,
                    old_password=old_password,
                    changed_by=request.user,
                )

            # 加密新密码
            from utils.encryption import encrypt_data

            encrypted_password = encrypt_data(new_password)

            # 更新密码字段
            password_entry.password = encrypted_password
            password_entry._password_encrypted = True
            password_entry.save(update_fields=["password", "updated_at"])

            # 记录操作日志
            try:
                from apps.audit.models import BusinessOperationLog

                BusinessOperationLog.objects.create(
                    user=request.user,
                    action_type="password_update_only",
                    target_type="password_entry",
                    target_id=str(
                        password_entry.id, ip_address="127.0.0.1", extra_data={}
                    ),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    extra_data={
                        "title": password_entry.title,
                        "action": "password_only_update",
                    },
                )
            except Exception as e:
                logger.error(f"记录密码更新日志失败: {e}")

            return Response(
                {"message": "密码更新成功", "updated_at": password_entry.updated_at}
            )

        except Exception as e:
            logger.error(f"密码更新失败: {e}")
            return Response(
                {"error": "密码更新失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class PasswordInfoUpdateView(APIView):
    """仅更新密码信息字段的视图（不包括密码本身）"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        """仅更新密码信息字段"""
        try:
            # 获取密码条目
            password_entry = get_object_or_404(PasswordEntry, pk=pk)

            # 检查权限
            user = request.user
            user_groups = GroupPermission.objects.filter(user=user).values_list(
                "group", flat=True
            )

            if not (
                password_entry.owner == user
                or password_entry.groups.filter(id__in=user_groups).exists()
            ):
                return Response(
                    {"error": "无权限访问此密码条目"}, status=status.HTTP_403_FORBIDDEN
                )

            # 提取可更新的字段（排除密码字段）
            allowed_fields = [
                "title",
                "username",
                "hostname",
                "port",
                "url",
                "protocol",
                "system_type",
                "database_type",
                "database_name",
                "schema_name",
                "environment",
                "project_name",
                "responsible_person",
                "description",
                "notes",
                "category",
                "is_favorite",
                "expires_at",
            ]

            update_data = {}
            for field in allowed_fields:
                if field in request.data:
                    update_data[field] = request.data[field]

            # 处理密码组关联
            group_ids = request.data.get("group_ids")
            if group_ids is not None:
                from .models import PasswordEntryGroup, PasswordEntryGroupMembership

                # 删除现有的组关联
                PasswordEntryGroupMembership.objects.filter(
                    password_entry=password_entry
                ).delete()

                # 添加新的组关联
                for group_id in group_ids:
                    try:
                        group = PasswordEntryGroup.objects.get(
                            id=group_id, is_active=True
                        )
                        PasswordEntryGroupMembership.objects.create(
                            password_entry=password_entry,
                            group=group,
                            added_by=request.user,
                        )
                    except PasswordEntryGroup.DoesNotExist:
                        pass  # 忽略不存在的组

            # 更新字段
            for field, value in update_data.items():
                setattr(password_entry, field, value)

            password_entry.save()

            # 记录操作日志
            try:
                from apps.audit.models import BusinessOperationLog

                BusinessOperationLog.objects.create(
                    user=request.user,
                    action_type="password_info_update",
                    target_type="password_entry",
                    target_id=str(
                        password_entry.id, ip_address="127.0.0.1", extra_data={}
                    ),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    extra_data={
                        "title": password_entry.title,
                        "updated_fields": list(update_data.keys()),
                        "action": "info_only_update",
                    },
                )
            except Exception as e:
                logger.error(f"记录信息更新日志失败: {e}")

            # 返回更新后的数据
            from .serializers import PasswordEntryDetailSerializer

            serializer = PasswordEntryDetailSerializer(
                password_entry, context={"request": request}
            )

            return Response({"message": "信息更新成功", "data": serializer.data})

        except Exception as e:
            logger.error(f"信息更新失败: {e}")
            return Response(
                {"error": "信息更新失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


# 回收站相关视图
class RecycleBinListView(generics.ListAPIView):
    """回收站列表视图"""

    serializer_class = PasswordEntrySerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["title", "username", "hostname", "url", "notes"]
    ordering_fields = ["title", "deleted_at", "updated_at"]
    ordering = ["-deleted_at"]

    def get_queryset(self):
        """获取当前用户已删除的密码条目"""
        user = self.request.user

        # 获取用户有权限的组
        user_groups = GroupPermission.objects.filter(user=user).values_list(
            "group", flat=True
        )

        # 构建查询条件：用户拥有的已删除密码条目 OR 用户有权限的组中的已删除密码条目
        queryset = (
            PasswordEntry.objects.filter(
                Q(owner=user) | Q(groups__in=user_groups),
                is_deleted=True,  # 只显示已删除的密码
            )
            .select_related("category", "owner", "deleted_by")
            .prefetch_related("groups")
            .distinct()
        )

        return queryset


@api_view(["POST"])
@permission_classes([permissions.IsAuthenticated])
def restore_password(request, pk):
    """恢复已删除的密码"""
    try:
        # 获取密码条目
        password_entry = get_object_or_404(PasswordEntry, pk=pk, is_deleted=True)

        # 检查权限
        user = request.user
        user_groups = GroupPermission.objects.filter(user=user).values_list(
            "group", flat=True
        )

        if not (
            password_entry.owner == user
            or password_entry.groups.filter(id__in=user_groups).exists()
        ):
            return Response(
                {"error": "无权限恢复此密码条目"}, status=status.HTTP_403_FORBIDDEN
            )

        # 恢复密码
        password_entry.restore()

        # 记录操作日志
        try:
            BusinessOperationLog.objects.create(
                user=request.user,
                action_type="password_restore",
                target_type="password_entry",
                target_id=str(password_entry.id, ip_address="127.0.0.1", extra_data={}),
                ip_address=get_client_ip(request),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
                extra_data={
                    "title": password_entry.title,
                    "action": "restore",
                },
            )
        except Exception as e:
            logger.error(f"记录密码恢复日志失败: {e}")

        return Response({"message": "密码恢复成功"})

    except Exception as e:
        logger.error(f"恢复密码失败: {e}")
        return Response(
            {"error": "恢复密码失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(["DELETE"])
@permission_classes([permissions.IsAuthenticated])
def permanent_delete_password(request, pk):
    """永久删除密码"""
    try:
        # 获取密码条目
        password_entry = get_object_or_404(PasswordEntry, pk=pk, is_deleted=True)

        # 检查权限
        user = request.user
        user_groups = GroupPermission.objects.filter(user=user).values_list(
            "group", flat=True
        )

        if not (
            password_entry.owner == user
            or password_entry.groups.filter(id__in=user_groups).exists()
        ):
            return Response(
                {"error": "无权限永久删除此密码条目"}, status=status.HTTP_403_FORBIDDEN
            )

        # 记录操作日志
        try:
            BusinessOperationLog.objects.create(
                user=request.user,
                action_type="password_permanent_delete",
                target_type="password_entry",
                target_id=str(password_entry.id, ip_address="127.0.0.1", extra_data={}),
                ip_address=get_client_ip(request),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
                extra_data={
                    "title": password_entry.title,
                    "action": "permanent_delete",
                },
            )
        except Exception as e:
            logger.error(f"记录密码永久删除日志失败: {e}")

        # 永久删除
        password_entry.delete()

        return Response({"message": "密码永久删除成功"})

    except Exception as e:
        logger.error(f"永久删除密码失败: {e}")
        return Response(
            {"error": "永久删除密码失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(["POST"])
@permission_classes([permissions.IsAuthenticated])
def batch_delete_passwords(request):
    """批量删除密码（移动到回收站）"""
    try:
        password_ids = request.data.get("password_ids", [])

        if not password_ids:
            return Response(
                {"error": "请提供要删除的密码ID列表"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not isinstance(password_ids, list):
            return Response(
                {"error": "password_ids必须是列表格式"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # 获取用户拥有的密码条目
        password_entries = PasswordEntry.objects.filter(
            id__in=password_ids, owner=request.user, is_deleted=False
        )

        if not password_entries.exists():
            return Response(
                {"error": "未找到可删除的密码条目"}, status=status.HTTP_404_NOT_FOUND
            )

        # 批量软删除
        deleted_count = 0
        for password_entry in password_entries:
            password_entry.soft_delete(request.user)
            deleted_count += 1

            # 记录操作日志
            try:
                BusinessOperationLog.objects.create(
                    user=request.user,
                    action_type="password_delete",
                    target_type="password_entry",
                    target_id=str(
                        password_entry.id, ip_address="127.0.0.1", extra_data={}
                    ),
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    extra_data={
                        "title": password_entry.title,
                        "hostname": password_entry.hostname,
                        "batch_operation": True,
                    },
                )
            except Exception as e:
                logger.error(f"记录批量删除操作日志失败: {e}")

        return Response(
            {
                "message": f"成功移动 {deleted_count} 个密码到回收站",
                "deleted_count": deleted_count,
                "total_requested": len(password_ids),
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        logger.error(f"批量删除密码失败: {e}")
        return Response(
            {"error": "批量删除密码失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


class PasswordEntryGroupManagementView(APIView):
    """密码组管理视图 - 支持添加/移除密码条目和用户"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, group_id):
        """添加密码条目或用户到组"""
        try:
            group = get_object_or_404(PasswordEntryGroup, id=group_id, is_active=True)
            user = request.user

            # 检查权限
            if group.created_by != user and not group.has_permission(user, "manage"):
                return Response(
                    {"error": "您没有权限管理此组"}, status=status.HTTP_403_FORBIDDEN
                )

            action = request.data.get("action")

            if action == "add_password":
                return self._add_password_to_group(request, group)
            elif action == "add_user":
                return self._add_user_to_group(request, group)
            else:
                return Response(
                    {"error": "无效的操作类型"}, status=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            logger.error(f"密码组管理操作失败: {str(e)}")
            return Response(
                {"error": "操作失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def delete(self, request, group_id):
        """从组中移除密码条目或用户"""
        try:
            group = get_object_or_404(PasswordEntryGroup, id=group_id, is_active=True)
            user = request.user

            # 检查权限
            if group.created_by != user and not group.has_permission(user, "manage"):
                return Response(
                    {"error": "您没有权限管理此组"}, status=status.HTTP_403_FORBIDDEN
                )

            action = request.data.get("action")

            if action == "remove_password":
                return self._remove_password_from_group(request, group)
            elif action == "remove_user":
                return self._remove_user_from_group(request, group)
            else:
                return Response(
                    {"error": "无效的操作类型"}, status=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            logger.error(f"密码组管理操作失败: {str(e)}")
            return Response(
                {"error": "操作失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _add_password_to_group(self, request, group):
        """添加密码条目到组"""
        password_id = request.data.get("password_id")
        if not password_id:
            return Response({"error": "缺少密码ID"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            password_entry = get_object_or_404(
                PasswordEntry, id=password_id, is_deleted=False
            )

            # 检查用户是否有该密码的管理权限
            # 1. 检查是否为密码所有者
            if password_entry.owner != request.user:
                # 2. 如果不是所有者，检查是否有管理权限
                from utils.password_permissions import password_permission_service

                if not password_permission_service.can_admin(
                    str(password_id), request.user.id
                ):
                    return Response(
                        {"error": "您没有权限将此密码添加到组"},
                        status=status.HTTP_403_FORBIDDEN,
                    )

            # 检查是否已经在组中
            if PasswordEntryGroupMembership.objects.filter(
                password_entry=password_entry, group=group
            ).exists():
                return Response(
                    {"error": "密码条目已在该组中"}, status=status.HTTP_400_BAD_REQUEST
                )

            # 添加到组
            membership = PasswordEntryGroupMembership.objects.create(
                password_entry=password_entry, group=group, added_by=request.user
            )

            # 记录操作日志
            try:
                BusinessOperationLog.objects.create(
                    user=request.user,
                    action_type="add_password_to_group",
                    target_type="password_group",
                    target_id=group.id,
                    ip_address="127.0.0.1",
                    extra_data={},
                )
            except Exception as log_error:
                logger.warning(f"记录操作日志失败: {log_error}")

            return Response(
                {"message": "密码条目已成功添加到组", "membership_id": membership.id}
            )

        except PasswordEntry.DoesNotExist:
            return Response(
                {"error": "密码条目不存在"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"添加密码到组失败: {str(e)}")
            return Response(
                {"error": f"添加密码到组失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _add_user_to_group(self, request, group):
        """添加用户到组"""
        from apps.users.models import User

        user_id = request.data.get("user_id")
        permission_level = request.data.get("permission", "view")

        if not user_id:
            return Response({"error": "缺少用户ID"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            target_user = get_object_or_404(User, id=user_id)

            # 检查是否已经在组中
            if GroupPermission.objects.filter(user=target_user, group=group).exists():
                return Response(
                    {"error": "用户已在此组中"}, status=status.HTTP_400_BAD_REQUEST
                )

            # 验证权限级别
            valid_permissions = ["view", "edit", "manage", "admin"]
            if permission_level not in valid_permissions:
                return Response(
                    {"error": "无效的权限级别"}, status=status.HTTP_400_BAD_REQUEST
                )

            # 添加用户到组
            group_permission = GroupPermission.objects.create(
                user=target_user,
                group=group,
                permission=permission_level,
                granted_by=request.user,
            )

            # 记录操作日志
            try:
                BusinessOperationLog.objects.create(
                    user=request.user,
                    action_type="add_user_to_group",
                    target_type="password_group",
                    target_id=str(group.id, ip_address="127.0.0.1", extra_data={}),
                    extra_data={
                        "description": f"将用户 '{target_user.username}' 添加到组 '{group.name}'，权限级别: {permission_level}"
                    },
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )
            except Exception as e:
                logger.error(f"记录操作日志失败: {e}")

            return Response(
                {"message": "用户已成功添加到组", "permission_id": group_permission.id}
            )

        except Exception as e:
            logger.error(f"添加用户到组失败: {e}")
            return Response(
                {"error": "添加用户失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip

    def _remove_password_from_group(self, request, group):
        """从组中移除密码条目"""
        password_id = request.data.get("password_id")
        if not password_id:
            return Response({"error": "缺少密码ID"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            membership = get_object_or_404(
                PasswordEntryGroupMembership, password_entry_id=password_id, group=group
            )

            password_title = membership.password_entry.title
            membership.delete()

            # 记录操作日志
            try:
                BusinessOperationLog.objects.create(
                    user=request.user,
                    action_type="remove_password_from_group",
                    target_type="password_group",
                    target_id=str(group.id, ip_address="127.0.0.1", extra_data={}),
                    extra_data={
                        "description": f"从组 '{group.name}' 移除密码 '{password_title}'"
                    },
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )
            except Exception as e:
                logger.warning(f"记录操作日志失败: {e}")

            return Response({"message": "密码条目已从组中移除"})

        except PasswordEntryGroupMembership.DoesNotExist:
            return Response(
                {"error": "密码条目不在此组中"}, status=status.HTTP_404_NOT_FOUND
            )

    def _remove_user_from_group(self, request, group):
        """从组中移除用户"""
        user_id = request.data.get("user_id")
        if not user_id:
            return Response({"error": "缺少用户ID"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            group_permission = get_object_or_404(
                GroupPermission, user_id=user_id, group=group
            )

            username = group_permission.user.username
            group_permission.delete()

            # 记录操作日志
            try:
                BusinessOperationLog.objects.create(
                    user=request.user,
                    action_type="remove_user_from_group",
                    target_type="password_group",
                    target_id=str(group.id, ip_address="127.0.0.1", extra_data={}),
                    extra_data={
                        "description": f"从组 '{group.name}' 移除用户 '{username}'"
                    },
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )
            except Exception as e:
                logger.warning(f"记录操作日志失败: {e}")

            return Response({"message": "用户已从组中移除"})

        except GroupPermission.DoesNotExist:
            return Response(
                {"error": "用户不在此组中"}, status=status.HTTP_404_NOT_FOUND
            )


class GroupPermissionUpdateView(APIView):
    """组权限更新视图"""

    permission_classes = [permissions.IsAuthenticated]

    def patch(self, request, group_id, permission_id):
        """更新组成员权限"""
        try:
            group = get_object_or_404(PasswordEntryGroup, id=group_id, is_active=True)
            user = request.user

            # 检查权限
            if group.created_by != user and not group.has_permission(user, "manage"):
                return Response(
                    {"error": "您没有权限管理此组"}, status=status.HTTP_403_FORBIDDEN
                )

            group_permission = get_object_or_404(
                GroupPermission, id=permission_id, group=group
            )

            new_permission = request.data.get("permission")
            if not new_permission:
                return Response(
                    {"error": "缺少权限级别"}, status=status.HTTP_400_BAD_REQUEST
                )

            # 验证权限级别
            valid_permissions = ["view", "edit", "manage", "admin"]
            if new_permission not in valid_permissions:
                return Response(
                    {"error": "无效的权限级别"}, status=status.HTTP_400_BAD_REQUEST
                )

            old_permission = group_permission.permission
            group_permission.permission = new_permission
            group_permission.save()

            # 记录操作日志
            try:
                BusinessOperationLog.objects.create(
                    user=request.user,
                    action_type="update_group_permission",
                    target_type="password_group",
                    target_id=str(group.id, ip_address="127.0.0.1", extra_data={}),
                    extra_data={
                        "description": f"更新用户 '{group_permission.user.username}' 在组 '{group.name}' 的权限: {old_permission} -> {new_permission}"
                    },
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )
            except Exception as e:
                logger.warning(f"记录操作日志失败: {e}")

            return Response(
                {
                    "message": "权限已更新",
                    "permission": {
                        "id": group_permission.id,
                        "user_id": group_permission.user.id,
                        "username": group_permission.user.username,
                        "permission": group_permission.permission,
                        "permission_display": group_permission.get_permission_display(),
                    },
                }
            )

        except PasswordEntryGroup.DoesNotExist:
            return Response({"error": "密码组不存在"}, status=status.HTTP_404_NOT_FOUND)
        except GroupPermission.DoesNotExist:
            return Response(
                {"error": "权限记录不存在"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"更新组权限失败: {str(e)}")
            return Response(
                {"error": "更新权限失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
