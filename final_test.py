#!/usr/bin/env python
"""
最终测试修复后的功能
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

import requests
import json

def test_final():
    """最终测试"""
    base_url = "http://localhost:8001/api"
    
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    print("=== 最终测试修复后的功能 ===")
    
    try:
        login_response = requests.post(f"{base_url}/auth/login/", json=login_data)
        if login_response.status_code != 200:
            print(f"登录失败: {login_response.text}")
            return
        
        token = login_response.json().get("access_token")
        headers = {"Authorization": f"Bearer {token}"}
        
        print("✅ 登录成功")
        
        # 1. 测试密码组搜索功能
        print("\n1. 测试密码组搜索功能")
        search_response = requests.get(f"{base_url}/passwords/groups/?search=测试", headers=headers)
        if search_response.status_code == 200:
            search_results = search_response.json()
            print(f"✅ 密码组搜索功能正常，找到 {search_results.get('count', 0)} 个结果")
        else:
            print(f"❌ 密码组搜索功能异常: {search_response.text}")
        
        # 2. 创建测试组并验证权限
        print("\n2. 创建测试组并验证权限")
        test_group_data = {
            "name": "最终测试组",
            "description": "用于最终测试的组"
        }
        
        create_response = requests.post(f"{base_url}/passwords/groups/", json=test_group_data, headers=headers)
        if create_response.status_code == 201:
            test_group = create_response.json()
            print(f"✅ 测试组创建成功，ID: {test_group['id']}")
            
            # 检查组权限
            permissions_response = requests.get(f"{base_url}/passwords/group-permissions/?search={test_group['name']}", headers=headers)
            if permissions_response.status_code == 200:
                permissions = permissions_response.json()
                if permissions.get('count', 0) > 0:
                    print("✅ 创建者自动获得权限")
                else:
                    print("❌ 创建者没有自动获得权限")
            else:
                print(f"❌ 获取组权限失败: {permissions_response.text}")
        else:
            print(f"❌ 测试组创建失败: {create_response.text}")
        
        # 3. 创建测试密码并验证权限
        print("\n3. 创建测试密码并验证权限")
        test_password_data = {
            "title": "最终测试密码",
            "username": "testuser",
            "password": "testpass123",
            "project_name": "测试项目"
        }
        
        create_password_response = requests.post(f"{base_url}/passwords/passwords/", json=test_password_data, headers=headers)
        if create_password_response.status_code == 201:
            test_password = create_password_response.json()
            print(f"✅ 测试密码创建成功，ID: {test_password['id']}")
            
            # 检查密码权限
            permission_check_response = requests.get(f"{base_url}/passwords/passwords/{test_password['id']}/permissions/check/", headers=headers)
            if permission_check_response.status_code == 200:
                permission_info = permission_check_response.json()
                if permission_info.get('can_write', False):
                    print("✅ 密码创建者自动获得写权限")
                else:
                    print("❌ 密码创建者没有自动获得写权限")
            else:
                print(f"❌ 检查密码权限失败: {permission_check_response.text}")
        else:
            print(f"❌ 测试密码创建失败: {create_password_response.text}")
        
        print("\n=== 最终测试完成 ===")
        print("\n修复总结:")
        print("1. ✅ 密码组搜索功能 - 前端API支持搜索参数")
        print("2. ✅ 密码组创建权限 - 创建者自动获得admin权限")
        print("3. ✅ 密码创建权限 - 创建者自动获得写权限")
        print("4. ⚠️  密码组添加成员功能 - 需要进一步测试")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_final()
