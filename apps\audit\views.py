from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import OrderingFilter, SearchFilter
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema, extend_schema_view
from auditlog.models import LogEntry
from axes.models import AccessAttempt, AccessFailureLog, AccessLog

from .models import BusinessOperationLog, PasswordAccessLog, SecurityEvent
from .serializers import (
    BusinessOperationLogSerializer,
    PasswordAccessLogSerializer,
    SecurityEventSerializer,
    AuditLogEntrySerializer,
    AxesAccessAttemptSerializer,
    AxesAccessFailureLogSerializer,
    AxesAccessLogSerializer,
    UnifiedAuditTrailSerializer,
)
from .utils import get_user_audit_trail, get_model_change_history

User = get_user_model()


@extend_schema_view(
    get=extend_schema(
        summary="获取业务操作日志列表",
        description="获取系统中的业务操作日志，支持分页、过滤和搜索",
        tags=["审计日志"],
    )
)
class BusinessOperationLogListView(generics.ListAPIView):
    """业务操作日志列表视图"""

    queryset = BusinessOperationLog.objects.all()
    serializer_class = BusinessOperationLogSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["action_type", "result", "user", "target_type"]
    search_fields = ["description", "target_name", "user__username"]
    ordering_fields = ["created_at", "action_type", "result"]
    ordering = ["-created_at"]


@extend_schema_view(
    get=extend_schema(
        summary="获取密码访问日志列表",
        description="获取密码访问日志，支持分页、过滤和搜索",
        tags=["审计日志"],
    )
)
class PasswordAccessLogListView(generics.ListAPIView):
    """密码访问日志列表视图"""

    queryset = PasswordAccessLog.objects.all()
    serializer_class = PasswordAccessLogSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["access_type", "user", "password_entry", "access_source"]
    search_fields = ["password_entry__title", "user__username"]
    ordering_fields = ["created_at", "access_type"]
    ordering = ["-created_at"]


@extend_schema_view(
    get=extend_schema(
        summary="获取安全事件列表",
        description="获取系统安全事件，支持分页、过滤和搜索",
        tags=["审计日志"],
    ),
    patch=extend_schema(
        summary="更新安全事件",
        description="更新安全事件的状态、分配人员等信息",
        tags=["审计日志"],
    ),
)
class SecurityEventListView(generics.ListAPIView):
    """安全事件列表视图"""

    queryset = SecurityEvent.objects.all()
    serializer_class = SecurityEventSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["event_type", "severity", "status", "user", "assigned_to"]
    search_fields = ["title", "description", "user__username"]
    ordering_fields = ["created_at", "severity", "status"]
    ordering = ["-created_at"]


@extend_schema_view(
    get=extend_schema(
        summary="获取安全事件详情",
        description="获取特定安全事件的详细信息",
        tags=["审计日志"],
    ),
    patch=extend_schema(
        summary="更新安全事件",
        description="更新安全事件的状态、分配人员等信息",
        tags=["审计日志"],
    ),
)
class SecurityEventDetailView(generics.RetrieveUpdateAPIView):
    """安全事件详情视图"""

    queryset = SecurityEvent.objects.all()
    serializer_class = SecurityEventSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]


@extend_schema_view(
    get=extend_schema(
        summary="获取模型变更日志",
        description="获取auditlog记录的模型变更历史",
        tags=["审计日志"],
    )
)
class ModelChangeLogListView(generics.ListAPIView):
    """模型变更日志列表视图"""

    queryset = LogEntry.objects.all()
    serializer_class = AuditLogEntrySerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["content_type", "action", "actor"]
    search_fields = ["object_repr", "actor__username"]
    ordering_fields = ["timestamp", "action"]
    ordering = ["-timestamp"]


@extend_schema_view(
    get=extend_schema(
        summary="获取登录尝试记录",
        description="获取axes记录的登录尝试历史",
        tags=["审计日志"],
    )
)
class LoginAttemptListView(generics.ListAPIView):
    """登录尝试记录列表视图"""

    queryset = AccessAttempt.objects.all()
    serializer_class = AxesAccessAttemptSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["username", "ip_address"]
    search_fields = ["username", "ip_address", "user_agent"]
    ordering_fields = ["attempt_time", "failures_since_start"]
    ordering = ["-attempt_time"]


@extend_schema_view(
    get=extend_schema(
        summary="获取访问失败日志列表",
        description="获取axes记录的失败登录尝试历史",
        tags=["审计日志"],
    )
)
class AccessFailureLogListView(generics.ListAPIView):
    """访问失败日志列表视图"""

    queryset = AccessFailureLog.objects.all()
    serializer_class = AxesAccessFailureLogSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["username", "ip_address", "locked_out"]
    search_fields = ["username", "ip_address", "user_agent"]
    ordering_fields = ["attempt_time", "locked_out"]
    ordering = ["-attempt_time"]


@extend_schema_view(
    get=extend_schema(
        summary="获取访问日志列表",
        description="获取axes记录的成功登录和登出历史",
        tags=["审计日志"],
    )
)
class AccessLogListView(generics.ListAPIView):
    """访问日志列表视图"""

    queryset = AccessLog.objects.all()
    serializer_class = AxesAccessLogSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ["username", "ip_address"]
    search_fields = ["username", "ip_address", "user_agent"]
    ordering_fields = ["attempt_time", "logout_time"]
    ordering = ["-attempt_time"]


@extend_schema(
    summary="获取用户审计轨迹",
    description="获取指定用户的完整审计轨迹，包括模型变更、业务操作、安全事件和登录记录",
    tags=["审计日志"],
)
@api_view(["GET"])
@permission_classes([IsAuthenticated, IsAdminUser])
def user_audit_trail(request, user_id):
    """获取用户的完整审计轨迹"""
    try:
        audit_data = get_user_audit_trail(user_id)
        if not audit_data:
            return Response(
                {"error": "用户不存在或获取审计数据失败"},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = UnifiedAuditTrailSerializer(audit_data)
        return Response(serializer.data)

    except Exception as e:
        return Response(
            {"error": f"获取审计轨迹失败: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@extend_schema(
    summary="获取模型实例变更历史",
    description="获取特定模型实例的变更历史记录",
    tags=["审计日志"],
)
@api_view(["GET"])
@permission_classes([IsAuthenticated, IsAdminUser])
def model_change_history(request):
    """获取模型实例的变更历史"""
    model_name = request.GET.get("model")
    instance_id = request.GET.get("id")

    if not model_name or not instance_id:
        return Response(
            {"error": "需要提供model和id参数"}, status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # 根据模型名称获取模型类
        from django.apps import apps

        model_class = apps.get_model(model_name)

        changes = get_model_change_history(model_class, instance_id)
        serializer = AuditLogEntrySerializer(changes, many=True)

        return Response(
            {
                "model": model_name,
                "instance_id": instance_id,
                "changes": serializer.data,
            }
        )

    except Exception as e:
        return Response(
            {"error": f"获取变更历史失败: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@extend_schema(
    summary="获取审计统计信息",
    description="获取审计日志的统计信息，包括各类操作的数量统计",
    tags=["审计日志"],
)
@api_view(["GET"])
@permission_classes([IsAuthenticated, IsAdminUser])
def audit_statistics(request):
    """获取审计统计信息"""
    try:
        from django.db.models import Count
        from django.utils import timezone
        from datetime import timedelta

        # 获取时间范围参数
        days = int(request.GET.get("days", 30))
        start_date = timezone.now() - timedelta(days=days)

        # 业务操作统计
        business_ops_stats = (
            BusinessOperationLog.objects.filter(created_at__gte=start_date)
            .values("action_type")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        # 安全事件统计
        security_events_stats = (
            SecurityEvent.objects.filter(created_at__gte=start_date)
            .values("event_type", "severity")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        # 密码访问统计
        password_access_stats = (
            PasswordAccessLog.objects.filter(created_at__gte=start_date)
            .values("access_type")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        # 登录尝试统计
        login_attempts_stats = (
            AccessAttempt.objects.filter(attempt_time__gte=start_date)
            .values("username")
            .annotate(count=Count("id"))
            .order_by("-count")[:10]
        )

        return Response(
            {
                "period_days": days,
                "start_date": start_date,
                "business_operations": list(business_ops_stats),
                "security_events": list(security_events_stats),
                "password_access": list(password_access_stats),
                "top_login_attempts": list(login_attempts_stats),
            }
        )

    except Exception as e:
        return Response(
            {"error": f"获取统计信息失败: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
