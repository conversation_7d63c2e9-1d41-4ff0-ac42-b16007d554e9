# 使用Python 3.11作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_SETTINGS_MODULE=config.settings

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 收集静态文件
RUN python manage.py collectstatic --noinput

# 暴露端口
EXPOSE 8000

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "等待数据库启动..."\n\
sleep 10\n\
echo "运行数据库迁移..."\n\
python manage.py makemigrations\n\
python manage.py migrate\n\
echo "创建超级用户..."\n\
python manage.py shell -c "\n\
from django.contrib.auth import get_user_model\n\
User = get_user_model()\n\
if not User.objects.filter(username=\"admin\").exists():\n\
    User.objects.create_superuser(\"admin\", \"<EMAIL>\", \"admin123\")\n\
    print(\"超级用户创建成功\")\n\
else:\n\
    print(\"超级用户已存在\")\n\
"\n\
echo "初始化系统数据..."\n\
python manage.py shell -c "\n\
from utils.init_data import initialize_system_data\n\
try:\n\
    initialize_system_data()\n\
    print(\"系统数据初始化完成\")\n\
except Exception as e:\n\
    print(f\"初始化数据失败: {e}\")\n\
"\n\
echo "启动Django服务器..."\n\
python manage.py runserver 0.0.0.0:8000' > /app/start.sh

# 设置启动脚本权限
RUN chmod +x /app/start.sh

# 启动命令
CMD ["/app/start.sh"]