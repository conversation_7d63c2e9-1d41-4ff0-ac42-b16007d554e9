import os
from pathlib import Path
import environ
from datetime import timedelta

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Environment variables
env = environ.Env(DEBUG=(bool, False))

# Take environment variables from .env file
environ.Env.read_env(os.path.join(BASE_DIR, ".env"))

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env("SECRET_KEY", default="django-insecure-change-me")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env("DEBUG")

ALLOWED_HOSTS = env.list(
    "ALLOWED_HOSTS", default=["localhost", "127.0.0.1", "testserver"]
)

# Application definition
DJANGO_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
]

THIRD_PARTY_APPS = [
    "rest_framework",
    "rest_framework_simplejwt.token_blacklist",
    "corsheaders",
    "django_filters",
    "drf_spectacular",
    "axes",
    "auditlog",
]

LOCAL_APPS = [
    "apps.users",
    "apps.passwords",
    "apps.categories",
    "apps.sharing",
    "apps.audit",
    "apps.system",
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "axes.middleware.AxesMiddleware",  # 重新启用，配置为仅记录不锁定
    "utils.middleware.RequestLoggingMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "config.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"

# Database
# 支持DATABASE_URL环境变量（用于Docker部署）
if env("DATABASE_URL", default=None):
    import dj_database_url

    DATABASES = {"default": dj_database_url.parse(env("DATABASE_URL"))}
    # 为MySQL添加额外选项
    if "mysql" in DATABASES["default"]["ENGINE"]:
        DATABASES["default"]["OPTIONS"] = {
            "charset": "utf8mb4",
            "init_command": "SET sql_mode='STRICT_TRANS_TABLES'",
        }
else:
    DATABASES = {
        "default": {
            "ENGINE": env("DB_ENGINE", default="django.db.backends.sqlite3"),
            "NAME": env("DB_NAME", default=BASE_DIR / "db.sqlite3"),
            "USER": env("DB_USER", default=""),
            "PASSWORD": env("DB_PASSWORD", default=""),
            "HOST": env("DB_HOST", default=""),
            "PORT": env("DB_PORT", default=""),
        }
    }

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
LANGUAGE_CODE = "zh-hans"
TIME_ZONE = "Asia/Shanghai"
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")

# Media files
MEDIA_URL = "/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")

# Default primary key field type
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Django REST Framework
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "utils.authentication.JWTAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 20,
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
        "rest_framework.filters.SearchFilter",
        "rest_framework.filters.OrderingFilter",
    ],
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
    ],
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
}

# drf-spectacular settings
SPECTACULAR_SETTINGS = {
    "TITLE": "Password Locker API",
    "DESCRIPTION": "密码管理系统API文档",
    "VERSION": "1.0.0",
    "SERVE_INCLUDE_SCHEMA": False,
    "COMPONENT_SPLIT_REQUEST": True,
    "SCHEMA_PATH_PREFIX": "/api/",
    "SWAGGER_UI_SETTINGS": {
        "deepLinking": True,
        "persistAuthorization": True,
        "displayOperationId": True,
    },
    "AUTHENTICATION_WHITELIST": [
        "utils.authentication.JWTAuthentication",
    ],
    # 暂时排除有问题的应用，专注于其他模块
    "SCHEMA_PATH_PREFIX_TRIM": True,
    # 添加错误处理配置
    "DISABLE_ERRORS_AND_WARNINGS": True,
    "ENUM_NAME_OVERRIDES": {},
    "POSTPROCESSING_HOOKS": [],
    # 排除system应用的URL
    "PREPROCESSING_HOOKS": [
        "drf_spectacular.hooks.preprocess_exclude_path_format",
    ],
    "EXCLUDE_PATH_FORMAT": [
        r"/api/system/.*",
    ],
}

# CORS settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://localhost:5666",
    "http://127.0.0.1:5666",
    "http://localhost:5667",
    "http://127.0.0.1:5667",
    "http://localhost:5668",
    "http://127.0.0.1:5668",
    "http://localhost:5669",
    "http://127.0.0.1:5669",
    "http://localhost:5670",
    "http://127.0.0.1:5670",
]

CORS_ALLOW_CREDENTIALS = True

# JWT settings

JWT_SETTINGS = {
    "SECRET_KEY": env("JWT_SECRET_KEY", default=SECRET_KEY),
    "ALGORITHM": env("JWT_ALGORITHM", default="HS256"),
    "ACCESS_TOKEN_LIFETIME": int(env("JWT_ACCESS_TOKEN_LIFETIME", default=3600)),
    "REFRESH_TOKEN_LIFETIME": int(env("JWT_REFRESH_TOKEN_LIFETIME", default=86400)),
}

# Simple JWT settings (for django-rest-framework-simplejwt)
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(
        seconds=int(env("JWT_ACCESS_TOKEN_LIFETIME", default=3600))
    ),
    "REFRESH_TOKEN_LIFETIME": timedelta(
        seconds=int(env("JWT_REFRESH_TOKEN_LIFETIME", default=86400))
    ),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "UPDATE_LAST_LOGIN": True,
    "ALGORITHM": env("JWT_ALGORITHM", default="HS256"),
    "SIGNING_KEY": env("JWT_SECRET_KEY", default=SECRET_KEY),
    "VERIFYING_KEY": None,
    "AUDIENCE": None,
    "ISSUER": None,
    "JWK_URL": None,
    "LEEWAY": 0,
    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "id",
    "USER_ID_CLAIM": "user_id",
    "USER_AUTHENTICATION_RULE": "rest_framework_simplejwt.authentication.default_user_authentication_rule",
    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
    "TOKEN_TYPE_CLAIM": "token_type",
    "TOKEN_USER_CLASS": "rest_framework_simplejwt.models.TokenUser",
    "JTI_CLAIM": "jti",
    "SLIDING_TOKEN_REFRESH_EXP_CLAIM": "refresh_exp",
    "SLIDING_TOKEN_LIFETIME": timedelta(minutes=5),
    "SLIDING_TOKEN_REFRESH_LIFETIME": timedelta(days=1),
}

# SM4 encryption settings
SM4_SETTINGS = {
    "KEY": env("SM4_KEY", default="your-sm4-key-here-32-bytes-long"),
}

# Logging
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "{levelname} {asctime} {module} {process:d} {thread:d} {message}",
            "style": "{",
        },
        "simple": {
            "format": "{levelname} {message}",
            "style": "{",
        },
    },
    "handlers": {
        "file": {
            "level": "INFO",
            "class": "logging.FileHandler",
            "filename": env("LOG_FILE_PATH", default="logs/app.log"),
            "formatter": "verbose",
        },
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "simple",
        },
    },
    "root": {
        "handlers": ["console", "file"],
        "level": env("LOG_LEVEL", default="INFO"),
    },
}

# Security settings
SESSION_COOKIE_SECURE = env("SESSION_COOKIE_SECURE", default=False)
CSRF_COOKIE_SECURE = env("CSRF_COOKIE_SECURE", default=False)
SECURE_SSL_REDIRECT = env("SECURE_SSL_REDIRECT", default=False)

# Custom user model
AUTH_USER_MODEL = "users.User"

# Authentication backends
AUTHENTICATION_BACKENDS = [
    "axes.backends.AxesBackend",
    "django.contrib.auth.backends.ModelBackend",
]

# Frontend configuration
FRONTEND_BASE_URL = env("FRONTEND_BASE_URL", default="http://localhost:5666")

# ============ Django Axes Configuration ============
# 失败登录保护配置 - 仅记录不锁定 (django-axes 8.0+ 配置)
AXES_FAILURE_LIMIT = 999999  # 设置极高的失败次数限制，实际禁用锁定
AXES_COOLOFF_TIME = 0  # 无锁定时间
AXES_RESET_ON_SUCCESS = True  # 成功登录后重置失败次数
AXES_LOCKOUT_TEMPLATE = None  # 使用API响应而不是模板
AXES_LOCKOUT_CALLABLE = None  # 禁用自定义锁定处理器，仅记录不锁定
AXES_ENABLE_ADMIN = False  # 禁用管理界面的锁定功能
AXES_VERBOSE = True  # 详细日志

# 新版本的锁定策略配置 - 仅记录不锁定
# 使用新的 AXES_LOCKOUT_PARAMETERS 替代过时的组合设置
AXES_LOCKOUT_PARAMETERS = ["username"]  # 仅按用户名记录，不锁定
AXES_COOLOFF_TIME_UNIT = "hours"  # 时间单位

# 移除的过时设置 (django-axes 8.0+ 不再支持):
# AXES_LOCK_OUT_BY_COMBINATION_USER_AND_IP = True  # 已过时，使用 AXES_LOCKOUT_PARAMETERS
# AXES_ONLY_USER_FAILURES = False  # 已过时
# AXES_LOCK_OUT_BY_USER_OR_IP = False  # 已过时
# AXES_USE_USER_AGENT = True  # 已过时，通过 AXES_LOCKOUT_PARAMETERS 控制

# ============ Django Auditlog Configuration ============
# 审计日志配置
AUDITLOG_INCLUDE_ALL_MODELS = False  # 手动注册模型
AUDITLOG_DISABLE_REMOTE_ADDR = False  # 记录IP地址
AUDITLOG_USE_TEXT_CHANGES_IF_JSON_IS_NOT_PRESENT = False  # 不使用文本变更记录
