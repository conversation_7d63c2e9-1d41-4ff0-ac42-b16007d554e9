#!/usr/bin/env python
"""
简单的audit API测试
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def test_audit_api_endpoints():
    """测试audit API端点"""
    print("🧪 测试audit API端点...")
    
    try:
        from django.test import Client
        from django.contrib.auth import get_user_model
        from rest_framework_simplejwt.tokens import RefreshToken
        
        User = get_user_model()
        
        # 获取管理员用户
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            print("❌ 没有找到管理员用户")
            return False
        
        # 生成JWT令牌
        refresh = RefreshToken.for_user(admin_user)
        access_token = str(refresh.access_token)
        
        # 创建测试客户端
        client = Client()
        
        # 定义要测试的API端点
        api_endpoints = [
            '/api/audit/business-operations/',
            '/api/audit/password-access/',
            '/api/audit/security-events/',
            '/api/audit/model-changes/',
            '/api/audit/login-attempts/',
            '/api/audit/statistics/',
        ]
        
        successful_endpoints = 0
        total_endpoints = len(api_endpoints)
        
        print(f"\n📊 开始测试 {total_endpoints} 个主要API端点...")
        print("-" * 60)
        
        for endpoint in api_endpoints:
            print(f"\n🔍 测试: {endpoint}")
            
            try:
                # 发送请求
                response = client.get(
                    endpoint,
                    HTTP_AUTHORIZATION=f'Bearer {access_token}'
                )
                
                # 检查响应
                print(f"   📊 状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("   ✅ 请求成功")
                    
                    # 尝试解析JSON响应
                    try:
                        data = response.json()
                        if isinstance(data, dict):
                            if 'results' in data:
                                print(f"   📋 返回 {len(data['results'])} 条记录")
                                if 'count' in data:
                                    print(f"   📊 总计 {data['count']} 条记录")
                            else:
                                print(f"   📋 返回数据: {len(data)} 个字段")
                        elif isinstance(data, list):
                            print(f"   📋 返回 {len(data)} 条记录")
                    except Exception as e:
                        print(f"   ⚠️ JSON解析失败: {e}")
                    
                    successful_endpoints += 1
                    
                elif response.status_code == 401:
                    print("   ❌ 认证失败")
                elif response.status_code == 403:
                    print("   ❌ 权限不足")
                elif response.status_code == 404:
                    print("   ❌ 端点不存在")
                elif response.status_code == 500:
                    print("   ❌ 服务器内部错误")
                else:
                    print(f"   ❌ 未知错误: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ 请求异常: {e}")
        
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {successful_endpoints}/{total_endpoints} 个端点成功")
        
        if successful_endpoints == total_endpoints:
            print("🎉 所有主要audit API端点测试通过！")
        elif successful_endpoints > total_endpoints * 0.8:
            print("✅ 大部分audit API端点正常工作")
        else:
            print("⚠️ 多个audit API端点存在问题")
        
        return successful_endpoints >= total_endpoints * 0.8
        
    except Exception as e:
        print(f"❌ audit API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_counts():
    """测试数据统计"""
    print("\n🧪 测试数据统计...")
    
    try:
        from apps.audit.models import BusinessOperationLog, SecurityEvent
        from auditlog.models import LogEntry
        
        # 检查数据统计
        business_count = BusinessOperationLog.objects.count()
        security_count = SecurityEvent.objects.count()
        auditlog_count = LogEntry.objects.count()
        
        print(f"📊 数据统计:")
        print(f"   - 业务操作日志: {business_count} 条")
        print(f"   - 安全事件: {security_count} 条")
        print(f"   - AuditLog记录: {auditlog_count} 条")
        
        # 检查最新记录
        if business_count > 0:
            latest_business = BusinessOperationLog.objects.order_by('-created_at').first()
            print(f"   - 最新业务操作: {latest_business.action_type} ({latest_business.created_at})")
        
        if security_count > 0:
            latest_security = SecurityEvent.objects.order_by('-created_at').first()
            print(f"   - 最新安全事件: {latest_security.event_type} ({latest_security.created_at})")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据统计测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始简单audit API测试")
    print("=" * 60)
    
    tests = [
        test_data_counts,
        test_audit_api_endpoints,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            print("-" * 50)
    
    print("=" * 60)
    print(f"📊 总体测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 audit API测试完全通过！")
        print("\n💡 测试总结:")
        print("  1. ✅ 数据统计正常")
        print("  2. ✅ 主要API端点可访问")
        print("  3. ✅ 认证和权限控制正常")
        print("  4. ✅ 数据序列化正常")
        print("\n🔧 audit应用状态: 正常工作")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
