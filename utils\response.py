"""
统一API响应格式工具
"""

from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


class StandardResponse:
    """标准API响应格式"""

    @staticmethod
    def success(data=None, message="操作成功", total=None, page=None, page_size=None):
        """
        成功响应

        Args:
            data: 响应数据
            message: 响应消息
            total: 总数（分页时使用）
            page: 当前页码
            page_size: 每页大小
        """
        response = {
            "success": True,
            "message": message,
            "data": data,
            "timestamp": timezone.now().isoformat(),
        }

        # 分页信息
        if total is not None:
            response.update({"total": total, "page": page, "page_size": page_size})

        return Response(response, status=status.HTTP_200_OK)

    @staticmethod
    def created(data=None, message="创建成功"):
        """创建成功响应"""
        response = {
            "success": True,
            "message": message,
            "data": data,
            "timestamp": timezone.now().isoformat(),
        }
        return Response(response, status=status.HTTP_201_CREATED)

    @staticmethod
    def error(
        message="操作失败",
        code=None,
        errors=None,
        status_code=status.HTTP_400_BAD_REQUEST,
    ):
        """
        错误响应

        Args:
            message: 错误消息
            code: 错误码
            errors: 详细错误信息
            status_code: HTTP状态码
        """
        response = {
            "success": False,
            "message": message,
            "timestamp": timezone.now().isoformat(),
        }

        if code:
            response["code"] = code
        if errors:
            response["errors"] = errors

        return Response(response, status=status_code)

    @staticmethod
    def not_found(message="资源不存在", code="RESOURCE_NOT_FOUND"):
        """资源不存在响应"""
        return StandardResponse.error(
            message=message, code=code, status_code=status.HTTP_404_NOT_FOUND
        )

    @staticmethod
    def forbidden(message="权限不足", code="INSUFFICIENT_PERMISSION"):
        """权限不足响应"""
        return StandardResponse.error(
            message=message, code=code, status_code=status.HTTP_403_FORBIDDEN
        )

    @staticmethod
    def validation_error(message="数据验证失败", errors=None, code="VALIDATION_ERROR"):
        """数据验证错误响应"""
        return StandardResponse.error(
            message=message,
            code=code,
            errors=errors,
            status_code=status.HTTP_400_BAD_REQUEST,
        )


def log_operation(
    user, action, resource_type, resource_id=None, details=None, request=None
):
    """
    记录操作日志 - 兼容原有系统格式

    Args:
        user: 操作用户
        action: 操作类型
        resource_type: 资源类型
        resource_id: 资源ID
        details: 详细信息
        request: 请求对象
    """
    try:
        from apps.audit.models import BusinessOperationLog

        ip_address = None
        user_agent = None

        if request:
            x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(",")[0]
            else:
                ip_address = request.META.get("REMOTE_ADDR")
            user_agent = request.META.get("HTTP_USER_AGENT", "")

        # 使用BusinessOperationLog字段名
        BusinessOperationLog.objects.create(
            user=user,
            action_type=action,  # 使用action_type而不是action
            target_type=resource_type,  # 使用target_type而不是resource_type
            target_id=str(resource_id) if resource_id else None,
            ip_address=ip_address,
            user_agent=user_agent,
            extra_data=details or {},
        )
    except Exception as e:
        logger.error(f"记录操作日志失败: {e}")
