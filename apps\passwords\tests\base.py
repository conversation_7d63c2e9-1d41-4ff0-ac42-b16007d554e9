"""
测试基础类
"""
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from django.urls import reverse
from apps.audit.models import BusinessOperationLog, PasswordAccessLog
from .factories import (
    UserFactory,
    AdminUserFactory,
    PasswordEntryFactory,
    CategoryFactory,
    PasswordEntryGroupFactory,
    PasswordPolicyFactory,
)

User = get_user_model()


class BasePasswordAPITestCase(APITestCase):
    """密码API测试基础类"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建测试用户
        self.user = UserFactory()
        self.admin_user = AdminUserFactory()
        self.other_user = UserFactory()
        
        # 创建测试分类
        self.category = CategoryFactory()
        
        # 创建测试密码条目
        self.password_entry = PasswordEntryFactory(
            owner=self.user,
            category=self.category
        )
        
        # 创建其他用户的密码条目
        self.other_password_entry = PasswordEntryFactory(
            owner=self.other_user,
            category=self.category
        )
        
        # 创建测试密码组
        self.password_group = PasswordEntryGroupFactory(created_by=self.user)
        
        # 创建测试密码策略
        self.password_policy = PasswordPolicyFactory(created_by=self.admin_user)
    
    def authenticate_user(self, user=None):
        """认证用户"""
        if user is None:
            user = self.user
        self.client.force_authenticate(user=user)
    
    def authenticate_admin(self):
        """认证管理员用户"""
        self.client.force_authenticate(user=self.admin_user)
    
    def get_url(self, name, **kwargs):
        """获取URL"""
        return reverse(name, kwargs=kwargs)
    
    def assert_response_success(self, response, expected_status=status.HTTP_200_OK):
        """断言响应成功"""
        self.assertEqual(response.status_code, expected_status)
        return response.data
    
    def assert_response_error(self, response, expected_status=status.HTTP_400_BAD_REQUEST):
        """断言响应错误"""
        self.assertEqual(response.status_code, expected_status)
        return response.data
    
    def assert_operation_logged(self, action_type, target_type=None, user=None):
        """断言操作日志已记录"""
        if user is None:
            user = self.user
        
        log_exists = BusinessOperationLog.objects.filter(
            user=user,
            action_type=action_type
        )
        
        if target_type:
            log_exists = log_exists.filter(target_type=target_type)
        
        self.assertTrue(log_exists.exists(), f"操作日志未记录: {action_type}")
    
    def assert_access_logged(self, password_entry, access_type="view", user=None):
        """断言访问日志已记录"""
        if user is None:
            user = self.user
        
        log_exists = PasswordAccessLog.objects.filter(
            user=user,
            password_entry=password_entry,
            access_type=access_type
        ).exists()
        
        self.assertTrue(log_exists, f"访问日志未记录: {access_type}")
    
    def clear_logs(self):
        """清理日志"""
        BusinessOperationLog.objects.all().delete()
        PasswordAccessLog.objects.all().delete()


class BasePasswordPermissionTestCase(BasePasswordAPITestCase):
    """密码权限测试基础类"""
    
    def setUp(self):
        super().setUp()
        
        # 创建有权限的用户
        self.permitted_user = UserFactory()
        
        # 创建无权限的用户
        self.unpermitted_user = UserFactory()
    
    def grant_password_permission(self, user, password_entry, permission_level="read"):
        """授予密码权限"""
        from apps.passwords.models import PasswordPermission
        
        PasswordPermission.objects.create(
            password=password_entry,
            permission_type="user",
            target_id=user.id,
            permission_level=permission_level,
            granted_by=password_entry.owner,
            is_active=True
        )
    
    def grant_group_permission(self, user, group, permission_level="read"):
        """授予组权限"""
        from apps.passwords.models import GroupPermission
        
        GroupPermission.objects.create(
            group=group,
            user=user,
            permission_level=permission_level
        )
    
    def add_password_to_group(self, password_entry, group, user=None):
        """添加密码到组"""
        from apps.passwords.models import PasswordEntryGroupMembership
        
        if user is None:
            user = group.created_by
        
        PasswordEntryGroupMembership.objects.create(
            group=group,
            password_entry=password_entry,
            added_by=user
        )


class MockRequest:
    """模拟请求对象"""
    
    def __init__(self, user=None, data=None, method="GET"):
        self.user = user
        self.data = data or {}
        self.method = method
        self.META = {
            "HTTP_USER_AGENT": "Test Agent",
            "REMOTE_ADDR": "127.0.0.1"
        }
        self.query_params = {}
    
    def get_client_ip(self):
        return "127.0.0.1"
