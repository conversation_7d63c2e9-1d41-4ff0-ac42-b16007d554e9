由于我们要开发新特性，因此请使用git创建一个新的分支，然后再开始接下来的开发
我计划采用方案二 完全替换的方式，由于我们现在是在开发阶段，因此审计数据无法迁移。自定义的审计功能在后续在进行拓展。
由于这是一个backend项目，因此你除了完成项目集成外，还需要完成api的开发。
这是两个库的文档，希望对你有帮助 https://django-axes.readthedocs.io/en/latest/2_installation.html https://django-auditlog.readthedocs.io/en/latest/

请按照以下步骤实施django-axes和django-auditlog的完全替换集成方案：

**第一步：Git分支管理**
- 创建一个新的功能分支用于此次开发（建议命名为 `feature/audit-enhancement` 或类似）
- 确保从当前的develop分支创建新分支

**第二步：实施方案二（完全替换）**
- 采用完全替换现有audit模块的方式集成django-axes和django-auditlog
- 由于项目处于开发阶段，现有的审计数据不需要迁移，可以直接替换
- 移除或重构现有的自定义审计功能，后续根据需要再进行扩展

**第三步：完整的后端开发**
作为一个backend项目，除了基础的库集成外，还需要完成：
1. 安装和配置django-axes和django-auditlog
2. 重构现有的audit模块以使用新的库
3. 开发相应的REST API接口
4. 更新相关的序列化器和视图
5. 确保与现有的用户认证系统兼容
6. 编写或更新相关测试

**参考文档：**
- django-axes文档：https://django-axes.readthedocs.io/en/latest/2_installation.html
- django-auditlog文档：https://django-auditlog.readthedocs.io/en/latest/

**预期交付物：**
- 完整的库集成配置
- 重构后的audit模块
- 新的API端点和文档
- 更新的测试套件
- 确保所有现有功能正常工作


我在django admin后台看到 Access attempts	
Access failures	
Access logs这三个模型，我们是否都为其制定了api端点

BusinessOperationLog模型和PasswordAccessLog模型是否有功能重合的部分，OperationLog的一些业务行为如查看密码，复制密码等是是否可以集成到django auditlog模型中

1. 请分析 新增首次登录后需重置密码功能如何实现，在创建用户时如何设置该功能
2. 审计管理中冗余或临时的模型什么时候可以删掉


1. 无需数据迁移，请清理冗余模型
待清理的冗余模型：LoginLog - 完全被django-axes替代，功能重叠100%

2. Role模型目前的name字段是一个choice，在代码中固定了，我希望采用更灵活的方式去定义它。请先分析可行性，在得到我同意后在进行编码

3. System下的参数设置 设置项在代码中固定了，我希望采用更灵活的方式去定义。请先分析可行性，在得到我同意后在进行编码



