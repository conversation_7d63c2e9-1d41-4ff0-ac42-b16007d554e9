[tool:pytest]
DJANGO_SETTINGS_MODULE = config.settings
python_files = test_*.py *_test.py *_tests.py
python_classes = Test* *Test *TestCase
python_functions = test_*
testpaths = tests
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --reuse-db
    --nomigrations
markers =
    unit: 单元测试
    integration: 集成测试
    api: API测试
    slow: 慢速测试
    security: 安全测试
    performance: 性能测试
