#!/usr/bin/env python
"""
用户登录问题全面诊断
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def check_user_accounts():
    """检查用户账户状态"""
    print("🔍 检查用户账户状态")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 检查目标用户
        target_users = ['root', 'admin']
        
        for username in target_users:
            print(f"\n👤 用户: {username}")
            try:
                user = User.objects.get(username=username)
                print(f"   ✅ 用户存在")
                print(f"   📊 用户状态:")
                print(f"      - ID: {user.id}")
                print(f"      - 激活: {user.is_active}")
                print(f"      - 员工: {user.is_staff}")
                print(f"      - 超级用户: {user.is_superuser}")
                print(f"      - 最后登录: {user.last_login}")
                print(f"      - 创建时间: {user.date_joined}")
                
                # 测试密码验证
                test_passwords = ['root123!', 'admin123!', 'root', 'admin']
                print(f"   🔐 密码验证测试:")
                for pwd in test_passwords:
                    if user.check_password(pwd):
                        print(f"      ✅ 密码 '{pwd}' 正确")
                        break
                else:
                    print(f"      ❌ 测试的密码都不正确")
                    print(f"      💡 可能需要重置密码")
                
            except User.DoesNotExist:
                print(f"   ❌ 用户不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查用户账户失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_axes_lockout_status():
    """检查django-axes锁定状态"""
    print("\n🔍 检查django-axes锁定状态")
    print("=" * 60)
    
    try:
        # 检查是否安装了axes
        try:
            import axes
            from axes.models import AccessAttempt, AccessFailureLog
            print(f"   📦 django-axes版本: {axes.__version__}")
        except ImportError:
            print("   ⚠️ django-axes未安装")
            return True
        
        # 检查访问尝试记录
        attempts = AccessAttempt.objects.all()
        print(f"   📊 访问尝试记录数: {attempts.count()}")
        
        if attempts.exists():
            print("   📋 访问尝试详情:")
            for attempt in attempts:
                print(f"      - 用户名: {attempt.username}")
                print(f"        IP: {attempt.ip_address}")
                print(f"        失败次数: {attempt.failures_since_start}")
                print(f"        时间: {attempt.attempt_time}")
                print(f"        锁定: {'是' if attempt.failures_since_start >= 3 else '否'}")
                print()
        
        # 检查失败日志
        failures = AccessFailureLog.objects.all().order_by('-attempt_time')[:10]
        print(f"   📊 失败日志记录数: {AccessFailureLog.objects.count()}")
        
        if failures.exists():
            print("   📋 最近10次失败记录:")
            for failure in failures:
                print(f"      - 用户名: {failure.username}")
                print(f"        IP: {failure.ip_address}")
                print(f"        时间: {failure.attempt_time}")
                print(f"        用户代理: {failure.user_agent[:50]}...")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ 检查axes状态失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_axes_configuration():
    """检查django-axes配置"""
    print("\n🔍 检查django-axes配置")
    print("=" * 60)
    
    try:
        from django.conf import settings
        
        # 检查axes相关设置
        axes_settings = [
            'AXES_ENABLED',
            'AXES_FAILURE_LIMIT',
            'AXES_COOLOFF_TIME',
            'AXES_LOCKOUT_CALLABLE',
            'AXES_LOCK_OUT_BY_COMBINATION_USER_AND_IP',
            'AXES_LOCK_OUT_BY_USER_OR_IP',
            'AXES_ONLY_USER_FAILURES',
            'AXES_RESET_ON_SUCCESS',
        ]
        
        print("   📋 当前axes配置:")
        for setting in axes_settings:
            if hasattr(settings, setting):
                value = getattr(settings, setting)
                print(f"      {setting}: {value}")
            else:
                print(f"      {setting}: 未设置")
        
        # 检查中间件配置
        middleware = getattr(settings, 'MIDDLEWARE', [])
        axes_middleware = 'axes.middleware.AxesMiddleware'
        
        if axes_middleware in middleware:
            print(f"\n   ✅ axes中间件已配置")
        else:
            print(f"\n   ❌ axes中间件未配置或已禁用")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查axes配置失败: {e}")
        return False

def test_login_api():
    """测试登录API"""
    print("\n🔍 测试登录API")
    print("=" * 60)
    
    try:
        from django.test import Client
        import json
        
        client = Client()
        
        # 测试用户凭据
        test_credentials = [
            {'username': 'root', 'password': 'root123!'},
            {'username': 'admin', 'password': 'admin123!'},
            {'username': 'root', 'password': 'root'},
            {'username': 'admin', 'password': 'admin'},
        ]
        
        for creds in test_credentials:
            print(f"\n   🧪 测试登录: {creds['username']} / {creds['password']}")
            
            response = client.post(
                '/api/auth/login/',
                data=json.dumps(creds),
                content_type='application/json'
            )
            
            print(f"      状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"      ✅ 登录成功")
                try:
                    data = response.json()
                    if 'access_token' in data:
                        print(f"      🎫 获得访问令牌")
                    else:
                        print(f"      ⚠️ 响应格式异常")
                except:
                    print(f"      ⚠️ 响应解析失败")
            else:
                print(f"      ❌ 登录失败")
                try:
                    data = response.json()
                    if 'detail' in data:
                        print(f"      📝 错误信息: {data['detail']}")
                    elif 'error' in data:
                        print(f"      📝 错误信息: {data['error']}")
                    else:
                        print(f"      📝 响应内容: {data}")
                except:
                    print(f"      📝 响应内容: {response.content[:100]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试登录API失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def reset_user_passwords():
    """重置用户密码"""
    print("\n🔧 重置用户密码")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 重置目标用户密码
        password_resets = [
            {'username': 'root', 'password': 'root123!'},
            {'username': 'admin', 'password': 'admin123!'},
        ]
        
        for reset_info in password_resets:
            username = reset_info['username']
            password = reset_info['password']
            
            try:
                user = User.objects.get(username=username)
                user.set_password(password)
                user.is_active = True
                user.save()
                
                print(f"   ✅ 重置用户 {username} 密码成功")
                print(f"      新密码: {password}")
                
                # 验证密码设置
                if user.check_password(password):
                    print(f"      ✅ 密码验证成功")
                else:
                    print(f"      ❌ 密码验证失败")
                
            except User.DoesNotExist:
                print(f"   ❌ 用户 {username} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 重置用户密码失败: {e}")
        return False

def clear_axes_lockouts():
    """清除axes锁定"""
    print("\n🔧 清除axes锁定")
    print("=" * 60)
    
    try:
        try:
            from axes.models import AccessAttempt, AccessFailureLog
        except ImportError:
            print("   ⚠️ django-axes未安装，跳过清除")
            return True
        
        # 清除访问尝试记录
        attempt_count = AccessAttempt.objects.count()
        AccessAttempt.objects.all().delete()
        print(f"   ✅ 清除了 {attempt_count} 条访问尝试记录")
        
        # 清除失败日志（可选，保留用于审计）
        failure_count = AccessFailureLog.objects.count()
        print(f"   📊 保留了 {failure_count} 条失败日志记录（用于审计）")
        
        return True
        
    except Exception as e:
        print(f"❌ 清除axes锁定失败: {e}")
        return False

def main():
    """主诊断函数"""
    print("🚀 开始用户登录问题全面诊断")
    print("=" * 80)
    
    # 执行诊断步骤
    steps = [
        ("用户账户状态", check_user_accounts),
        ("axes锁定状态", check_axes_lockout_status),
        ("axes配置", check_axes_configuration),
        ("登录API测试", test_login_api),
    ]
    
    passed = 0
    total = len(steps)
    
    for step_name, step_func in steps:
        try:
            if step_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 步骤 {step_name} 出现异常: {e}")
            print("-" * 50)
    
    # 如果登录测试失败，尝试修复
    if passed < total:
        print("\n🔧 尝试修复问题")
        print("=" * 60)
        
        # 重置密码
        reset_user_passwords()
        print("-" * 30)
        
        # 清除锁定
        clear_axes_lockouts()
        print("-" * 30)
        
        # 重新测试
        print("\n🧪 重新测试登录")
        test_login_api()
    
    print("\n" + "=" * 80)
    print("📋 诊断总结")
    print("=" * 80)
    
    print(f"📊 诊断结果: {passed}/{total} 个检查通过")
    
    if passed == total:
        print("✅ 系统状态正常")
    else:
        print("⚠️ 发现问题，已尝试修复")
    
    print("\n💡 建议:")
    print("   1. 使用 root / root123! 登录")
    print("   2. 使用 admin / admin123! 登录")
    print("   3. 如仍有问题，运行解锁管理命令")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
