from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (
    PasswordEntry, PasswordEntryGroup, GroupPermission, Category,
    CustomField, Attachment, PasswordHistory, PasswordEntryGroupMembership,
    PasswordPolicy, PasswordPermission, UserGroupMembership
)


@admin.register(PasswordEntry)
class PasswordEntryAdmin(admin.ModelAdmin):
    list_display = ['title', 'username', 'system_type', 'environment', 'owner', 'created_at']
    list_filter = ['system_type', 'environment', 'strength', 'is_favorite', 'is_deleted']
    search_fields = ['title', 'username', 'ip_address', 'project_name']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        (_('基本信息'), {
            'fields': ('title', 'username', 'password', 'owner')
        }),
        (_('系统信息'), {
            'fields': ('system_type', 'ip_address', 'port', 'url', 'os_type', 'mdw_type', 'database_type', 'database_name')
        }),
        (_('环境信息'), {
            'fields': ('environment', 'project_name', 'category')
        }),
        (_('其他'), {
            'fields': ('notes', 'strength', 'last_used', 'expires_at', 'is_favorite', 'is_pinned')
        }),
        (_('软删除'), {
            'fields': ('is_deleted', 'deleted_at', 'deleted_by'),
            'classes': ('collapse',)
        }),
        (_('时间戳'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(PasswordEntryGroup)
class PasswordEntryGroupAdmin(admin.ModelAdmin):
    list_display = ['name', 'created_by', 'created_at', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(GroupPermission)
class GroupPermissionAdmin(admin.ModelAdmin):
    list_display = ['user', 'group', 'permission', 'granted_by', 'granted_at']
    list_filter = ['permission', 'granted_at']
    search_fields = ['user__username', 'group__name']
    readonly_fields = ['granted_at']


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'user', 'parent', 'is_system', 'created_at']
    list_filter = ['is_system', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(PasswordPolicy)
class PasswordPolicyAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_active', 'is_default', 'created_by', 'created_at']
    list_filter = ['is_active', 'is_default', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(PasswordPermission)
class PasswordPermissionAdmin(admin.ModelAdmin):
    list_display = ['password', 'permission_type', 'target_id', 'permission_level', 'granted_by', 'granted_at', 'is_active']
    list_filter = ['permission_type', 'permission_level', 'is_active', 'granted_at']
    search_fields = ['password__title']
    readonly_fields = ['granted_at']
    
    fieldsets = (
        (_('权限信息'), {
            'fields': ('password', 'permission_type', 'target_id', 'permission_level')
        }),
        (_('授权信息'), {
            'fields': ('granted_by', 'granted_at', 'expires_at', 'is_active')
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('password', 'granted_by')


@admin.register(UserGroupMembership)
class UserGroupMembershipAdmin(admin.ModelAdmin):
    list_display = ['user', 'group', 'joined_at', 'added_by', 'is_active']
    list_filter = ['is_active', 'joined_at']
    search_fields = ['user__username', 'group__name']
    readonly_fields = ['joined_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'group', 'added_by')


@admin.register(CustomField)
class CustomFieldAdmin(admin.ModelAdmin):
    list_display = ['password_entry', 'field_name', 'field_type', 'is_sensitive', 'order']
    list_filter = ['field_type', 'is_sensitive']
    search_fields = ['password_entry__title', 'field_name']


@admin.register(Attachment)
class AttachmentAdmin(admin.ModelAdmin):
    list_display = ['password_entry', 'file_name', 'file_size', 'uploaded_by', 'uploaded_at']
    list_filter = ['content_type', 'uploaded_at']
    search_fields = ['password_entry__title', 'file_name']
    readonly_fields = ['uploaded_at']


@admin.register(PasswordHistory)
class PasswordHistoryAdmin(admin.ModelAdmin):
    list_display = ['password_entry', 'changed_by', 'changed_at']
    list_filter = ['changed_at']
    search_fields = ['password_entry__title', 'changed_by__username']
    readonly_fields = ['changed_at']


@admin.register(PasswordEntryGroupMembership)
class PasswordEntryGroupMembershipAdmin(admin.ModelAdmin):
    list_display = ['password_entry', 'group', 'added_by', 'added_at']
    list_filter = ['added_at']
    search_fields = ['password_entry__title', 'group__name']
    readonly_fields = ['added_at']
