from rest_framework import permissions
from django.contrib.auth import get_user_model

User = get_user_model()


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    自定义权限：只有对象的所有者才能编辑它
    """
    
    def has_object_permission(self, request, view, obj):
        # 读取权限允许任何请求
        # 所以我们总是允许GET、HEAD或OPTIONS请求
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 写入权限只给对象的所有者
        return obj.owner == request.user


class IsAdminOrReadOnly(permissions.BasePermission):
    """
    自定义权限：只有管理员才能进行写操作
    """
    
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user and request.user.is_staff


class IsSuperUserOrReadOnly(permissions.BasePermission):
    """
    自定义权限：只有超级用户才能进行写操作
    """
    
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user and request.user.is_superuser


class CanManagePasswords(permissions.BasePermission):
    """
    密码管理权限
    """
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # 用户只能管理自己的密码
        return obj.owner == request.user


class CanSharePasswords(permissions.BasePermission):
    """
    密码共享权限
    """
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # 用户可以查看分享给自己的密码，或者自己分享的密码
        if hasattr(obj, 'owner'):
            return obj.owner == request.user
        if hasattr(obj, 'shared_by'):
            return obj.shared_by == request.user
        if hasattr(obj, 'shared_to'):
            return obj.shared_to == request.user
        return False


class IsOwnerOrSharedWith(permissions.BasePermission):
    """
    权限：用户是所有者或者密码已分享给该用户
    """
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # 检查用户是否是所有者
        if hasattr(obj, 'owner') and obj.owner == request.user:
            return True
        
        # 检查密码是否分享给了该用户
        if hasattr(obj, 'shared_users'):
            return request.user in obj.shared_users.all()
        
        # 检查是否通过分享记录访问
        if hasattr(obj, 'share_records'):
            return obj.share_records.filter(shared_to=request.user, is_active=True).exists()
        
        return False


class IsAdminOrSuperUser(permissions.BasePermission):
    """
    权限：只有管理员或超级用户才能访问
    """
    
    def has_permission(self, request, view):
        return request.user and (request.user.is_staff or request.user.is_superuser)