#!/usr/bin/env python
"""
测试临时令牌API
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def find_user_with_temp_token():
    """查找具有临时令牌的用户"""
    print("🔍 查找具有临时令牌的用户")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 查找有临时令牌的用户
        users_with_tokens = User.objects.exclude(temp_password_token='')
        
        print(f"📊 有临时令牌的用户数: {users_with_tokens.count()}")
        
        for user in users_with_tokens:
            print(f"\n👤 用户: {user.username} (ID: {user.id})")
            print(f"   📧 邮箱: {user.email}")
            print(f"   🔑 临时令牌: {user.temp_password_token}")
            print(f"   🔄 需要修改密码: {user.requires_password_change}")
            print(f"   🆕 首次登录: {user.is_first_login}")
            print(f"   🔒 必须修改密码: {user.password_must_change}")
        
        return users_with_tokens.first() if users_with_tokens.exists() else None
        
    except Exception as e:
        print(f"❌ 查找用户失败: {e}")
        return None

def test_specific_temp_token():
    """测试特定的临时令牌"""
    print("\n🧪 测试特定的临时令牌")
    print("=" * 60)
    
    # 前端提供的令牌
    provided_token = "mwiovVOLyoytmzbSKGBeHNjuDOgej6dhR49qSaZUi9c"
    
    try:
        from django.contrib.auth import get_user_model
        from django.test import Client
        import json
        
        User = get_user_model()
        client = Client()
        
        print(f"🔑 测试令牌: {provided_token}")
        
        # 查找具有此令牌的用户
        try:
            user = User.objects.get(temp_password_token=provided_token)
            print(f"✅ 找到用户: {user.username} (ID: {user.id})")
            print(f"   📧 邮箱: {user.email}")
            print(f"   🔄 需要修改密码: {user.requires_password_change}")
            print(f"   🆕 首次登录: {user.is_first_login}")
            
            # 测试API调用
            print(f"\n📡 测试API调用...")
            
            change_data = {
                "temp_token": provided_token,
                "user_id": user.id,
                "new_password": "NewPassword123!",
                "new_password_confirm": "NewPassword123!"
            }
            
            response = client.post(
                "/api/auth/password/first-login-change/",
                data=json.dumps(change_data),
                content_type="application/json"
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 密码修改成功")
                result = response.json()
                print(f"响应内容: {result}")
                
                # 检查返回的JWT令牌
                if 'access_token' in result:
                    access_token = result['access_token']
                    print(f"🎫 获得JWT令牌: {access_token[:30]}...")
                    
                    # 验证JWT令牌格式
                    parts = access_token.split('.')
                    if len(parts) == 3:
                        print("✅ JWT令牌格式正确")
                    else:
                        print("❌ JWT令牌格式错误")
                
                return True
            else:
                print(f"❌ 密码修改失败")
                try:
                    error = response.json()
                    print(f"错误信息: {error}")
                except:
                    print(f"响应内容: {response.content}")
                
                return False
                
        except User.DoesNotExist:
            print(f"❌ 没有找到具有此令牌的用户")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_user_with_temp_token():
    """创建测试用户并生成临时令牌"""
    print("\n🔧 创建测试用户并生成临时令牌")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 创建测试用户
        test_username = "test_temp_user"
        test_email = "<EMAIL>"
        
        # 删除已存在的测试用户
        User.objects.filter(username=test_username).delete()
        
        test_user = User.objects.create_user(
            username=test_username,
            email=test_email,
            password="TempPassword123!",
            is_first_login=True,
            password_must_change=True
        )
        
        # 生成临时令牌
        temp_token = test_user.generate_temp_password_token()
        
        print(f"✅ 创建测试用户: {test_username}")
        print(f"   📧 邮箱: {test_email}")
        print(f"   🆔 用户ID: {test_user.id}")
        print(f"   🔑 临时令牌: {temp_token}")
        print(f"   🔄 需要修改密码: {test_user.requires_password_change}")
        
        return test_user, temp_token
        
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        return None, None

def test_temp_token_api(user, temp_token):
    """测试临时令牌API"""
    print(f"\n🧪 测试临时令牌API")
    print("=" * 60)
    
    try:
        from django.test import Client
        import json
        
        client = Client()
        
        print(f"🔑 使用令牌: {temp_token}")
        print(f"👤 用户: {user.username} (ID: {user.id})")
        
        # 测试密码修改
        change_data = {
            "temp_token": temp_token,
            "user_id": user.id,
            "new_password": "NewPassword123!",
            "new_password_confirm": "NewPassword123!"
        }
        
        response = client.post(
            "/api/auth/password/first-login-change/",
            data=json.dumps(change_data),
            content_type="application/json"
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 密码修改成功")
            result = response.json()
            
            print(f"📝 响应内容:")
            for key, value in result.items():
                if key in ['access_token', 'refresh_token']:
                    print(f"   {key}: {str(value)[:30]}...")
                else:
                    print(f"   {key}: {value}")
            
            # 验证用户状态变更
            user.refresh_from_db()
            print(f"\n📊 用户状态变更:")
            print(f"   🆕 首次登录: {user.is_first_login}")
            print(f"   🔒 必须修改密码: {user.password_must_change}")
            print(f"   🔑 临时令牌: {user.temp_password_token}")
            
            return True
        else:
            print(f"❌ 密码修改失败")
            try:
                error = response.json()
                print(f"错误信息: {error}")
            except:
                print(f"响应内容: {response.content}")
            
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def generate_solution():
    """生成解决方案"""
    print(f"\n💡 问题解决方案")
    print("=" * 60)
    
    print("🎯 问题分析:")
    print("   1. 前端使用的不是JWT令牌，而是临时密码令牌(temp_token)")
    print("   2. 首次登录密码修改接口不需要JWT认证")
    print("   3. 该接口使用临时令牌进行身份验证")
    print()
    
    print("✅ 正确的API调用方式:")
    print("   POST /api/auth/password/first-login-change/")
    print("   Content-Type: application/json")
    print("   (不需要Authorization头)")
    print()
    print("   请求体:")
    print("   {")
    print('     "temp_token": "mwiovVOLyoytmzbSKGBeHNjuDOgej6dhR49qSaZUi9c",')
    print('     "user_id": 用户ID,')
    print('     "new_password": "新密码",')
    print('     "new_password_confirm": "确认新密码"')
    print("   }")
    print()
    
    print("🔧 前端修复建议:")
    print("   1. 移除Authorization头")
    print("   2. 确保请求体包含所有必需字段")
    print("   3. 使用正确的用户ID")
    print("   4. 确保两次密码输入一致")
    print()
    
    print("⚠️ 常见错误:")
    print("   1. 错误地添加了Authorization头")
    print("   2. 缺少必需的字段(temp_token, user_id, new_password_confirm)")
    print("   3. 用户ID不正确")
    print("   4. 临时令牌已过期或无效")

def main():
    """主测试函数"""
    print("🚀 开始测试临时令牌API")
    print("=" * 80)
    
    # 1. 查找现有的临时令牌用户
    existing_user = find_user_with_temp_token()
    
    # 2. 测试前端提供的特定令牌
    specific_token_works = test_specific_temp_token()
    
    # 3. 如果没有现有用户或测试失败，创建新的测试用户
    if not existing_user or not specific_token_works:
        print("\n" + "=" * 50)
        test_user, temp_token = create_test_user_with_temp_token()
        
        if test_user and temp_token:
            test_temp_token_api(test_user, temp_token)
    
    # 4. 生成解决方案
    generate_solution()
    
    print("\n" + "=" * 80)
    print("📋 测试总结")
    print("=" * 80)
    
    if specific_token_works:
        print("🎉 前端提供的令牌测试成功！")
        print("\n✅ 解决方案:")
        print("   1. 前端不需要添加Authorization头")
        print("   2. 直接使用temp_token进行API调用")
        print("   3. 确保包含所有必需字段")
    else:
        print("⚠️ 前端提供的令牌可能有问题")
        print("\n💡 可能的原因:")
        print("   1. 令牌已过期或无效")
        print("   2. 用户ID不正确")
        print("   3. 用户状态不符合要求")
        print("\n🔧 建议:")
        print("   1. 检查用户是否需要修改密码")
        print("   2. 重新生成临时令牌")
        print("   3. 确认用户ID正确")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
