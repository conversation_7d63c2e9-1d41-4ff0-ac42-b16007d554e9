#!/usr/bin/env python
"""
Admin用户权限问题诊断
处理前端提交的紧急issue: admin用户权限数组为空
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def check_admin_user():
    """检查admin用户基本信息"""
    print("🔍 检查admin用户基本信息")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 查找admin用户
        try:
            admin_user = User.objects.get(username='admin')
            print(f"✅ 找到admin用户")
            print(f"   ID: {admin_user.id}")
            print(f"   用户名: {admin_user.username}")
            print(f"   邮箱: {admin_user.email}")
            print(f"   姓名: {admin_user.name}")
            print(f"   是否激活: {admin_user.is_active}")
            print(f"   是否员工: {admin_user.is_staff}")
            print(f"   是否超级用户: {admin_user.is_superuser}")
            print(f"   角色: {admin_user.role}")
            print(f"   部门: {admin_user.department}")
            
            return admin_user
            
        except User.DoesNotExist:
            print("❌ 未找到admin用户")
            return None
        
    except Exception as e:
        print(f"❌ 检查admin用户失败: {e}")
        return None

def check_user_info_api():
    """测试用户信息API"""
    print("\n🧪 测试用户信息API")
    print("=" * 60)
    
    try:
        from django.test import Client
        import json
        
        client = Client()
        
        # 1. 先登录获取JWT令牌
        print("1. 登录获取JWT令牌...")
        login_data = {
            'username': 'admin',
            'password': 'admin123!'  # 使用之前重置的密码
        }
        
        response = client.post(
            '/api/auth/login/',
            data=json.dumps(login_data),
            content_type='application/json'
        )
        
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            try:
                error = response.json()
                print(f"错误信息: {error}")
            except:
                print(f"响应内容: {response.content}")
            return False
        
        data = response.json()
        access_token = data.get('access_token')
        
        if not access_token:
            print("❌ 未获得访问令牌")
            return False
        
        print(f"✅ 登录成功，获得令牌: {access_token[:30]}...")
        
        # 2. 测试用户信息API
        print("\n2. 测试用户信息API...")
        
        response = client.get(
            '/api/auth/user-info',
            HTTP_AUTHORIZATION=f'Bearer {access_token}'
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功")
            print(f"响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 检查权限数组
            permissions = result.get('permissions', [])
            print(f"\n📊 权限分析:")
            print(f"   权限数量: {len(permissions)}")
            
            if len(permissions) == 0:
                print("   ❌ 权限数组为空 - 这是问题所在！")
            else:
                print("   ✅ 权限数组不为空")
                for perm in permissions:
                    print(f"      - {perm}")
            
            return result
        else:
            print(f"❌ API调用失败")
            try:
                error = response.json()
                print(f"错误信息: {error}")
            except:
                print(f"响应内容: {response.content}")
            return None
        
    except Exception as e:
        print(f"❌ 测试用户信息API失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_user_info_view():
    """检查用户信息视图实现"""
    print("\n🔍 检查用户信息视图实现")
    print("=" * 60)
    
    try:
        # 查找用户信息视图
        from apps.users import views
        
        # 检查是否有UserInfoView
        if hasattr(views, 'UserInfoView'):
            view_class = getattr(views, 'UserInfoView')
            print(f"✅ 找到UserInfoView")
            print(f"   类型: {type(view_class)}")
            
            # 检查方法
            if hasattr(view_class, 'get'):
                print(f"   ✅ 有get方法")
            else:
                print(f"   ❌ 没有get方法")
            
            return True
        else:
            print("❌ 未找到UserInfoView")
            
            # 列出所有可用的视图
            print("\n📋 可用的视图:")
            for attr_name in dir(views):
                attr = getattr(views, attr_name)
                if (hasattr(attr, '__bases__') and 
                    any('View' in base.__name__ for base in attr.__bases__)):
                    print(f"   {attr_name}")
            
            return False
        
    except Exception as e:
        print(f"❌ 检查用户信息视图失败: {e}")
        return False

def check_permissions_system():
    """检查权限系统"""
    print("\n🔍 检查权限系统")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        admin_user = User.objects.get(username='admin')
        
        # 检查Django内置权限
        print("1. Django内置权限:")
        user_permissions = admin_user.user_permissions.all()
        print(f"   用户直接权限数: {user_permissions.count()}")
        for perm in user_permissions:
            print(f"      - {perm.codename}: {perm.name}")
        
        # 检查组权限
        print(f"\n2. 用户组权限:")
        groups = admin_user.groups.all()
        print(f"   用户组数: {groups.count()}")
        for group in groups:
            print(f"      组: {group.name}")
            group_permissions = group.permissions.all()
            print(f"         权限数: {group_permissions.count()}")
            for perm in group_permissions:
                print(f"            - {perm.codename}: {perm.name}")
        
        # 检查所有权限
        print(f"\n3. 用户所有权限:")
        all_permissions = admin_user.get_all_permissions()
        print(f"   总权限数: {len(all_permissions)}")
        for perm in all_permissions:
            print(f"      - {perm}")
        
        # 检查超级用户状态
        print(f"\n4. 超级用户检查:")
        print(f"   is_superuser: {admin_user.is_superuser}")
        print(f"   has_perm('*'): {admin_user.has_perm('*')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查权限系统失败: {e}")
        return False

def check_custom_permissions():
    """检查自定义权限系统"""
    print("\n🔍 检查自定义权限系统")
    print("=" * 60)
    
    try:
        # 检查是否有自定义权限模型
        from django.apps import apps
        
        # 查找权限相关的模型
        permission_models = []
        for model in apps.get_models():
            model_name = model.__name__.lower()
            if 'permission' in model_name or 'role' in model_name:
                permission_models.append(model)
                print(f"📋 找到权限相关模型: {model.__name__}")
        
        if not permission_models:
            print("⚠️ 未找到自定义权限模型")
            return False
        
        # 检查admin用户在自定义权限系统中的配置
        from django.contrib.auth import get_user_model
        User = get_user_model()
        admin_user = User.objects.get(username='admin')
        
        # 检查用户角色
        if hasattr(admin_user, 'role') and admin_user.role:
            print(f"\n👤 用户角色信息:")
            role = admin_user.role
            print(f"   角色: {role}")
            print(f"   角色类型: {type(role)}")
            
            # 如果角色有权限，检查权限
            if hasattr(role, 'permissions'):
                role_permissions = role.permissions.all()
                print(f"   角色权限数: {role_permissions.count()}")
                for perm in role_permissions:
                    print(f"      - {perm}")
        else:
            print(f"\n⚠️ 用户没有分配角色")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查自定义权限系统失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_admin_permissions():
    """为admin用户创建权限"""
    print("\n🔧 为admin用户创建权限")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        from django.contrib.auth.models import Permission
        from django.contrib.contenttypes.models import ContentType
        
        User = get_user_model()
        admin_user = User.objects.get(username='admin')
        
        # 确保admin是超级用户
        if not admin_user.is_superuser:
            admin_user.is_superuser = True
            admin_user.is_staff = True
            admin_user.save()
            print("✅ 设置admin为超级用户")
        
        # 获取所有权限
        all_permissions = Permission.objects.all()
        print(f"📊 系统总权限数: {all_permissions.count()}")
        
        # 为admin用户添加所有权限
        admin_user.user_permissions.set(all_permissions)
        print("✅ 为admin用户添加所有权限")
        
        # 验证权限
        user_permissions = admin_user.get_all_permissions()
        print(f"✅ admin用户现在有 {len(user_permissions)} 个权限")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建admin权限失败: {e}")
        return False

def fix_user_info_api():
    """修复用户信息API"""
    print("\n🔧 修复用户信息API")
    print("=" * 60)
    
    # 这里我们需要检查UserInfoView的实现
    # 如果权限返回逻辑有问题，需要修复
    
    print("💡 需要检查以下几个方面:")
    print("1. UserInfoView是否正确返回权限")
    print("2. 权限序列化器是否正确")
    print("3. 权限计算逻辑是否正确")
    
    return True

def main():
    """主诊断函数"""
    print("🚀 开始Admin用户权限问题诊断")
    print("=" * 80)
    print("📋 处理前端issue: admin用户权限数组为空")
    print("=" * 80)
    
    # 1. 检查admin用户基本信息
    admin_user = check_admin_user()
    if not admin_user:
        print("❌ 无法继续，admin用户不存在")
        return False
    
    # 2. 检查权限系统
    check_permissions_system()
    
    # 3. 检查自定义权限系统
    check_custom_permissions()
    
    # 4. 检查用户信息视图
    check_user_info_view()
    
    # 5. 测试用户信息API
    api_result = check_user_info_api()
    
    # 6. 如果权限为空，尝试修复
    if api_result and api_result.get('permissions', []) == []:
        print("\n🔧 检测到权限为空，尝试修复...")
        create_admin_permissions()
        
        # 重新测试API
        print("\n🧪 重新测试API...")
        check_user_info_api()
    
    print("\n" + "=" * 80)
    print("📋 诊断总结")
    print("=" * 80)
    
    print("✅ 已完成admin用户权限问题诊断")
    print("\n💡 如果问题仍然存在，可能需要:")
    print("1. 检查UserInfoView的权限返回逻辑")
    print("2. 检查权限序列化器实现")
    print("3. 确保权限数据正确初始化")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
