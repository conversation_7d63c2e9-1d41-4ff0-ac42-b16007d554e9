#!/usr/bin/env python
"""
Team模型完全删除计划和执行脚本
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def analyze_team_usage():
    """分析Team模型的使用情况"""
    print("🔍 分析Team模型使用情况")
    print("=" * 60)
    
    try:
        from apps.users.models import Team, User, Department
        
        # 检查Team表中的数据
        team_count = Team.objects.count()
        print(f"📊 Team表中的记录数: {team_count}")
        
        if team_count > 0:
            print("📋 现有Team记录:")
            for team in Team.objects.all():
                print(f"   - {team.name} (ID: {team.id}, 部门: {team.department.name})")
        
        # 检查User表中引用Team的记录
        users_with_team = User.objects.filter(team__isnull=False).count()
        print(f"📊 引用Team的用户数: {users_with_team}")
        
        if users_with_team > 0:
            print("👥 引用Team的用户:")
            for user in User.objects.filter(team__isnull=False):
                print(f"   - {user.username} -> {user.team.name}")
        
        # 检查Department中的team_set关系
        departments_with_teams = Department.objects.filter(team__isnull=False).distinct().count()
        print(f"📊 有Team的部门数: {departments_with_teams}")
        
        return {
            'team_count': team_count,
            'users_with_team': users_with_team,
            'departments_with_teams': departments_with_teams
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def check_code_references():
    """检查代码中的Team引用"""
    print("\n🔍 检查代码中的Team引用")
    print("=" * 60)
    
    files_to_check = [
        "apps/users/models.py",
        "apps/users/serializers.py", 
        "apps/users/views.py",
        "apps/users/urls.py",
        "apps/users/admin.py",
    ]
    
    references = {}
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找Team相关的引用
            team_refs = []
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if 'Team' in line and not line.strip().startswith('#'):
                    team_refs.append(f"Line {i}: {line.strip()}")
            
            if team_refs:
                references[file_path] = team_refs
                print(f"📄 {file_path}:")
                for ref in team_refs[:5]:  # 只显示前5个引用
                    print(f"   {ref}")
                if len(team_refs) > 5:
                    print(f"   ... 还有 {len(team_refs) - 5} 个引用")
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    return references

def generate_removal_plan():
    """生成删除计划"""
    print("\n📋 Team模型删除计划")
    print("=" * 60)
    
    plan = {
        "step1": {
            "title": "数据备份和清理",
            "actions": [
                "备份Team表数据",
                "将User.team字段设置为NULL",
                "记录受影响的用户"
            ]
        },
        "step2": {
            "title": "删除模型定义",
            "actions": [
                "从models.py删除Team模型类",
                "从User模型删除team外键字段"
            ]
        },
        "step3": {
            "title": "删除序列化器",
            "actions": [
                "删除TeamListSerializer",
                "删除TeamDetailSerializer", 
                "删除TeamCreateUpdateSerializer",
                "删除TeamSerializer",
                "更新DepartmentDetailSerializer中的teams字段",
                "更新UserSerializer中的team相关字段"
            ]
        },
        "step4": {
            "title": "删除视图",
            "actions": [
                "删除TeamListCreateView",
                "删除TeamDetailView",
                "删除TeamUsersView",
                "删除DepartmentTeamsView",
                "更新UserListCreateView中的team过滤"
            ]
        },
        "step5": {
            "title": "删除URL配置",
            "actions": [
                "删除team相关的URL模式",
                "删除department teams相关的URL"
            ]
        },
        "step6": {
            "title": "生成数据库迁移",
            "actions": [
                "生成删除team字段的迁移",
                "生成删除Team表的迁移",
                "应用迁移"
            ]
        },
        "step7": {
            "title": "更新文档和测试",
            "actions": [
                "更新API文档",
                "删除Team相关的测试",
                "更新README文档"
            ]
        }
    }
    
    for step_key, step_info in plan.items():
        print(f"\n{step_key.upper()}: {step_info['title']}")
        for i, action in enumerate(step_info['actions'], 1):
            print(f"   {i}. {action}")
    
    return plan

def backup_team_data():
    """备份Team数据"""
    print("\n💾 备份Team数据")
    print("=" * 60)
    
    try:
        from apps.users.models import Team, User
        import json
        from datetime import datetime
        
        # 备份Team数据
        teams_data = []
        for team in Team.objects.all():
            team_data = {
                'id': team.id,
                'name': team.name,
                'description': team.description,
                'department_id': team.department.id,
                'department_name': team.department.name,
                'created_at': team.created_at.isoformat(),
                'updated_at': team.updated_at.isoformat(),
                'users': []
            }
            
            # 备份关联的用户
            for user in team.user_set.all():
                team_data['users'].append({
                    'id': user.id,
                    'username': user.username,
                    'name': user.name
                })
            
            teams_data.append(team_data)
        
        # 保存备份文件
        backup_file = f"debug/team_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(teams_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 备份完成: {backup_file}")
        print(f"📊 备份了 {len(teams_data)} 个团队的数据")
        
        return backup_file
        
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return None

def clear_team_references():
    """清理Team引用"""
    print("\n🧹 清理Team引用")
    print("=" * 60)
    
    try:
        from apps.users.models import User
        
        # 将所有用户的team字段设置为NULL
        users_updated = User.objects.filter(team__isnull=False).update(team=None)
        print(f"✅ 清理了 {users_updated} 个用户的team引用")
        
        return users_updated
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return 0

def main():
    """主函数"""
    print("🚀 开始Team模型删除分析")
    print("=" * 80)
    
    # 1. 分析使用情况
    usage_info = analyze_team_usage()
    
    # 2. 检查代码引用
    code_refs = check_code_references()
    
    # 3. 生成删除计划
    plan = generate_removal_plan()
    
    # 4. 数据备份
    backup_file = backup_team_data()
    
    # 5. 清理引用
    if usage_info and usage_info['users_with_team'] > 0:
        print(f"\n⚠️ 发现 {usage_info['users_with_team']} 个用户引用了Team")
        response = input("是否要清理这些引用？(y/N): ")
        if response.lower() == 'y':
            clear_team_references()
    
    print("\n" + "=" * 80)
    print("📋 删除准备工作完成")
    print("=" * 80)
    
    print("✅ 已完成的准备工作:")
    print("   1. 分析了Team模型使用情况")
    print("   2. 检查了代码中的引用")
    print("   3. 生成了详细的删除计划")
    if backup_file:
        print(f"   4. 备份了数据到: {backup_file}")
    print("   5. 清理了数据库中的Team引用")
    
    print("\n📋 接下来需要手动执行:")
    print("   1. 删除模型定义和序列化器")
    print("   2. 删除视图和URL配置")
    print("   3. 生成并应用数据库迁移")
    print("   4. 更新文档和测试")
    
    print("\n⚠️ 注意事项:")
    print("   - 确保在删除前已备份重要数据")
    print("   - 删除后需要重新生成API文档")
    print("   - 前端代码可能需要相应调整")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
