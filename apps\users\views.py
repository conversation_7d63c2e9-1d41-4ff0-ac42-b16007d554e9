from rest_framework import status, generics, permissions, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenRefreshView
from django.contrib.auth.tokens import default_token_generator
from django.contrib.auth import logout
from django.contrib.auth.models import Group
from django.core.mail import send_mail
from django.conf import settings
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.utils.translation import gettext_lazy as _
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils import timezone
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from .models import User, Department, Role
from .serializers import (
    UserSerializer,
    UserCreateSerializer,
    UserUpdateSerializer,
    LoginSerializer,
    PasswordChangeSerializer,
    PasswordResetSerializer,
    PasswordResetConfirmSerializer,
    FirstLoginPasswordChangeSerializer,
    MFASetupSerializer,
    MFAQRCodeSerializer,
    DepartmentSerializer,
    RoleSerializer,
    GroupListSerializer,
    GroupDetailSerializer,
    GroupCreateUpdateSerializer,
)
from apps.audit.models import BusinessOperationLog
from apps.audit.utils import log_business_operation
import logging

logger = logging.getLogger(__name__)


class LoginView(APIView):
    """用户登录视图"""

    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = LoginSerializer(data=request.data, context={"request": request})

        if serializer.is_valid():
            user = serializer.validated_data["user"]

            # 检查是否需要强制修改密码
            if user.requires_password_change:
                # 生成临时令牌用于密码修改
                temp_token = user.generate_temp_password_token()

                # 记录首次登录或密码过期事件
                try:
                    action_type = (
                        "first_login"
                        if user.is_first_login
                        else "password_expired_login"
                    )
                    log_business_operation(
                        user=user,
                        action_type=action_type,
                        description=f"用户 {user.username} 需要强制修改密码",
                        target_type="user",
                        target_id=str(user.id),
                        target_name=user.username,
                        request=request,
                        extra_data={
                            "login_method": "password",
                            "requires_password_change": True,
                            "is_first_login": user.is_first_login,
                            "password_expired": bool(
                                user.password_expires_at
                                and user.password_expires_at < timezone.now()
                            ),
                        },
                    )
                except Exception as e:
                    logger.error(f"记录登录日志失败: {e}")

                return Response(
                    {
                        "requires_password_change": True,
                        "temp_token": temp_token,
                        "user_id": user.id,
                        "username": user.username,
                        "is_first_login": user.is_first_login,
                        "message": (
                            _("首次登录需要修改密码")
                            if user.is_first_login
                            else _("密码已过期，需要修改密码")
                        ),
                    },
                    status=status.HTTP_200_OK,
                )

            # 正常登录流程
            # 生成JWT令牌
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # 记录登录操作日志
            try:
                log_business_operation(
                    user=user,
                    action_type="user_login",
                    description=f"用户 {user.username} 登录成功",
                    target_type="user",
                    target_id=str(user.id),
                    target_name=user.username,
                    request=request,
                    extra_data={"login_method": "password"},
                )
            except Exception as e:
                logger.error(f"记录登录日志失败: {e}")

            return Response(
                {
                    "access_token": str(access_token),
                    "refresh_token": str(refresh),
                    "user": UserSerializer(user).data,
                    "message": _("登录成功"),
                },
                status=status.HTTP_200_OK,
            )

        # 登录失败，django-axes会自动记录失败日志

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class LogoutView(APIView):
    """用户登出视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            # 记录登出日志
            OperationLog.objects.create(
                user=request.user,
                action="logout",
                resource_type="user",
                resource_id=str(request.user.id),
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
            )

            # 将刷新令牌加入黑名单
            refresh_token = request.data.get("refresh_token")
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            return Response({"message": _("登出成功")}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"登出失败: {e}")
            return Response(
                {"error": _("登出失败")}, status=status.HTTP_400_BAD_REQUEST
            )

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class CustomTokenRefreshView(TokenRefreshView):
    """自定义令牌刷新视图"""

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)

        if response.status_code == 200:
            # 记录令牌刷新日志
            try:
                OperationLog.objects.create(
                    user=request.user if request.user.is_authenticated else None,
                    action="token_refresh",
                    resource_type="token",
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )
            except Exception as e:
                logger.error(f"记录令牌刷新日志失败: {e}")

        return response

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class PasswordChangeView(APIView):
    """密码修改视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = PasswordChangeSerializer(
            data=request.data, context={"request": request}
        )

        if serializer.is_valid():
            serializer.save()

            # 记录密码修改日志
            try:
                OperationLog.objects.create(
                    user=request.user,
                    action="password_change",
                    resource_type="user",
                    resource_id=str(request.user.id),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )
            except Exception as e:
                logger.error(f"记录密码修改日志失败: {e}")

            return Response({"message": _("密码修改成功")}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class FirstLoginPasswordChangeView(APIView):
    """首次登录密码修改视图"""

    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = FirstLoginPasswordChangeSerializer(data=request.data)

        if serializer.is_valid():
            user = serializer.save()

            # 记录首次登录密码修改日志
            try:
                log_business_operation(
                    user=user,
                    action_type="first_login_password_change",
                    description=f"用户 {user.username} 完成首次登录密码修改",
                    target_type="user",
                    target_id=str(user.id),
                    target_name=user.username,
                    request=request,
                    extra_data={"first_login_completed": True},
                )
            except Exception as e:
                logger.error(f"记录首次登录密码修改日志失败: {e}")

            # 生成JWT令牌
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            return Response(
                {
                    "access_token": str(access_token),
                    "refresh_token": str(refresh),
                    "user": UserSerializer(user).data,
                    "message": _("密码修改成功，首次登录完成"),
                },
                status=status.HTTP_200_OK,
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetView(APIView):
    """密码重置请求视图"""

    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = PasswordResetSerializer(data=request.data)

        if serializer.is_valid():
            user = serializer.user

            # 生成重置令牌
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))

            # 构建重置链接
            reset_url = request.build_absolute_uri(
                reverse(
                    "password_reset_confirm", kwargs={"uidb64": uid, "token": token}
                )
            )

            # 发送重置邮件
            try:
                subject = _("密码重置请求")
                message = render_to_string(
                    "emails/password_reset.html",
                    {
                        "user": user,
                        "reset_url": reset_url,
                        "site_name": "Password Locker",
                    },
                )

                send_mail(
                    subject,
                    message,
                    settings.DEFAULT_FROM_EMAIL,
                    [user.email],
                    html_message=message,
                )

                # 记录操作日志
                OperationLog.objects.create(
                    user=user,
                    action="password_reset_request",
                    resource_type="user",
                    resource_id=str(user.id),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )

                return Response(
                    {"message": _("密码重置邮件已发送，请检查您的邮箱")},
                    status=status.HTTP_200_OK,
                )

            except Exception as e:
                logger.error(f"发送密码重置邮件失败: {e}")
                return Response(
                    {"error": _("发送邮件失败，请稍后再试")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class PasswordResetConfirmView(APIView):
    """密码重置确认视图"""

    permission_classes = [permissions.AllowAny]

    def post(self, request, uidb64, token):
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            return Response(
                {"error": _("无效的重置链接")}, status=status.HTTP_400_BAD_REQUEST
            )

        if not default_token_generator.check_token(user, token):
            return Response(
                {"error": _("重置链接已过期或无效")}, status=status.HTTP_400_BAD_REQUEST
            )

        serializer = PasswordResetConfirmSerializer(data=request.data)

        if serializer.is_valid():
            user.set_password(serializer.validated_data["new_password"])
            user.save()

            # 记录密码重置日志
            try:
                OperationLog.objects.create(
                    user=user,
                    action="password_reset_confirm",
                    resource_type="user",
                    resource_id=str(user.id),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )
            except Exception as e:
                logger.error(f"记录密码重置日志失败: {e}")

            return Response({"message": _("密码重置成功")}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class MFASetupView(APIView):
    """MFA设置视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = MFASetupSerializer(data=request.data, context={"request": request})

        if serializer.is_valid():
            user = serializer.save()

            # 记录MFA设置日志
            try:
                action = "mfa_enable" if user.is_mfa_enabled else "mfa_disable"
                OperationLog.objects.create(
                    user=request.user,
                    action=action,
                    resource_type="user",
                    resource_id=str(request.user.id),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )
            except Exception as e:
                logger.error(f"记录MFA设置日志失败: {e}")

            message = (
                _("多因素认证已启用") if user.is_mfa_enabled else _("多因素认证已禁用")
            )
            return Response({"message": message}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class MFAQRCodeView(APIView):
    """MFA二维码视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        serializer = MFAQRCodeSerializer(context={"request": request})
        return Response(serializer.to_representation(None), status=status.HTTP_200_OK)


class UserProfileView(APIView):
    """用户个人资料视图 - 返回用户信息和权限"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        user = request.user
        serializer = UserSerializer(user)
        user_data = serializer.data

        # 获取用户权限
        permissions = self._get_user_permissions(user)

        # 返回前端期望的格式
        return Response(
            {"userInfo": user_data, "permissions": permissions},
            status=status.HTTP_200_OK,
        )

    def _get_user_permissions(self, user):
        """获取用户权限列表"""
        permissions = []

        # 超级用户拥有所有权限
        if user.is_superuser:
            permissions = [
                # 用户管理权限
                "user:create",
                "user:read",
                "user:update",
                "user:delete",
                "user:change_password",
                "user:unlock",
                # 角色管理权限
                "role:create",
                "role:read",
                "role:update",
                "role:delete",
                # 部门管理权限
                "department:create",
                "department:read",
                "department:update",
                "department:delete",
                # 组管理权限
                "group:create",
                "group:read",
                "group:update",
                "group:delete",
                # 密码管理权限
                "password:create",
                "password:read",
                "password:update",
                "password:delete",
                "password:copy",
                "password:share",
                # 系统管理权限
                "system:*",
                "audit:read",
                "settings:manage",
            ]
        elif user.is_staff:
            # 管理员权限
            permissions = [
                "user:create",
                "user:read",
                "user:update",
                "user:delete",
                "user:change_password",
                "user:unlock",
                "role:read",
                "department:read",
                "group:read",
                "password:create",
                "password:read",
                "password:update",
                "password:delete",
                "audit:read",
            ]
        else:
            # 普通用户权限
            permissions = [
                "user:read",  # 可以查看用户信息
                "password:create",
                "password:read",
                "password:update",
                "password:copy",
            ]

        # 根据用户角色添加额外权限
        if hasattr(user, "role") and user.role:
            role_permissions = self._get_role_permissions(user.role)
            permissions.extend(role_permissions)

        # 去重并排序
        return sorted(list(set(permissions)))

    def _get_role_permissions(self, role):
        """根据角色获取权限"""
        role_permissions = []

        if hasattr(role, "name"):
            role_name = role.name

            if role_name == "admin":
                role_permissions = [
                    "user:create",
                    "user:read",
                    "user:update",
                    "user:delete",
                    "role:create",
                    "role:read",
                    "role:update",
                    "role:delete",
                    "system:manage",
                ]
            elif role_name == "manager":
                role_permissions = [
                    "user:read",
                    "user:update",
                    "password:create",
                    "password:read",
                    "password:update",
                    "password:delete",
                ]
            elif role_name == "user":
                role_permissions = [
                    "password:create",
                    "password:read",
                    "password:update",
                ]

        return role_permissions


class UserInfoView(APIView):
    """用户信息和权限视图 - 为前端提供用户信息和权限数据"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取当前用户信息和权限"""
        user = request.user

        # 用户基本信息
        user_info = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "name": user.name,
            "phone": user.phone,
        }

        # 获取用户权限
        permissions = self._get_user_permissions(user)

        return Response(
            {"userInfo": user_info, "permissions": permissions},
            status=status.HTTP_200_OK,
        )

    def _get_user_permissions(self, user):
        """获取用户权限列表"""
        permissions = []

        # 超级用户拥有所有权限
        if user.is_superuser:
            permissions = [
                # 用户管理权限
                "user:create",
                "user:read",
                "user:update",
                "user:delete",
                "user:change_password",
                "user:unlock",
                # 角色管理权限
                "role:create",
                "role:read",
                "role:update",
                "role:delete",
                # 部门管理权限
                "department:create",
                "department:read",
                "department:update",
                "department:delete",
                # 组管理权限
                "group:create",
                "group:read",
                "group:update",
                "group:delete",
                # 密码管理权限
                "password:create",
                "password:read",
                "password:update",
                "password:delete",
                "password:copy",
                "password:share",
                # 系统管理权限
                "system:*",
                "audit:read",
                "settings:manage",
            ]
        elif user.is_staff:
            # 管理员权限
            permissions = [
                "user:create",
                "user:read",
                "user:update",
                "user:delete",
                "user:change_password",
                "user:unlock",
                "role:read",
                "department:read",
                "group:read",
                "password:create",
                "password:read",
                "password:update",
                "password:delete",
                "audit:read",
            ]
        else:
            # 普通用户权限
            permissions = [
                "user:read",  # 可以查看用户信息
                "password:create",
                "password:read",
                "password:update",
                "password:copy",
            ]

        # 根据用户角色添加额外权限
        if hasattr(user, "role") and user.role:
            role_permissions = self._get_role_permissions(user.role)
            permissions.extend(role_permissions)

        # 去重并排序
        return sorted(list(set(permissions)))

    def _get_role_permissions(self, role):
        """根据角色获取权限"""
        role_permissions = []

        if hasattr(role, "name"):
            role_name = role.name

            if role_name == "admin":
                role_permissions = [
                    "user:create",
                    "user:read",
                    "user:update",
                    "user:delete",
                    "role:create",
                    "role:read",
                    "role:update",
                    "role:delete",
                    "system:manage",
                ]
            elif role_name == "manager":
                role_permissions = [
                    "user:read",
                    "user:update",
                    "password:create",
                    "password:read",
                    "password:update",
                    "password:delete",
                ]
            elif role_name == "user":
                role_permissions = [
                    "password:create",
                    "password:read",
                    "password:update",
                ]

        return role_permissions


class AccessCodesView(APIView):
    """获取用户权限码视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        # 根据用户角色返回权限码
        user = request.user
        access_codes = []

        # 基础权限
        access_codes.append("user:read")

        # 根据角色添加权限
        if user.role:
            if user.role.name == "admin":
                access_codes.extend(
                    [
                        "user:create",
                        "user:update",
                        "user:delete",
                        "password:create",
                        "password:read",
                        "password:update",
                        "password:delete",
                        "category:create",
                        "category:read",
                        "category:update",
                        "category:delete",
                        "system:read",
                        "system:update",
                    ]
                )
            elif user.role.name == "manager":
                access_codes.extend(
                    [
                        "password:create",
                        "password:read",
                        "password:update",
                        "password:delete",
                        "category:create",
                        "category:read",
                        "category:update",
                        "category:delete",
                    ]
                )
            elif user.role.name == "user":
                access_codes.extend(
                    ["password:read", "password:create", "password:update"]
                )
        else:
            # 如果用户没有角色，给予基本的密码管理权限
            access_codes.extend(["password:read", "password:create", "password:update"])

        return Response(access_codes, status=status.HTTP_200_OK)


class UserProfileUpdateView(APIView):
    """用户个人资料更新视图"""

    permission_classes = [permissions.IsAuthenticated]

    def put(self, request):
        serializer = UserUpdateSerializer(request.user, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()

            # 记录用户信息更新日志
            try:
                OperationLog.objects.create(
                    user=request.user,
                    action="profile_update",
                    resource_type="user",
                    resource_id=str(request.user.id),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    details={"updated_fields": list(serializer.validated_data.keys())},
                )
            except Exception as e:
                logger.error(f"记录用户信息更新日志失败: {e}")

            return Response(
                UserSerializer(request.user).data, status=status.HTTP_200_OK
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


# 用户管理视图（管理员功能）
class UserListCreateView(generics.ListCreateAPIView):
    """用户列表和创建视图 - 增强版"""

    permission_classes = [permissions.IsAuthenticated]
    pagination_class = PageNumberPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["department", "role", "is_active", "is_staff"]
    search_fields = ["username", "email", "name", "first_name", "last_name"]
    ordering_fields = ["username", "email", "name", "date_joined", "created_at"]
    ordering = ["-date_joined"]

    def get_queryset(self):
        """获取用户查询集，支持高级过滤和查询优化"""
        queryset = User.objects.select_related("department", "role").prefetch_related(
            "groups"
        )

        # 权限控制：非管理员只能看到自己的信息
        if not self.request.user.is_staff:
            queryset = queryset.filter(id=self.request.user.id)

        return queryset

    def get_serializer_class(self):
        if self.request.method == "POST":
            return UserCreateSerializer
        return UserSerializer

    def perform_create(self, serializer):
        user = serializer.save()

        # 记录用户创建日志
        try:
            OperationLog.objects.create(
                user=self.request.user,
                action_type="user_create",
                target_type="user",
                target_id=str(user.id),
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
                extra_data={"created_user": user.username},
            )
        except Exception as e:
            logger.error(f"记录用户创建日志失败: {e}")

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class UserDetailView(generics.RetrieveUpdateDestroyAPIView):
    """用户详情视图 - 增强版"""

    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """获取用户查询集，支持查询优化"""
        return User.objects.select_related("department", "role").prefetch_related(
            "groups"
        )

    def get_serializer_class(self):
        """根据请求方法返回不同的序列化器"""
        if self.request.method == "GET":
            return UserSerializer
        return UserUpdateSerializer

    def perform_update(self, serializer):
        user = serializer.save()

        # 记录用户更新日志
        try:
            OperationLog.objects.create(
                user=self.request.user,
                action="user_update",
                resource_type="user",
                resource_id=str(user.id),
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
                details={
                    "updated_user": user.username,
                    "updated_fields": list(serializer.validated_data.keys()),
                },
            )
        except Exception as e:
            logger.error(f"记录用户更新日志失败: {e}")

    def perform_destroy(self, instance):
        # 记录用户删除日志
        try:
            OperationLog.objects.create(
                user=self.request.user,
                action_type="user_delete",
                target_type="user",
                target_id=str(instance.id),
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
                extra_data={"deleted_user": instance.username},
            )
        except Exception as e:
            logger.error(f"记录用户删除日志失败: {e}")

        instance.delete()

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class UserResetPasswordView(APIView):
    """用户密码重置视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        """重置用户密码"""
        try:
            user = get_object_or_404(User, pk=pk)
            new_password = request.data.get("new_password")

            if not new_password:
                return Response(
                    {"error": "新密码不能为空"}, status=status.HTTP_400_BAD_REQUEST
                )

            # 设置新密码
            user.set_password(new_password)
            user.save()

            # 记录操作日志
            try:
                log_business_operation(
                    user=request.user,
                    action_type="user_password_reset",
                    description=f"管理员重置用户 {user.username} 的密码",
                    target_type="user",
                    target_id=str(user.id),
                    target_name=user.username,
                    request=request,
                    extra_data={"reset_by_admin": True},
                )
            except Exception as e:
                logger.error(f"记录密码重置日志失败: {e}")

            return Response({"message": "密码重置成功"}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"重置密码失败: {e}")
            return Response(
                {"error": "重置密码失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserToggleActiveView(APIView):
    """用户激活状态切换视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        """切换用户激活状态"""
        try:
            user = get_object_or_404(User, pk=pk)

            # 切换激活状态
            user.is_active = not user.is_active
            user.save()

            # 记录操作日志
            try:
                action_type = "user_activate" if user.is_active else "user_deactivate"
                description = (
                    f"{'激活' if user.is_active else '停用'}用户 {user.username}"
                )

                log_business_operation(
                    user=request.user,
                    action_type=action_type,
                    description=description,
                    target_type="user",
                    target_id=str(user.id),
                    target_name=user.username,
                    request=request,
                    extra_data={"is_active": user.is_active},
                )
            except Exception as e:
                logger.error(f"记录激活状态切换日志失败: {e}")

            return Response(
                {
                    "message": f"用户已{'激活' if user.is_active else '停用'}",
                    "is_active": user.is_active,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"切换激活状态失败: {e}")
            return Response(
                {"error": "切换激活状态失败"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# ============ 关联查询视图 ============


class DepartmentUsersView(generics.ListAPIView):
    """部门用户列表视图"""

    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = PageNumberPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["username", "email", "name"]
    ordering_fields = ["username", "email", "date_joined"]
    ordering = ["username"]

    def get_queryset(self):
        department_id = self.kwargs["pk"]
        department = get_object_or_404(Department, pk=department_id)
        return department.user_set.filter(is_active=True).select_related(
            "department", "role"
        )


class RoleUsersView(generics.ListAPIView):
    """角色用户列表视图"""

    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = PageNumberPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["username", "email", "name"]
    ordering_fields = ["username", "email", "date_joined"]
    ordering = ["username"]

    def get_queryset(self):
        role_id = self.kwargs["pk"]
        role = get_object_or_404(Role, pk=role_id)
        return role.user_set.filter(is_active=True).select_related("department", "role")


# ============ 用户组管理视图 ============


class GroupListCreateView(generics.ListCreateAPIView):
    """用户组列表和创建视图"""

    queryset = Group.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = PageNumberPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["name"]
    ordering_fields = ["name"]
    ordering = ["name"]

    def get_serializer_class(self):
        if self.request.method == "POST":
            return GroupCreateUpdateSerializer
        return GroupListSerializer


class GroupDetailView(generics.RetrieveUpdateDestroyAPIView):
    """用户组详情视图"""

    queryset = Group.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return GroupDetailSerializer
        return GroupCreateUpdateSerializer


class GroupUsersView(generics.ListAPIView):
    """用户组用户列表视图"""

    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = PageNumberPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["username", "email", "name"]
    ordering_fields = ["username", "email", "date_joined"]
    ordering = ["username"]

    def get_queryset(self):
        group_id = self.kwargs["pk"]
        group = get_object_or_404(Group, pk=group_id)
        return group.user_set.filter(is_active=True).select_related(
            "department", "role"
        )


class GroupAddUsersView(APIView):
    """向用户组添加用户视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        """向用户组添加用户"""
        try:
            group = get_object_or_404(Group, pk=pk)
            user_ids = request.data.get("user_ids", [])

            if not user_ids:
                return Response(
                    {"error": "用户ID列表不能为空"}, status=status.HTTP_400_BAD_REQUEST
                )

            # 获取用户对象
            users = User.objects.filter(id__in=user_ids, is_active=True)
            if not users.exists():
                return Response(
                    {"error": "未找到有效的用户"}, status=status.HTTP_400_BAD_REQUEST
                )

            # 添加用户到组
            group.user_set.add(*users)

            # 记录操作日志
            try:
                log_business_operation(
                    user=request.user,
                    action_type="group_add_users",
                    description=f"向用户组 {group.name} 添加 {users.count()} 个用户",
                    target_type="group",
                    target_id=str(group.id),
                    target_name=group.name,
                    request=request,
                    extra_data={"user_ids": user_ids, "users_count": users.count()},
                )
            except Exception as e:
                logger.error(f"记录用户组添加用户日志失败: {e}")

            return Response(
                {
                    "message": f"成功添加 {users.count()} 个用户到用户组",
                    "added_count": users.count(),
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"添加用户到用户组失败: {e}")
            return Response(
                {"error": "添加用户失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GroupRemoveUsersView(APIView):
    """从用户组移除用户视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        """从用户组移除用户"""
        try:
            group = get_object_or_404(Group, pk=pk)
            user_ids = request.data.get("user_ids", [])

            if not user_ids:
                return Response(
                    {"error": "用户ID列表不能为空"}, status=status.HTTP_400_BAD_REQUEST
                )

            # 获取用户对象
            users = User.objects.filter(id__in=user_ids)
            if not users.exists():
                return Response(
                    {"error": "未找到有效的用户"}, status=status.HTTP_400_BAD_REQUEST
                )

            # 从组中移除用户
            group.user_set.remove(*users)

            # 记录操作日志
            try:
                log_business_operation(
                    user=request.user,
                    action_type="group_remove_users",
                    description=f"从用户组 {group.name} 移除 {users.count()} 个用户",
                    target_type="group",
                    target_id=str(group.id),
                    target_name=group.name,
                    request=request,
                    extra_data={"user_ids": user_ids, "users_count": users.count()},
                )
            except Exception as e:
                logger.error(f"记录用户组移除用户日志失败: {e}")

            return Response(
                {
                    "message": f"成功从用户组移除 {users.count()} 个用户",
                    "removed_count": users.count(),
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"从用户组移除用户失败: {e}")
            return Response(
                {"error": "移除用户失败"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# 部门管理视图
class DepartmentListCreateView(generics.ListCreateAPIView):
    """部门列表和创建视图"""

    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]


class DepartmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """部门详情视图"""

    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]


# 角色管理视图
class RoleListCreateView(generics.ListCreateAPIView):
    """角色列表和创建视图"""

    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [permissions.IsAuthenticated]


class RoleDetailView(generics.RetrieveUpdateDestroyAPIView):
    """角色详情视图"""

    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [permissions.IsAuthenticated]
