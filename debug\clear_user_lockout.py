#!/usr/bin/env python
"""
清除用户自定义锁定状态
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def check_user_lockout_status():
    """检查用户锁定状态"""
    print("🔍 检查用户自定义锁定状态")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        from django.utils import timezone
        
        User = get_user_model()
        
        # 检查所有用户的锁定状态
        users = User.objects.all()
        
        for user in users:
            print(f"\n👤 用户: {user.username}")
            print(f"   失败登录次数: {user.failed_login_attempts}")
            print(f"   锁定到: {user.locked_until}")
            print(f"   是否被锁定: {user.is_locked}")
            
            if user.is_locked:
                print(f"   🔒 用户被锁定到: {user.locked_until}")
                remaining = user.locked_until - timezone.now()
                print(f"   ⏰ 剩余锁定时间: {remaining}")
            else:
                print(f"   ✅ 用户未被锁定")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查用户锁定状态失败: {e}")
        return False

def clear_user_lockout():
    """清除用户锁定状态"""
    print("\n🔧 清除用户锁定状态")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 重置所有用户的锁定状态
        target_users = ['root', 'admin']
        
        for username in target_users:
            try:
                user = User.objects.get(username=username)
                
                print(f"\n🔓 重置用户: {username}")
                print(f"   重置前 - 失败次数: {user.failed_login_attempts}, 锁定到: {user.locked_until}")
                
                # 重置失败次数和锁定状态
                user.reset_failed_attempts()
                
                print(f"   重置后 - 失败次数: {user.failed_login_attempts}, 锁定到: {user.locked_until}")
                print(f"   ✅ 用户 {username} 锁定状态已清除")
                
            except User.DoesNotExist:
                print(f"   ❌ 用户 {username} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 清除用户锁定状态失败: {e}")
        return False

def test_login_after_clear():
    """清除锁定后测试登录"""
    print("\n🧪 测试清除锁定后的登录")
    print("=" * 60)
    
    try:
        from django.test import Client
        import json
        
        client = Client()
        
        # 测试用户凭据
        test_credentials = [
            {'username': 'root', 'password': 'root123!'},
            {'username': 'admin', 'password': 'admin123!'},
        ]
        
        for creds in test_credentials:
            print(f"\n🔐 测试登录: {creds['username']} / {creds['password']}")
            
            response = client.post(
                '/api/auth/login/',
                data=json.dumps(creds),
                content_type='application/json'
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 登录成功")
                try:
                    data = response.json()
                    if 'access_token' in data:
                        token = data['access_token']
                        print(f"   🎫 访问令牌: {token[:20]}...")
                    else:
                        print(f"   ⚠️ 响应中没有访问令牌")
                except Exception as e:
                    print(f"   ⚠️ 响应解析失败: {e}")
            else:
                print(f"   ❌ 登录失败")
                try:
                    data = response.json()
                    print(f"   📝 错误信息: {data}")
                except:
                    print(f"   📝 响应内容: {response.content[:100]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试登录失败: {e}")
        return False

def disable_user_lockout_mechanism():
    """禁用用户锁定机制"""
    print("\n🔧 建议禁用用户锁定机制")
    print("=" * 60)
    
    print("💡 为了完全禁用用户锁定，建议修改以下代码:")
    print()
    print("1. 在 apps/users/models.py 中修改 increment_failed_attempts 方法:")
    print("   def increment_failed_attempts(self):")
    print("       # 仅增加计数，不设置锁定")
    print("       self.failed_login_attempts += 1")
    print("       # 注释掉锁定逻辑")
    print("       # if self.failed_login_attempts >= 5:")
    print("       #     self.locked_until = timezone.now() + timedelta(minutes=30)")
    print("       self.save(update_fields=['failed_login_attempts'])")
    print()
    print("2. 或者在 apps/users/serializers.py 中注释掉锁定检查:")
    print("   # if user.is_locked:")
    print("   #     raise serializers.ValidationError(_('账户已被锁定，请稍后再试'))")
    print()
    print("3. 这样可以保留失败次数统计，但不会锁定用户")

def main():
    """主函数"""
    print("🚀 开始清除用户锁定状态")
    print("=" * 80)
    
    # 1. 检查当前锁定状态
    check_user_lockout_status()
    
    # 2. 清除锁定状态
    clear_user_lockout()
    
    # 3. 再次检查状态
    print("\n🔍 清除后的状态检查")
    check_user_lockout_status()
    
    # 4. 测试登录
    test_login_after_clear()
    
    # 5. 建议禁用锁定机制
    disable_user_lockout_mechanism()
    
    print("\n" + "=" * 80)
    print("📋 操作总结")
    print("=" * 80)
    
    print("✅ 已完成的操作:")
    print("   1. 检查了用户自定义锁定状态")
    print("   2. 清除了用户锁定状态")
    print("   3. 重置了失败登录次数")
    print("   4. 测试了登录功能")
    
    print("\n💡 如果登录仍然失败:")
    print("   1. 检查是否还有其他锁定机制")
    print("   2. 考虑临时禁用用户锁定检查")
    print("   3. 重启Django服务器")
    print("   4. 检查前端发送的请求格式")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
