#!/usr/bin/env python
"""
登录问题诊断脚本
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def check_user_exists():
    """检查用户是否存在"""
    print("🔍 检查用户账户状态")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 检查root用户
        print("1. 检查 'root' 用户:")
        try:
            root_user = User.objects.get(username='root')
            print(f"   ✅ 用户存在: {root_user.username}")
            print(f"   📧 邮箱: {root_user.email}")
            print(f"   🆔 用户ID: {root_user.id}")
            print(f"   🔓 激活状态: {root_user.is_active}")
            print(f"   👑 管理员: {root_user.is_staff}")
            print(f"   🔑 超级用户: {root_user.is_superuser}")
            print(f"   🆕 首次登录: {root_user.is_first_login}")
            print(f"   🔄 需要修改密码: {root_user.password_must_change}")
            print(f"   🎫 临时令牌: {root_user.temp_password_token}")
            print(f"   📅 最后登录: {root_user.last_login}")
            print(f"   📅 加入时间: {root_user.date_joined}")
            
            # 检查密码
            if root_user.check_password('root123!'):
                print("   ✅ 密码 'root123!' 正确")
            else:
                print("   ❌ 密码 'root123!' 不正确")
                
            # 尝试其他常见密码
            common_passwords = ['root', 'admin', '123456', 'password', 'root123']
            for pwd in common_passwords:
                if root_user.check_password(pwd):
                    print(f"   ✅ 密码 '{pwd}' 正确")
                    break
            else:
                print("   ⚠️ 常见密码都不正确，可能需要重置密码")
                
            return root_user
            
        except User.DoesNotExist:
            print("   ❌ 'root' 用户不存在")
            
        # 检查admin用户（日志中显示的用户名）
        print("\n2. 检查 'admin' 用户:")
        try:
            admin_user = User.objects.get(username='admin')
            print(f"   ✅ 用户存在: {admin_user.username}")
            print(f"   🔓 激活状态: {admin_user.is_active}")
            print(f"   🆕 首次登录: {admin_user.is_first_login}")
            print(f"   🔄 需要修改密码: {admin_user.password_must_change}")
            
            if admin_user.check_password('root123!'):
                print("   ✅ 密码 'root123!' 正确")
            else:
                print("   ❌ 密码 'root123!' 不正确")
                
        except User.DoesNotExist:
            print("   ❌ 'admin' 用户不存在")
        
        # 列出所有用户
        print("\n3. 所有用户列表:")
        all_users = User.objects.all()
        for user in all_users:
            status = "🔓" if user.is_active else "🔒"
            admin_status = "👑" if user.is_staff else "👤"
            print(f"   {status}{admin_status} {user.username} (ID: {user.id}) - {user.email}")
            
        return None
        
    except Exception as e:
        print(f"❌ 检查用户失败: {e}")
        return None

def check_axes_lockout():
    """检查django-axes锁定状态"""
    print("\n🔒 检查django-axes锁定状态")
    print("=" * 60)
    
    try:
        from axes.models import AccessAttempt, AccessFailureLog
        
        # 检查访问尝试记录
        print("1. 最近的访问尝试:")
        recent_attempts = AccessAttempt.objects.all().order_by('-attempt_time')[:10]
        
        if recent_attempts:
            for attempt in recent_attempts:
                status = "🔒" if attempt.failures_since_start >= 5 else "🔓"
                print(f"   {status} {attempt.username} from {attempt.ip_address}")
                print(f"      失败次数: {attempt.failures_since_start}")
                print(f"      时间: {attempt.attempt_time}")
                print(f"      User-Agent: {attempt.user_agent[:50]}...")
                print()
        else:
            print("   📝 没有访问尝试记录")
        
        # 检查失败日志
        print("2. 最近的失败日志:")
        recent_failures = AccessFailureLog.objects.all().order_by('-attempt_time')[:5]
        
        if recent_failures:
            for failure in recent_failures:
                print(f"   ❌ {failure.username} from {failure.ip_address}")
                print(f"      时间: {failure.attempt_time}")
                print(f"      User-Agent: {failure.user_agent[:50]}...")
                print()
        else:
            print("   📝 没有失败日志记录")
            
        # 检查是否有锁定的用户
        locked_attempts = AccessAttempt.objects.filter(failures_since_start__gte=5)
        if locked_attempts:
            print("3. 被锁定的账户:")
            for attempt in locked_attempts:
                print(f"   🔒 {attempt.username} from {attempt.ip_address}")
                print(f"      失败次数: {attempt.failures_since_start}")
                print(f"      锁定时间: {attempt.attempt_time}")
        else:
            print("3. ✅ 没有被锁定的账户")
            
        return recent_attempts, recent_failures
        
    except Exception as e:
        print(f"❌ 检查axes状态失败: {e}")
        return None, None

def test_login_api():
    """测试登录API"""
    print("\n🧪 测试登录API")
    print("=" * 60)
    
    try:
        from django.test import Client
        import json
        
        client = Client()
        
        # 测试不同的用户名和密码组合
        test_cases = [
            ("root", "root123!"),
            ("admin", "root123!"),
            ("root", "root"),
            ("admin", "admin"),
        ]
        
        for username, password in test_cases:
            print(f"测试登录: {username} / {password}")
            
            login_data = {
                "username": username,
                "password": password
            }
            
            response = client.post(
                "/api/auth/login/",
                data=json.dumps(login_data),
                content_type="application/json"
            )
            
            print(f"   📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 登录成功")
                response_data = response.json()
                print(f"   🔑 获得令牌: {response_data.get('access_token', 'N/A')[:20]}...")
            else:
                print("   ❌ 登录失败")
                try:
                    error_data = response.json()
                    print(f"   错误信息: {error_data}")
                except:
                    print(f"   响应内容: {response.content.decode()}")
            print()
            
    except Exception as e:
        print(f"❌ 测试登录API失败: {e}")

def reset_user_password():
    """重置用户密码"""
    print("\n🔧 重置用户密码")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 尝试找到root用户
        try:
            user = User.objects.get(username='root')
        except User.DoesNotExist:
            # 如果root不存在，尝试找admin
            try:
                user = User.objects.get(username='admin')
            except User.DoesNotExist:
                print("   ❌ 找不到root或admin用户")
                return False
        
        print(f"找到用户: {user.username}")
        
        # 重置密码
        new_password = "root123!"
        user.set_password(new_password)
        
        # 确保用户状态正常
        user.is_active = True
        user.is_first_login = False
        user.password_must_change = False
        user.temp_password_token = ""
        
        user.save()
        
        print(f"   ✅ 密码已重置为: {new_password}")
        print(f"   ✅ 用户状态已正常化")
        
        # 验证密码
        if user.check_password(new_password):
            print("   ✅ 密码验证成功")
            return True
        else:
            print("   ❌ 密码验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 重置密码失败: {e}")
        return False

def clear_axes_lockout():
    """清除axes锁定"""
    print("\n🧹 清除django-axes锁定")
    print("=" * 60)
    
    try:
        from axes.models import AccessAttempt, AccessFailureLog
        
        # 清除访问尝试记录
        attempt_count = AccessAttempt.objects.count()
        AccessAttempt.objects.all().delete()
        print(f"   ✅ 清除了 {attempt_count} 条访问尝试记录")
        
        # 清除失败日志
        failure_count = AccessFailureLog.objects.count()
        AccessFailureLog.objects.all().delete()
        print(f"   ✅ 清除了 {failure_count} 条失败日志记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 清除axes锁定失败: {e}")
        return False

def create_test_user():
    """创建测试用户"""
    print("\n👤 创建测试用户")
    print("=" * 60)
    
    try:
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # 检查root用户是否存在
        if User.objects.filter(username='root').exists():
            print("   ℹ️ root用户已存在，跳过创建")
            return True
        
        # 创建root用户
        root_user = User.objects.create_superuser(
            username='root',
            email='<EMAIL>',
            password='root123!',
            name='Root Administrator'
        )
        
        print(f"   ✅ 创建用户: {root_user.username}")
        print(f"   📧 邮箱: {root_user.email}")
        print(f"   🔑 密码: root123!")
        print(f"   👑 超级用户: {root_user.is_superuser}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        return False

def main():
    """主诊断函数"""
    print("🚀 开始登录问题诊断")
    print("=" * 80)
    
    # 1. 检查用户账户
    user = check_user_exists()
    
    # 2. 检查axes锁定状态
    attempts, failures = check_axes_lockout()
    
    # 3. 清除axes锁定
    clear_axes_lockout()
    
    # 4. 如果没有找到用户，创建测试用户
    if not user:
        create_test_user()
    else:
        # 5. 重置用户密码
        reset_user_password()
    
    # 6. 测试登录API
    test_login_api()
    
    print("\n" + "=" * 80)
    print("📋 诊断总结")
    print("=" * 80)
    
    print("✅ 已完成的修复操作:")
    print("   1. 清除了django-axes的锁定记录")
    print("   2. 重置了用户密码为 'root123!'")
    print("   3. 确保用户状态正常")
    print("   4. 测试了登录API功能")
    
    print("\n💡 建议的解决方案:")
    print("   1. 使用用户名 'root' 和密码 'root123!' 登录")
    print("   2. 如果仍然失败，检查前端发送的请求格式")
    print("   3. 检查网络连接和CORS设置")
    print("   4. 查看浏览器开发者工具的网络请求")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
