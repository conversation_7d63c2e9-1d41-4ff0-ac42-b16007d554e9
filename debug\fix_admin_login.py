#!/usr/bin/env python
"""
修复admin登录问题
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def test_admin_login_direct():
    """直接测试admin登录"""
    print("🔍 直接测试admin登录")
    print("=" * 60)
    
    try:
        from django.test import Client
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        client = Client()
        
        # 测试GET请求
        print("1. 测试GET /admin/login/")
        response = client.get('/admin/login/')
        print(f"   状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"   ❌ GET请求失败: {response.status_code}")
            return False
        
        # 获取CSRF token
        csrf_token = None
        content = response.content.decode()
        
        # 简单的CSRF token提取
        import re
        csrf_match = re.search(r'name=["\']csrfmiddlewaretoken["\'] value=["\']([^"\']+)["\']', content)
        if csrf_match:
            csrf_token = csrf_match.group(1)
            print(f"   ✅ CSRF token获取成功: {csrf_token[:10]}...")
        else:
            print("   ⚠️ 未找到CSRF token")
        
        # 测试POST请求（使用错误凭据）
        print("\n2. 测试POST /admin/login/ (错误凭据)")
        post_data = {
            'username': 'nonexistent',
            'password': 'wrongpassword',
            'next': '/admin/',
        }
        
        if csrf_token:
            post_data['csrfmiddlewaretoken'] = csrf_token
        
        response = client.post('/admin/login/', post_data)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code in [200, 302]:
            print("   ✅ POST请求处理正常")
        else:
            print(f"   ❌ POST请求失败: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_middleware_chain():
    """测试中间件链"""
    print("\n🔍 测试中间件链")
    print("=" * 60)
    
    try:
        from django.test import RequestFactory
        from django.contrib.auth.models import AnonymousUser
        from django.conf import settings
        
        factory = RequestFactory()
        request = factory.get('/admin/login/')
        request.user = AnonymousUser()
        
        # 测试每个中间件
        middleware_classes = settings.MIDDLEWARE
        
        for i, middleware_path in enumerate(middleware_classes, 1):
            try:
                # 动态导入中间件
                module_path, class_name = middleware_path.rsplit('.', 1)
                module = __import__(module_path, fromlist=[class_name])
                middleware_class = getattr(module, class_name)
                
                # 实例化中间件
                middleware = middleware_class(lambda req: None)
                
                print(f"   {i}. {class_name}: ", end="")
                
                # 测试process_request
                if hasattr(middleware, 'process_request'):
                    result = middleware.process_request(request)
                    if result is not None:
                        print(f"返回了响应 ({type(result).__name__})")
                    else:
                        print("正常")
                else:
                    print("无process_request方法")
                
            except Exception as e:
                print(f"❌ 中间件 {middleware_path} 测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 中间件链测试失败: {e}")
        return False

def check_admin_urls():
    """检查admin URL配置"""
    print("\n🔍 检查admin URL配置")
    print("=" * 60)
    
    try:
        from django.urls import reverse
        
        # 测试admin相关URL
        admin_urls = [
            'admin:index',
            'admin:login',
            'admin:logout',
        ]
        
        for url_name in admin_urls:
            try:
                url = reverse(url_name)
                print(f"   ✅ {url_name}: {url}")
            except Exception as e:
                print(f"   ❌ {url_name}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查admin URL失败: {e}")
        return False

def fix_middleware_order():
    """修复中间件顺序"""
    print("\n🔧 检查中间件顺序")
    print("=" * 60)
    
    try:
        from django.conf import settings
        
        current_middleware = list(settings.MIDDLEWARE)
        print("当前中间件顺序:")
        for i, mw in enumerate(current_middleware, 1):
            print(f"   {i}. {mw}")
        
        # 建议的中间件顺序
        recommended_order = [
            "corsheaders.middleware.CorsMiddleware",
            "django.middleware.security.SecurityMiddleware",
            "django.contrib.sessions.middleware.SessionMiddleware",
            "django.middleware.common.CommonMiddleware",
            "django.middleware.csrf.CsrfViewMiddleware",
            "django.contrib.auth.middleware.AuthenticationMiddleware",
            "django.contrib.messages.middleware.MessageMiddleware",
            "axes.middleware.AxesMiddleware",
            "utils.middleware.RequestLoggingMiddleware",
            "django.middleware.clickjacking.XFrameOptionsMiddleware",
        ]
        
        print("\n建议的中间件顺序:")
        for i, mw in enumerate(recommended_order, 1):
            print(f"   {i}. {mw}")
        
        if current_middleware != recommended_order:
            print("\n⚠️ 中间件顺序需要调整")
            print("💡 建议将clickjacking中间件移到最后")
        else:
            print("\n✅ 中间件顺序正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查中间件顺序失败: {e}")
        return False

def create_simple_admin_test():
    """创建简单的admin测试"""
    print("\n🔧 创建简单admin测试")
    print("=" * 60)
    
    test_script = '''#!/usr/bin/env python
"""
简单的admin登录测试
"""
import os
import sys
import django

# 设置Django环境
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

from django.test import Client

def test_admin():
    client = Client()
    
    print("测试admin登录页面...")
    response = client.get('/admin/login/')
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ admin登录页面正常")
    else:
        print("❌ admin登录页面异常")
        print(f"响应内容: {response.content[:200]}")

if __name__ == '__main__':
    test_admin()
'''
    
    with open('debug/simple_admin_test.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 简单测试脚本已创建: debug/simple_admin_test.py")
    return True

def suggest_immediate_fixes():
    """建议立即修复方案"""
    print("\n💡 立即修复方案")
    print("=" * 60)
    
    fixes = [
        "1. 重启Django开发服务器",
        "2. 清除浏览器所有数据（缓存、cookies、会话）",
        "3. 使用无痕模式访问admin",
        "4. 检查是否有其他Django进程在运行",
        "5. 尝试不同的浏览器",
        "6. 检查防火墙和代理设置",
    ]
    
    for fix in fixes:
        print(f"   {fix}")
    
    print("\n🔧 代码修复建议:")
    print("   1. 调整中间件顺序（将clickjacking移到最后）")
    print("   2. 临时禁用自定义中间件进行测试")
    print("   3. 检查CSRF设置")
    
    print("\n📋 测试步骤:")
    print("   1. 运行: python debug/simple_admin_test.py")
    print("   2. 访问: http://127.0.0.1:8001/admin/login/")
    print("   3. 使用超级用户登录: admin / admin123!")

def main():
    """主修复函数"""
    print("🚀 开始修复admin登录问题")
    print("=" * 80)
    
    tests = [
        ("admin URL配置", check_admin_urls),
        ("中间件顺序", fix_middleware_order),
        ("中间件链测试", test_middleware_chain),
        ("admin登录直接测试", test_admin_login_direct),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            print("-" * 50)
    
    # 创建测试脚本
    create_simple_admin_test()
    
    # 建议修复方案
    suggest_immediate_fixes()
    
    print("\n" + "=" * 80)
    print("📋 修复总结")
    print("=" * 80)
    
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed >= 3:
        print("✅ 基本功能正常，问题可能是:")
        print("   1. 浏览器缓存或会话问题")
        print("   2. 中间件执行顺序问题")
        print("   3. CSRF token处理问题")
        
        print("\n🎯 立即尝试:")
        print("   1. 重启Django服务器")
        print("   2. 清除浏览器数据")
        print("   3. 使用无痕模式访问")
    else:
        print("⚠️ 发现配置问题，需要进一步调查")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
