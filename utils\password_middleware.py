"""
密码权限检查中间件
对密码相关API进行权限预检查
"""

import re
import json
from django.http import JsonResponse
from django.urls import resolve
from django.utils.deprecation import MiddlewareMixin
from .password_permissions import password_permission_service
import logging

logger = logging.getLogger(__name__)


class PasswordPermissionMiddleware(MiddlewareMixin):
    """密码权限检查中间件"""
    
    # 需要权限检查的URL模式
    PROTECTED_PATTERNS = [
        # 密码详情相关
        (r'^/api/passwords/([^/]+)/$', 'browse'),  # GET需要browse权限
        (r'^/api/passwords/([^/]+)/copy/$', 'read'),  # 复制需要read权限
        (r'^/api/passwords/([^/]+)/toggle-favorite/$', 'write'),  # 收藏需要write权限
        (r'^/api/passwords/([^/]+)/update-password/$', 'write'),  # 更新密码需要write权限
        (r'^/api/passwords/([^/]+)/update-info/$', 'write'),  # 更新信息需要write权限
        
        # 权限管理相关（需要admin权限）
        (r'^/api/passwords/([^/]+)/permissions/', 'admin'),
        (r'^/api/passwords/([^/]+)/ownership/transfer/$', 'admin'),
    ]
    
    # 不需要权限检查的路径
    EXCLUDED_PATHS = [
        '/api/passwords/',  # 密码列表（在视图中处理权限过滤）
        '/api/password-generator/',
        '/api/security-analysis/',
        '/api/user/accessible-passwords/',
    ]
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """处理请求前的权限检查"""
        
        # 跳过非API请求
        if not request.path.startswith('/api/'):
            return None
        
        # 跳过不需要权限检查的路径
        if any(request.path.startswith(path) for path in self.EXCLUDED_PATHS):
            return None
        
        # 跳过未认证用户（由其他中间件处理）
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return None
        
        # 检查是否为受保护的密码相关API
        password_id, required_permission = self._extract_password_info(request.path, request.method)
        
        if password_id and required_permission:
            # 进行权限检查
            try:
                has_permission = password_permission_service.has_permission(
                    password_id, request.user.id, required_permission
                )
                
                if not has_permission:
                    logger.warning(
                        f"中间件权限检查失败: user_id={request.user.id}, "
                        f"password_id={password_id}, required_permission={required_permission}, "
                        f"path={request.path}, method={request.method}"
                    )
                    
                    return JsonResponse({
                        'error': f'需要 {required_permission} 权限',
                        'code': 'INSUFFICIENT_PERMISSION',
                        'required_permission': required_permission
                    }, status=403)
                
            except Exception as e:
                logger.error(f"中间件权限检查异常: {e}")
                return JsonResponse({
                    'error': '权限检查失败',
                    'code': 'PERMISSION_CHECK_ERROR'
                }, status=500)
        
        return None
    
    def _extract_password_info(self, path, method):
        """
        从请求路径中提取密码ID和所需权限
        
        Args:
            path: 请求路径
            method: HTTP方法
            
        Returns:
            tuple: (password_id, required_permission) 或 (None, None)
        """
        
        for pattern, base_permission in self.PROTECTED_PATTERNS:
            match = re.match(pattern, path)
            if match:
                password_id_str = match.group(1)
                
                # 验证password_id格式（UUID）
                if not self._is_valid_uuid(password_id_str):
                    continue
                
                # 根据HTTP方法确定具体权限要求
                required_permission = self._get_permission_by_method(method, base_permission)
                
                return password_id_str, required_permission
        
        return None, None
    
    def _is_valid_uuid(self, uuid_string):
        """验证UUID格式"""
        try:
            import uuid
            uuid.UUID(uuid_string)
            return True
        except (ValueError, TypeError):
            return False
    
    def _get_permission_by_method(self, method, base_permission):
        """
        根据HTTP方法和基础权限确定具体权限要求
        
        Args:
            method: HTTP方法
            base_permission: 基础权限级别
            
        Returns:
            str: 具体权限要求
        """
        
        # 权限映射规则
        permission_mapping = {
            'GET': {
                'browse': 'browse',
                'read': 'read',
                'write': 'browse',  # GET操作通常只需要browse权限
                'admin': 'admin'
            },
            'POST': {
                'browse': 'write',  # POST操作通常需要write权限
                'read': 'read',
                'write': 'write',
                'admin': 'admin'
            },
            'PUT': {
                'browse': 'write',
                'read': 'write',
                'write': 'write',
                'admin': 'admin'
            },
            'PATCH': {
                'browse': 'write',
                'read': 'write',
                'write': 'write',
                'admin': 'admin'
            },
            'DELETE': {
                'browse': 'admin',  # DELETE操作通常需要admin权限
                'read': 'admin',
                'write': 'admin',
                'admin': 'admin'
            }
        }
        
        return permission_mapping.get(method, {}).get(base_permission, base_permission)
    
    def process_response(self, request, response):
        """处理响应（可用于日志记录等）"""
        
        # 记录权限相关的API访问
        if (hasattr(request, 'user') and 
            request.user.is_authenticated and 
            request.path.startswith('/api/passwords/')):
            
            try:
                # 提取密码ID用于日志
                password_id, _ = self._extract_password_info(request.path, request.method)
                
                if password_id and response.status_code < 400:
                    logger.info(
                        f"密码API访问成功: user_id={request.user.id}, "
                        f"password_id={password_id}, method={request.method}, "
                        f"path={request.path}, status={response.status_code}"
                    )
                elif password_id and response.status_code >= 400:
                    logger.warning(
                        f"密码API访问失败: user_id={request.user.id}, "
                        f"password_id={password_id}, method={request.method}, "
                        f"path={request.path}, status={response.status_code}"
                    )
                    
            except Exception as e:
                logger.error(f"记录API访问日志失败: {e}")
        
        return response


class PasswordAccessLoggingMiddleware(MiddlewareMixin):
    """密码访问日志中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_response(self, request, response):
        """记录密码访问日志"""
        
        # 只记录成功的密码相关API访问
        if (hasattr(request, 'user') and 
            request.user.is_authenticated and 
            request.path.startswith('/api/passwords/') and
            200 <= response.status_code < 300):
            
            try:
                # 异步记录访问日志（避免影响响应性能）
                self._log_password_access_async(request, response)
                
            except Exception as e:
                logger.error(f"记录密码访问日志失败: {e}")
        
        return response
    
    def _log_password_access_async(self, request, response):
        """异步记录密码访问日志"""
        
        # 这里可以使用Celery等异步任务队列
        # 目前简化为同步记录
        
        try:
            from apps.audit.models import PasswordAccessLog
            from apps.passwords.models import PasswordEntry
            
            # 提取密码ID
            password_id_match = re.search(r'/api/passwords/([^/]+)/', request.path)
            if password_id_match:
                password_id = password_id_match.group(1)
                
                # 验证密码是否存在
                try:
                    password_entry = PasswordEntry.objects.get(id=password_id)
                    
                    # 确定访问类型
                    access_type = self._determine_access_type(request.path, request.method)
                    
                    # 创建访问日志
                    PasswordAccessLog.objects.create(
                        user=request.user,
                        password_entry=password_entry,
                        access_type=access_type,
                        ip_address=self._get_client_ip(request),
                        user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    )
                    
                except PasswordEntry.DoesNotExist:
                    pass  # 密码不存在，不记录日志
                    
        except Exception as e:
            logger.error(f"异步记录密码访问日志失败: {e}")
    
    def _determine_access_type(self, path, method):
        """确定访问类型"""
        
        if '/copy/' in path:
            return 'copy'
        elif method == 'GET':
            return 'view'
        elif method in ['PUT', 'PATCH', 'POST']:
            return 'edit'
        else:
            return 'other'
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
