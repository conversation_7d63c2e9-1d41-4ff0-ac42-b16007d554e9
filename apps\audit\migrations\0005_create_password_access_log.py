# Generated manually to create password_access_logs table

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('passwords', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('audit', '0004_remove_loginlog_model'),
    ]

    operations = [
        migrations.CreateModel(
            name='PasswordAccessLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('access_type', models.CharField(choices=[('view', '查看'), ('copy', '复制'), ('edit', '编辑'), ('delete', '删除'), ('share', '分享'), ('export', '导出')], max_length=10, verbose_name='访问类型')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('access_method', models.CharField(blank=True, max_length=50, verbose_name='访问方式')),
                ('success', models.BooleanField(default=True, verbose_name='是否成功')),
                ('failure_reason', models.CharField(blank=True, max_length=200, verbose_name='失败原因')),
                ('additional_data', models.JSONField(blank=True, default=dict, verbose_name='附加数据')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('password_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='access_logs', to='passwords.passwordentry', verbose_name='密码条目')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '密码访问日志',
                'verbose_name_plural': '密码访问日志',
                'db_table': 'password_access_logs',
                'ordering': ['-created_at'],
            },
        ),
    ]
