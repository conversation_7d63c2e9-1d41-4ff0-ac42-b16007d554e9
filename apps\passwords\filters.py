"""
密码条目筛选器
使用django-filters简化筛选逻辑
"""

import django_filters
from django_filters import rest_framework as filters
from django.db.models import Q
from django.contrib.auth import get_user_model
from .models import PasswordEntry, Category

User = get_user_model()


class PasswordEntryFilter(filters.FilterSet):
    """密码条目筛选器"""

    # 基本搜索 - 支持多字段模糊搜索
    search = django_filters.CharFilter(
        method="filter_search", help_text="搜索标题、用户名、IP地址、URL、备注"
    )

    # 系统类型筛选
    system_type = django_filters.ChoiceFilter(
        choices=PasswordEntry.SYSTEM_TYPE_CHOICES, help_text="系统类型筛选"
    )

    # 操作系统类型筛选
    os_type = django_filters.ChoiceFilter(
        choices=PasswordEntry.OS_TYPE_CHOICES, help_text="操作系统类型筛选"
    )

    # 中间件类型筛选
    mdw_type = django_filters.ChoiceFilter(
        choices=PasswordEntry.MDW_TYPE_CHOICES, help_text="中间件类型筛选"
    )

    # 数据库类型筛选
    database_type = django_filters.ChoiceFilter(
        choices=PasswordEntry.DATABASE_TYPE_CHOICES, help_text="数据库类型筛选"
    )

    # 环境筛选
    environment = django_filters.ChoiceFilter(
        choices=[
            ("dev", "开发环境"),
            ("test", "测试环境"),
            ("staging", "预发布环境"),
            ("prod", "生产环境"),
        ],
        help_text="环境筛选",
    )

    # 项目名称模糊搜索
    project_name = django_filters.CharFilter(
        lookup_expr="icontains", help_text="项目名称模糊搜索"
    )

    # IP地址模糊搜索
    ip_address = django_filters.CharFilter(
        lookup_expr="icontains", help_text="IP地址模糊搜索"
    )

    # 负责人筛选（所有者）
    owner = django_filters.ModelChoiceFilter(
        queryset=User.objects.all(), help_text="负责人筛选"
    )

    # 负责人用户名搜索
    owner_username = django_filters.CharFilter(
        field_name="owner__username",
        lookup_expr="icontains",
        help_text="负责人用户名搜索",
    )

    # 收藏筛选
    is_favorite = django_filters.BooleanFilter(help_text="收藏筛选")

    # 置顶筛选
    is_pinned = django_filters.BooleanFilter(help_text="置顶筛选")

    # 分类筛选
    category = django_filters.ModelChoiceFilter(
        queryset=Category.objects.all(), help_text="分类筛选"
    )

    # 端口筛选
    port = django_filters.NumberFilter(help_text="端口精确匹配")
    port_gte = django_filters.NumberFilter(
        field_name="port", lookup_expr="gte", help_text="端口大于等于"
    )
    port_lte = django_filters.NumberFilter(
        field_name="port", lookup_expr="lte", help_text="端口小于等于"
    )

    # 数据库名称模糊搜索
    database_name = django_filters.CharFilter(
        lookup_expr="icontains", help_text="数据库名称模糊搜索"
    )

    # URL模糊搜索
    url = django_filters.CharFilter(lookup_expr="icontains", help_text="URL模糊搜索")

    # 用户名模糊搜索
    username = django_filters.CharFilter(
        lookup_expr="icontains", help_text="用户名模糊搜索"
    )

    # 标题模糊搜索
    title = django_filters.CharFilter(lookup_expr="icontains", help_text="标题模糊搜索")

    # 备注模糊搜索
    notes = django_filters.CharFilter(lookup_expr="icontains", help_text="备注模糊搜索")

    # 密码强度筛选
    strength = django_filters.ChoiceFilter(
        choices=PasswordEntry.STRENGTH_CHOICES, help_text="密码强度筛选"
    )

    # 日期范围筛选
    created_after = django_filters.DateFilter(
        field_name="created_at", lookup_expr="gte", help_text="创建时间大于等于"
    )
    created_before = django_filters.DateFilter(
        field_name="created_at", lookup_expr="lte", help_text="创建时间小于等于"
    )

    # 更新时间筛选
    updated_after = django_filters.DateFilter(
        field_name="updated_at", lookup_expr="gte", help_text="更新时间大于等于"
    )
    updated_before = django_filters.DateFilter(
        field_name="updated_at", lookup_expr="lte", help_text="更新时间小于等于"
    )

    # 过期时间筛选
    expires_after = django_filters.DateFilter(
        field_name="expires_at", lookup_expr="gte", help_text="过期时间大于等于"
    )
    expires_before = django_filters.DateFilter(
        field_name="expires_at", lookup_expr="lte", help_text="过期时间小于等于"
    )

    # 即将过期筛选（30天内）
    expires_soon = django_filters.BooleanFilter(
        method="filter_expires_soon", help_text="即将过期（30天内）"
    )

    # 弱密码筛选
    weak_passwords = django_filters.BooleanFilter(
        method="filter_weak_passwords", help_text="弱密码筛选"
    )

    class Meta:
        model = PasswordEntry
        fields = []  # 所有字段都在上面明确定义了

    def filter_search(self, queryset, name, value):
        """自定义搜索方法 - 支持多字段模糊搜索"""
        if value:
            return queryset.filter(
                Q(title__icontains=value)
                | Q(username__icontains=value)
                | Q(ip_address__icontains=value)
                | Q(url__icontains=value)
                | Q(notes__icontains=value)
                | Q(project_name__icontains=value)
                | Q(database_name__icontains=value)
            )
        return queryset

    def filter_expires_soon(self, queryset, name, value):
        """筛选即将过期的密码（30天内）"""
        if value:
            from django.utils import timezone
            from datetime import timedelta

            next_month = timezone.now() + timedelta(days=30)
            return queryset.filter(
                expires_at__isnull=False,
                expires_at__lte=next_month,
                expires_at__gte=timezone.now(),
            )
        return queryset

    def filter_weak_passwords(self, queryset, name, value):
        """筛选弱密码"""
        if value:
            return queryset.filter(strength__in=["weak", "very_weak"])
        return queryset


class PasswordEntrySystemFilter(PasswordEntryFilter):
    """密码条目系统筛选器 - 继承基础筛选器，添加系统管理特定筛选"""

    # 系统管理特定的筛选字段可以在这里添加
    # 例如：批量操作、系统状态等

    # 主机名筛选（兼容旧字段）
    hostname = django_filters.CharFilter(
        field_name="ip_address",  # 映射到ip_address字段
        lookup_expr="icontains",
        help_text="主机名/IP地址模糊搜索（兼容字段）",
    )

    # 负责人筛选（兼容旧字段）
    responsible_person = django_filters.CharFilter(
        field_name="created_by",  # 映射到created_by字段
        lookup_expr="icontains",
        help_text="负责人搜索（兼容字段）",
    )

    # 即将过期天数筛选
    days_to_expire = django_filters.NumberFilter(
        method="filter_days_to_expire", help_text="即将过期天数"
    )

    def filter_days_to_expire(self, queryset, name, value):
        """筛选指定天数内过期的密码"""
        if value:
            from django.utils import timezone
            from datetime import timedelta

            expire_date = timezone.now() + timedelta(days=int(value))
            return queryset.filter(
                expires_at__lte=expire_date, expires_at__gte=timezone.now()
            )
        return queryset
