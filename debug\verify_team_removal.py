#!/usr/bin/env python
"""
验证Team模型删除的完整性
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def verify_model_removal():
    """验证模型删除"""
    print("🔍 验证Team模型删除")
    print("=" * 60)
    
    try:
        # 尝试导入Team模型，应该失败
        try:
            from apps.users.models import Team
            print("   ❌ Team模型仍然存在")
            return False
        except ImportError:
            print("   ✅ Team模型已成功删除")
        
        # 检查User模型是否还有team字段
        from apps.users.models import User
        user_fields = [field.name for field in User._meta.fields]
        
        if 'team' in user_fields:
            print("   ❌ User模型仍然包含team字段")
            return False
        else:
            print("   ✅ User模型的team字段已删除")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证模型删除失败: {e}")
        return False

def verify_database_changes():
    """验证数据库变更"""
    print("\n🔍 验证数据库变更")
    print("=" * 60)
    
    try:
        from django.db import connection
        
        # 检查teams表是否已删除
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='teams';
            """)
            result = cursor.fetchone()
            
            if result:
                print("   ❌ teams表仍然存在")
                return False
            else:
                print("   ✅ teams表已成功删除")
        
        # 检查users表是否还有team_id字段
        with connection.cursor() as cursor:
            cursor.execute("PRAGMA table_info(users);")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            if 'team_id' in column_names:
                print("   ❌ users表仍然包含team_id字段")
                return False
            else:
                print("   ✅ users表的team_id字段已删除")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证数据库变更失败: {e}")
        return False

def verify_serializers():
    """验证序列化器"""
    print("\n🔍 验证序列化器")
    print("=" * 60)
    
    try:
        # 检查Team序列化器是否已删除
        try:
            from apps.users.serializers import TeamSerializer
            print("   ❌ TeamSerializer仍然存在")
            return False
        except ImportError:
            print("   ✅ TeamSerializer已删除")
        
        # 检查其他序列化器是否正常
        from apps.users.serializers import UserSerializer, DepartmentSerializer
        
        print("   ✅ UserSerializer正常")
        print("   ✅ DepartmentSerializer正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证序列化器失败: {e}")
        return False

def verify_views():
    """验证视图"""
    print("\n🔍 验证视图")
    print("=" * 60)
    
    try:
        # 检查Team视图是否已删除
        from apps.users import views
        
        team_views = [
            'TeamListCreateView',
            'TeamDetailView', 
            'TeamUsersView',
            'DepartmentTeamsView'
        ]
        
        for view_name in team_views:
            if hasattr(views, view_name):
                print(f"   ❌ {view_name}仍然存在")
                return False
            else:
                print(f"   ✅ {view_name}已删除")
        
        # 检查其他视图是否正常
        essential_views = [
            'UserListCreateView',
            'UserDetailView',
            'DepartmentListCreateView',
            'DepartmentDetailView'
        ]
        
        for view_name in essential_views:
            if hasattr(views, view_name):
                print(f"   ✅ {view_name}正常")
            else:
                print(f"   ❌ {view_name}缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证视图失败: {e}")
        return False

def verify_urls():
    """验证URL配置"""
    print("\n🔍 验证URL配置")
    print("=" * 60)
    
    try:
        from django.urls import reverse
        from django.urls.exceptions import NoReverseMatch
        
        # 检查Team相关URL是否已删除
        team_urls = [
            'team_list_create',
            'team_detail',
            'team_users',
            'department_teams'
        ]
        
        for url_name in team_urls:
            try:
                reverse(url_name)
                print(f"   ❌ URL {url_name}仍然存在")
                return False
            except NoReverseMatch:
                print(f"   ✅ URL {url_name}已删除")
        
        # 检查其他URL是否正常
        essential_urls = [
            'user_list_create',
            'department_list_create',
            'role_list_create'
        ]
        
        for url_name in essential_urls:
            try:
                url = reverse(url_name)
                print(f"   ✅ URL {url_name}正常: {url}")
            except NoReverseMatch:
                print(f"   ❌ URL {url_name}缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 验证URL配置失败: {e}")
        return False

def verify_api_endpoints():
    """验证API端点"""
    print("\n🔍 验证API端点")
    print("=" * 60)
    
    try:
        from django.test import Client
        
        client = Client()
        
        # 检查Team相关端点是否返回404
        team_endpoints = [
            '/api/auth/teams/',
            '/api/auth/teams/1/',
            '/api/auth/teams/1/users/',
            '/api/auth/departments/1/teams/',
        ]
        
        for endpoint in team_endpoints:
            response = client.get(endpoint)
            if response.status_code == 404:
                print(f"   ✅ {endpoint}: 404 (已删除)")
            else:
                print(f"   ❌ {endpoint}: {response.status_code} (仍然存在)")
                return False
        
        # 检查其他端点是否正常（应该返回401认证错误）
        essential_endpoints = [
            '/api/auth/users/',
            '/api/auth/departments/',
            '/api/auth/roles/',
        ]
        
        for endpoint in essential_endpoints:
            response = client.get(endpoint)
            if response.status_code in [200, 401, 403]:  # 正常的响应码
                print(f"   ✅ {endpoint}: {response.status_code} (正常)")
            else:
                print(f"   ⚠️ {endpoint}: {response.status_code} (异常)")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证API端点失败: {e}")
        return False

def check_code_references():
    """检查代码中是否还有Team引用"""
    print("\n🔍 检查代码中的Team引用")
    print("=" * 60)
    
    files_to_check = [
        "apps/users/models.py",
        "apps/users/serializers.py",
        "apps/users/views.py", 
        "apps/users/urls.py",
        "apps/users/admin.py",
    ]
    
    found_references = False
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找Team相关引用（排除注释）
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if 'Team' in line and not line.strip().startswith('#'):
                    print(f"   ⚠️ {file_path}:{i}: {line.strip()}")
                    found_references = True
    
    if not found_references:
        print("   ✅ 没有发现Team相关引用")
        return True
    else:
        print("   ❌ 发现Team相关引用，需要手动清理")
        return False

def main():
    """主验证函数"""
    print("🚀 开始验证Team模型删除的完整性")
    print("=" * 80)
    
    tests = [
        verify_model_removal,
        verify_database_changes,
        verify_serializers,
        verify_views,
        verify_urls,
        verify_api_endpoints,
        check_code_references,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            print("-" * 50)
    
    print("=" * 80)
    print(f"📊 验证结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 Team模型已完全删除！")
        print("\n✅ 删除完成的内容:")
        print("   1. Team模型类已删除")
        print("   2. User.team字段已删除")
        print("   3. teams数据库表已删除")
        print("   4. 所有Team序列化器已删除")
        print("   5. 所有Team视图已删除")
        print("   6. 所有Team URL配置已删除")
        print("   7. Team admin配置已删除")
        print("   8. 数据库迁移已应用")
        
        print("\n🎯 系统现在的状态:")
        print("   - 用户管理功能正常")
        print("   - 部门管理功能正常")
        print("   - 角色管理功能正常")
        print("   - 用户组管理功能正常")
        print("   - 所有API端点正常工作")
        
    else:
        print("⚠️ 部分验证失败，需要进一步检查")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
