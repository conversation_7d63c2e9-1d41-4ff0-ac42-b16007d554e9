from django.http import JsonResponse
from django.views import View
from django.urls import reverse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def api_documentation(request):
    """
    API文档视图 - 返回所有可用的API端点
    """
    api_endpoints = {
        "认证相关API": {
            "登录": "/api/auth/login/",
            "登出": "/api/auth/logout/",
            "刷新令牌": "/api/auth/token/refresh/",
            "修改密码": "/api/auth/change-password/",
            "重置密码": "/api/auth/reset-password/",
            "确认重置密码": "/api/auth/reset-password/confirm/",
            "MFA设置": "/api/auth/mfa/setup/",
            "MFA验证": "/api/auth/mfa/verify/",
            "MFA禁用": "/api/auth/mfa/disable/",
            "用户资料": "/api/auth/profile/",
        },
        "用户管理API": {
            "用户列表": "/api/auth/users/",
            "用户详情": "/api/auth/users/{id}/",
            "部门列表": "/api/auth/departments/",
            "部门详情": "/api/auth/departments/{id}/",
            "团队列表": "/api/auth/teams/",
            "团队详情": "/api/auth/teams/{id}/",
            "角色列表": "/api/auth/roles/",
            "角色详情": "/api/auth/roles/{id}/",
        },
        "密码管理API": {
            "密码条目列表": "/api/passwords/entries/",
            "密码条目详情": "/api/passwords/entries/{id}/",
            "复制密码": "/api/passwords/entries/{id}/copy/",
            "密码生成器": "/api/passwords/generator/",
            "分类列表": "/api/passwords/categories/",
            "分类详情": "/api/passwords/categories/{id}/",
            "标签列表": "/api/passwords/tags/",
            "标签详情": "/api/passwords/tags/{id}/",
            "自定义字段列表": "/api/passwords/entries/{entry_id}/custom-fields/",
            "自定义字段详情": "/api/passwords/custom-fields/{id}/",
            "附件列表": "/api/passwords/entries/{entry_id}/attachments/",
            "附件详情": "/api/passwords/attachments/{id}/",
            "安全分析": "/api/passwords/security-analysis/",
        },
        "密码分享API": {
            "分享链接列表": "/api/sharing/share-links/",
            "分享链接详情": "/api/sharing/share-links/{id}/",
            "分享链接访问": "/api/sharing/share/{token}/",
            "分享链接统计": "/api/sharing/share-links/{id}/stats/",
        },
        "审计日志API (🔒 管理员专用)": {
            "业务操作日志": "/api/audit/business-operations/ (🔒 管理员)",
            "密码访问日志": "/api/audit/password-access/ (🔒 管理员)",
            "安全事件列表": "/api/audit/security-events/ (🔒 管理员)",
            "安全事件详情": "/api/audit/security-events/{id}/ (🔒 管理员)",
            "模型变更日志": "/api/audit/model-changes/ (🔒 管理员)",
            "登录尝试记录": "/api/audit/login-attempts/ (🔒 管理员)",
            "用户审计轨迹": "/api/audit/users/{user_id}/audit-trail/ (🔒 管理员)",
            "审计统计": "/api/audit/statistics/ (🔒 管理员)",
            "操作日志(兼容)": "/api/audit/operation-logs/ (🔒 管理员)",
            "访问日志(兼容)": "/api/audit/access-logs/ (🔒 管理员)",
        },
        "系统管理API": {
            "系统设置列表": "/api/system/settings/",
            "系统设置详情": "/api/system/settings/{key}/",
            "批量更新设置": "/api/system/settings/batch/update/",
            "邮件模板列表": "/api/system/email-templates/",
            "邮件模板详情": "/api/system/email-templates/{id}/",
            "备份配置列表": "/api/system/backup-configs/",
            "备份配置详情": "/api/system/backup-configs/{id}/",
            "系统状态": "/api/system/status/",
            "系统维护": "/api/system/maintenance/",
        },
    }

    return Response(
        {
            "message": "密码管理系统API文档",
            "version": "1.0.0",
            "base_url": request.build_absolute_uri("/"),
            "endpoints": api_endpoints,
            "authentication": {
                "type": "JWT Token",
                "header": "Authorization: Bearer <token>",
                "login_endpoint": "/api/auth/login/",
            },
            "notes": [
                "所有API都需要认证，除了登录、重置密码确认和分享链接访问接口",
                "使用JWT Token进行认证，在请求头中添加 Authorization: Bearer <token>",
                "🔒 审计日志API需要管理员权限 (is_superuser=True)",
                "🔐 用户管理API需要相应的权限级别",
                "🌐 密码管理API需要认证用户权限",
                "分页参数: page, page_size",
                "搜索参数: search, ordering",
                "时间格式: ISO 8601 (YYYY-MM-DDTHH:MM:SSZ)",
                "权限不足时返回403 Forbidden错误",
            ],
        }
    )
