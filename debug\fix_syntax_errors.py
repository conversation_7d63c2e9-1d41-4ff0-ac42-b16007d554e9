#!/usr/bin/env python
"""
修复BusinessOperationLog语法错误
"""
import re

def fix_system_views():
    """修复system views中的语法错误"""
    file_path = 'apps/system/views.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复缺少右括号的问题
    # 查找 target_id=str(instance.id, 这种模式并修复
    content = re.sub(
        r'target_id=str\(instance\.id,',
        r'target_id=str(instance.id),',
        content
    )
    
    # 修复多余的括号和逗号
    content = re.sub(
        r'ip_address="127\.0\.0\.1",\s*extra_data=\{\}\s*\),\s*target_name=',
        r'target_name=',
        content
    )
    
    # 修复缺少字段的问题
    content = re.sub(
        r'(BusinessOperationLog\.objects\.create\(\s*user=request\.user,\s*target_type="system",\s*ip_address="127\.0\.0\.1",\s*extra_data=\{\}\s*\),)',
        r'BusinessOperationLog.objects.create(\n                    user=request.user,\n                    action_type=f"system_maintenance_{action}",\n                    target_type="system",\n                    target_id="maintenance",\n                    target_name=action,\n                    ip_address=self.get_client_ip(request),\n                    user_agent=request.META.get("HTTP_USER_AGENT", ""),\n                    extra_data={\n                        "action": action,\n                        "parameters": parameters,\n                        "result": result\n                    },\n                )',
        content,
        flags=re.DOTALL
    )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已修复apps/system/views.py中的语法错误")

if __name__ == '__main__':
    fix_system_views()
