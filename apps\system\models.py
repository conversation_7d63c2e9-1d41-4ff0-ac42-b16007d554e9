from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid

User = get_user_model()


class SystemSetting(models.Model):
    """系统设置模型"""

    SETTING_TYPES = [
        ("string", _("字符串")),
        ("integer", _("整数")),
        ("float", _("浮点数")),
        ("boolean", _("布尔值")),
        ("json", _("JSON")),
        ("text", _("文本")),
    ]

    CATEGORIES = [
        ("password_policy", _("密码策略")),
        ("security", _("安全设置")),
        ("sharing", _("分享设置")),
        ("backup", _("备份设置")),
        ("notification", _("通知设置")),
        ("ui", _("界面设置")),
        ("integration", _("集成设置")),
        ("audit", _("审计设置")),
        ("system", _("系统设置")),
    ]

    key = models.CharField(max_length=100, unique=True, verbose_name=_("设置键"))
    name = models.CharField(max_length=200, verbose_name=_("设置名称"))
    description = models.TextField(blank=True, verbose_name=_("设置描述"))

    category = models.CharField(
        max_length=20, choices=CATEGORIES, default="system", verbose_name=_("设置分类")
    )

    value_type = models.CharField(
        max_length=10, choices=SETTING_TYPES, default="string", verbose_name=_("值类型")
    )

    value = models.TextField(verbose_name=_("设置值"))
    default_value = models.TextField(verbose_name=_("默认值"))

    # 验证规则
    validation_rules = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_("验证规则"),
        help_text=_("JSON格式的验证规则"),
    )

    # 设置属性
    is_public = models.BooleanField(default=False, verbose_name=_("公开设置"))
    is_readonly = models.BooleanField(default=False, verbose_name=_("只读设置"))
    requires_restart = models.BooleanField(default=False, verbose_name=_("需要重启"))

    # 排序和分组
    order = models.IntegerField(default=0, verbose_name=_("排序"))
    group = models.CharField(max_length=50, blank=True, verbose_name=_("分组"))

    # 修改记录
    modified_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("修改者")
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("系统设置")
        verbose_name_plural = _("系统设置")
        db_table = "system_settings"
        ordering = ["category", "order", "name"]

    def __str__(self):
        return f"{self.name} ({self.key})"

    def get_typed_value(self):
        """获取类型化的值"""
        if self.value_type == "integer":
            return int(self.value)
        elif self.value_type == "float":
            return float(self.value)
        elif self.value_type == "boolean":
            return self.value.lower() in ("true", "1", "yes", "on")
        elif self.value_type == "json":
            import json

            return json.loads(self.value)
        else:
            return self.value

    def set_typed_value(self, value):
        """设置类型化的值"""
        if self.value_type == "json":
            import json

            self.value = json.dumps(value)
        else:
            self.value = str(value)

    def validate_value(self, value):
        """验证设置值"""
        if not self.validation_rules:
            return True, None

        try:
            rules = self.validation_rules

            # 检查必填
            if rules.get("required", False) and not value:
                return False, _("该设置为必填项")

            # 检查最小值/最大值
            if self.value_type in ["integer", "float"]:
                num_value = float(value) if self.value_type == "float" else int(value)

                if "min_value" in rules and num_value < rules["min_value"]:
                    return False, _(f"值不能小于 {rules['min_value']}")

                if "max_value" in rules and num_value > rules["max_value"]:
                    return False, _(f"值不能大于 {rules['max_value']}")

            # 检查字符串长度
            if self.value_type in ["string", "text"]:
                if "min_length" in rules and len(value) < rules["min_length"]:
                    return False, _(f"长度不能少于 {rules['min_length']} 个字符")

                if "max_length" in rules and len(value) > rules["max_length"]:
                    return False, _(f"长度不能超过 {rules['max_length']} 个字符")

            # 检查正则表达式
            if "pattern" in rules:
                import re

                if not re.match(rules["pattern"], str(value)):
                    return False, _("值格式不正确")

            # 检查选项列表
            if "choices" in rules:
                if value not in rules["choices"]:
                    return False, _(
                        f"值必须是以下选项之一: {', '.join(rules['choices'])}"
                    )

            return True, None

        except Exception as e:
            return False, str(e)


class BackupRecord(models.Model):
    """备份记录模型"""

    BACKUP_TYPES = [
        ("full", _("完整备份")),
        ("incremental", _("增量备份")),
        ("differential", _("差异备份")),
        ("manual", _("手动备份")),
    ]

    BACKUP_STATUS = [
        ("pending", _("等待中")),
        ("running", _("运行中")),
        ("completed", _("已完成")),
        ("failed", _("失败")),
        ("cancelled", _("已取消")),
    ]

    STORAGE_TYPES = [
        ("local", _("本地存储")),
        ("s3", _("Amazon S3")),
        ("azure", _("Azure Blob")),
        ("gcs", _("Google Cloud Storage")),
        ("ftp", _("FTP服务器")),
        ("sftp", _("SFTP服务器")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, verbose_name=_("备份名称"))
    description = models.TextField(blank=True, verbose_name=_("备份描述"))

    backup_type = models.CharField(
        max_length=15,
        choices=BACKUP_TYPES,
        default="manual",
        verbose_name=_("备份类型"),
    )

    status = models.CharField(
        max_length=10,
        choices=BACKUP_STATUS,
        default="pending",
        verbose_name=_("备份状态"),
    )

    # 备份内容
    include_passwords = models.BooleanField(default=True, verbose_name=_("包含密码"))
    include_categories = models.BooleanField(default=True, verbose_name=_("包含分类"))
    include_users = models.BooleanField(default=True, verbose_name=_("包含用户"))
    include_settings = models.BooleanField(default=True, verbose_name=_("包含设置"))
    include_logs = models.BooleanField(default=False, verbose_name=_("包含日志"))
    include_attachments = models.BooleanField(default=True, verbose_name=_("包含附件"))

    # 存储信息
    storage_type = models.CharField(
        max_length=10,
        choices=STORAGE_TYPES,
        default="local",
        verbose_name=_("存储类型"),
    )
    storage_path = models.CharField(max_length=500, verbose_name=_("存储路径"))
    file_name = models.CharField(max_length=255, blank=True, verbose_name=_("文件名"))
    file_size = models.BigIntegerField(
        null=True, blank=True, verbose_name=_("文件大小")
    )

    # 加密信息
    is_encrypted = models.BooleanField(default=True, verbose_name=_("是否加密"))
    encryption_method = models.CharField(
        max_length=50, blank=True, verbose_name=_("加密方法")
    )

    # 压缩信息
    is_compressed = models.BooleanField(default=True, verbose_name=_("是否压缩"))
    compression_method = models.CharField(
        max_length=20, blank=True, verbose_name=_("压缩方法")
    )
    compression_ratio = models.FloatField(
        null=True, blank=True, verbose_name=_("压缩比")
    )

    # 执行信息
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("创建者")
    )

    started_at = models.DateTimeField(null=True, blank=True, verbose_name=_("开始时间"))
    completed_at = models.DateTimeField(
        null=True, blank=True, verbose_name=_("完成时间")
    )

    # 错误信息
    error_message = models.TextField(blank=True, verbose_name=_("错误信息"))

    # 统计信息
    total_records = models.IntegerField(
        null=True, blank=True, verbose_name=_("总记录数")
    )
    processed_records = models.IntegerField(default=0, verbose_name=_("已处理记录数"))

    # 验证信息
    checksum = models.CharField(max_length=128, blank=True, verbose_name=_("校验和"))

    # 过期设置
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name=_("过期时间"))

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("备份记录")
        verbose_name_plural = _("备份记录")
        db_table = "backup_records"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.name} - {self.get_status_display()}"

    @property
    def duration(self):
        """计算备份持续时间"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None

    @property
    def progress_percentage(self):
        """计算备份进度百分比"""
        if self.total_records and self.total_records > 0:
            return (self.processed_records / self.total_records) * 100
        return 0

    @property
    def is_expired(self):
        """检查备份是否过期"""
        if self.expires_at:
            from django.utils import timezone

            return timezone.now() > self.expires_at
        return False


class RestoreRecord(models.Model):
    """恢复记录模型"""

    RESTORE_STATUS = [
        ("pending", _("等待中")),
        ("running", _("运行中")),
        ("completed", _("已完成")),
        ("failed", _("失败")),
        ("cancelled", _("已取消")),
    ]

    RESTORE_MODES = [
        ("full", _("完整恢复")),
        ("selective", _("选择性恢复")),
        ("merge", _("合并恢复")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, verbose_name=_("恢复名称"))
    description = models.TextField(blank=True, verbose_name=_("恢复描述"))

    backup_record = models.ForeignKey(
        BackupRecord,
        on_delete=models.CASCADE,
        related_name="restore_records",
        verbose_name=_("备份记录"),
    )

    status = models.CharField(
        max_length=10,
        choices=RESTORE_STATUS,
        default="pending",
        verbose_name=_("恢复状态"),
    )

    restore_mode = models.CharField(
        max_length=10, choices=RESTORE_MODES, default="full", verbose_name=_("恢复模式")
    )

    # 恢复选项
    restore_passwords = models.BooleanField(default=True, verbose_name=_("恢复密码"))
    restore_categories = models.BooleanField(default=True, verbose_name=_("恢复分类"))
    restore_users = models.BooleanField(default=False, verbose_name=_("恢复用户"))
    restore_settings = models.BooleanField(default=False, verbose_name=_("恢复设置"))
    restore_attachments = models.BooleanField(default=True, verbose_name=_("恢复附件"))

    # 冲突处理
    overwrite_existing = models.BooleanField(
        default=False, verbose_name=_("覆盖现有数据")
    )
    skip_duplicates = models.BooleanField(default=True, verbose_name=_("跳过重复项"))

    # 执行信息
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("创建者")
    )

    started_at = models.DateTimeField(null=True, blank=True, verbose_name=_("开始时间"))
    completed_at = models.DateTimeField(
        null=True, blank=True, verbose_name=_("完成时间")
    )

    # 错误信息
    error_message = models.TextField(blank=True, verbose_name=_("错误信息"))

    # 统计信息
    total_records = models.IntegerField(
        null=True, blank=True, verbose_name=_("总记录数")
    )
    processed_records = models.IntegerField(default=0, verbose_name=_("已处理记录数"))
    created_records = models.IntegerField(default=0, verbose_name=_("新建记录数"))
    updated_records = models.IntegerField(default=0, verbose_name=_("更新记录数"))
    skipped_records = models.IntegerField(default=0, verbose_name=_("跳过记录数"))
    failed_records = models.IntegerField(default=0, verbose_name=_("失败记录数"))

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("恢复记录")
        verbose_name_plural = _("恢复记录")
        db_table = "restore_records"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.name} - {self.get_status_display()}"

    @property
    def duration(self):
        """计算恢复持续时间"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None

    @property
    def progress_percentage(self):
        """计算恢复进度百分比"""
        if self.total_records and self.total_records > 0:
            return (self.processed_records / self.total_records) * 100
        return 0


class SystemNotification(models.Model):
    """系统通知模型"""

    NOTIFICATION_TYPES = [
        ("info", _("信息")),
        ("warning", _("警告")),
        ("error", _("错误")),
        ("success", _("成功")),
    ]

    PRIORITY_LEVELS = [
        ("low", _("低")),
        ("normal", _("普通")),
        ("high", _("高")),
        ("urgent", _("紧急")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200, verbose_name=_("通知标题"))
    message = models.TextField(verbose_name=_("通知内容"))

    notification_type = models.CharField(
        max_length=10,
        choices=NOTIFICATION_TYPES,
        default="info",
        verbose_name=_("通知类型"),
    )

    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_LEVELS,
        default="normal",
        verbose_name=_("优先级"),
    )

    # 目标用户
    target_users = models.ManyToManyField(
        User,
        through="NotificationRecipient",
        related_name="notifications",
        verbose_name=_("目标用户"),
    )

    # 通知设置
    is_global = models.BooleanField(default=False, verbose_name=_("全局通知"))
    is_persistent = models.BooleanField(default=False, verbose_name=_("持久通知"))
    auto_dismiss = models.BooleanField(default=True, verbose_name=_("自动消失"))
    dismiss_after = models.IntegerField(
        null=True, blank=True, verbose_name=_("消失时间(秒)")
    )

    # 操作按钮
    action_url = models.URLField(blank=True, verbose_name=_("操作链接"))
    action_text = models.CharField(
        max_length=50, blank=True, verbose_name=_("操作文本")
    )

    # 发送者
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_notifications",
        verbose_name=_("创建者"),
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name=_("过期时间"))

    class Meta:
        verbose_name = _("系统通知")
        verbose_name_plural = _("系统通知")
        db_table = "system_notifications"
        ordering = ["-created_at"]

    def __str__(self):
        return self.title

    @property
    def is_expired(self):
        """检查通知是否过期"""
        if self.expires_at:
            from django.utils import timezone

            return timezone.now() > self.expires_at
        return False


class NotificationRecipient(models.Model):
    """通知接收者模型"""

    notification = models.ForeignKey(
        SystemNotification, on_delete=models.CASCADE, verbose_name=_("通知")
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_("用户"))

    # 状态
    is_read = models.BooleanField(default=False, verbose_name=_("已读"))
    is_dismissed = models.BooleanField(default=False, verbose_name=_("已消除"))

    # 时间戳
    read_at = models.DateTimeField(null=True, blank=True, verbose_name=_("阅读时间"))
    dismissed_at = models.DateTimeField(
        null=True, blank=True, verbose_name=_("消除时间")
    )

    class Meta:
        verbose_name = _("通知接收者")
        verbose_name_plural = _("通知接收者")
        db_table = "notification_recipients"
        unique_together = ["notification", "user"]

    def __str__(self):
        return f"{self.notification.title} - {self.user.username}"

    def mark_as_read(self):
        """标记为已读"""
        if not self.is_read:
            from django.utils import timezone

            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=["is_read", "read_at"])

    def dismiss(self):
        """消除通知"""
        if not self.is_dismissed:
            from django.utils import timezone

            self.is_dismissed = True
            self.dismissed_at = timezone.now()
            self.save(update_fields=["is_dismissed", "dismissed_at"])


class EmailTemplate(models.Model):
    """邮件模板模型"""

    TEMPLATE_TYPES = [
        ("welcome", _("欢迎邮件")),
        ("password_reset", _("密码重置")),
        ("account_locked", _("账户锁定")),
        ("security_alert", _("安全警报")),
        ("backup_notification", _("备份通知")),
        ("system_maintenance", _("系统维护")),
        ("custom", _("自定义")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200, verbose_name=_("模板名称"))
    template_type = models.CharField(
        max_length=20, choices=TEMPLATE_TYPES, verbose_name=_("模板类型")
    )
    subject = models.CharField(max_length=200, verbose_name=_("邮件主题"))
    content = models.TextField(verbose_name=_("邮件内容"))

    # 模板设置
    is_active = models.BooleanField(default=True, verbose_name=_("是否启用"))
    is_default = models.BooleanField(default=False, verbose_name=_("默认模板"))

    # 创建信息
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_("创建者")
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("邮件模板")
        verbose_name_plural = _("邮件模板")
        db_table = "email_templates"
        ordering = ["template_type", "name"]
        unique_together = ["template_type", "is_default"]

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"


# 为了兼容views.py中的导入，添加别名
BackupConfig = BackupRecord
