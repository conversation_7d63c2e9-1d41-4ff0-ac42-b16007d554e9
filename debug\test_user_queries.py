#!/usr/bin/env python
"""
测试用户查询是否正常
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def test_user_queries():
    """测试各种用户查询"""
    from apps.users.models import User
    
    try:
        print("测试1: 基本查询")
        users = User.objects.all()
        print(f"✅ 成功，找到 {users.count()} 个用户")
        
        print("测试2: select_related查询")
        users = User.objects.select_related('department', 'role')
        print(f"✅ 成功，查询集创建正常")
        
        print("测试3: 获取第一个用户")
        user = users.first()
        if user:
            print(f"✅ 成功，用户: {user.username}")
            print(f"   部门: {user.department}")
            print(f"   角色: {user.role}")
        
        print("测试4: 过滤查询")
        active_users = User.objects.filter(is_active=True)
        print(f"✅ 成功，活跃用户: {active_users.count()}")
        
        print("测试5: 复杂查询")
        users_with_dept = User.objects.filter(department__isnull=False)
        print(f"✅ 成功，有部门的用户: {users_with_dept.count()}")
        
        print("\n🎉 所有查询测试通过！")
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_user_queries()
