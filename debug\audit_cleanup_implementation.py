#!/usr/bin/env python
"""
审计模型清理实施脚本
执行LoginLog模型的安全清理和数据迁移
"""
import os
import sys
import django
from datetime import datetime
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def backup_loginlog_data():
    """备份LoginLog数据到JSON文件"""
    print("📦 开始备份LoginLog数据...")
    
    try:
        from apps.audit.models import LoginLog
        
        # 获取所有LoginLog数据
        login_logs = LoginLog.objects.all().values(
            'id', 'user_id', 'username', 'ip_address', 'user_agent',
            'login_type', 'result', 'failure_reason', 'created_at'
        )
        
        # 转换为可序列化的格式
        backup_data = []
        for log in login_logs:
            log_data = dict(log)
            log_data['created_at'] = log_data['created_at'].isoformat()
            backup_data.append(log_data)
        
        # 保存到文件
        backup_filename = f"loginlog_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        backup_path = os.path.join('debug', backup_filename)
        
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 备份完成: {len(backup_data)} 条记录")
        print(f"   备份文件: {backup_path}")
        
        return backup_path, len(backup_data)
        
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return None, 0


def migrate_loginlog_to_axes():
    """将LoginLog数据迁移到django-axes模型"""
    print("🔄 开始数据迁移...")
    
    try:
        from apps.audit.models import LoginLog
        from axes.models import AccessAttempt, AccessFailureLog, AccessLog
        from django.utils import timezone
        
        success_count = 0
        failure_count = 0
        
        # 迁移成功登录记录
        success_logs = LoginLog.objects.filter(result='success')
        print(f"   迁移成功登录记录: {success_logs.count()} 条")
        
        for log in success_logs:
            try:
                AccessLog.objects.get_or_create(
                    username=log.username,
                    ip_address=log.ip_address,
                    user_agent=log.user_agent or '',
                    attempt_time=log.created_at,
                    defaults={
                        'logout_time': None,
                        'path_info': '/api/auth/login/',
                    }
                )
                success_count += 1
            except Exception as e:
                print(f"     ⚠️ 迁移成功记录失败: {e}")
        
        # 迁移失败登录记录
        failure_logs = LoginLog.objects.exclude(result='success')
        print(f"   迁移失败登录记录: {failure_logs.count()} 条")
        
        for log in failure_logs:
            try:
                AccessFailureLog.objects.get_or_create(
                    username=log.username,
                    ip_address=log.ip_address,
                    user_agent=log.user_agent or '',
                    attempt_time=log.created_at,
                    defaults={
                        'locked_out': False,
                        'path_info': '/api/auth/login/',
                    }
                )
                failure_count += 1
            except Exception as e:
                print(f"     ⚠️ 迁移失败记录失败: {e}")
        
        print(f"✅ 数据迁移完成:")
        print(f"   成功记录: {success_count} 条")
        print(f"   失败记录: {failure_count} 条")
        
        return success_count + failure_count
        
    except Exception as e:
        print(f"❌ 数据迁移失败: {e}")
        return 0


def verify_migration():
    """验证迁移结果"""
    print("🔍 验证迁移结果...")
    
    try:
        from apps.audit.models import LoginLog
        from axes.models import AccessAttempt, AccessFailureLog, AccessLog
        
        # 统计原始数据
        original_total = LoginLog.objects.count()
        original_success = LoginLog.objects.filter(result='success').count()
        original_failure = LoginLog.objects.exclude(result='success').count()
        
        # 统计迁移后数据
        migrated_success = AccessLog.objects.count()
        migrated_failure = AccessFailureLog.objects.count()
        migrated_total = migrated_success + migrated_failure
        
        print(f"📊 数据对比:")
        print(f"   原始数据总计: {original_total}")
        print(f"   - 成功记录: {original_success}")
        print(f"   - 失败记录: {original_failure}")
        print(f"   迁移后总计: {migrated_total}")
        print(f"   - AccessLog: {migrated_success}")
        print(f"   - AccessFailureLog: {migrated_failure}")
        
        # 验证完整性
        if migrated_total >= original_total * 0.95:  # 允许5%的差异
            print("✅ 数据迁移验证通过")
            return True
        else:
            print("❌ 数据迁移验证失败，数据量差异过大")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程失败: {e}")
        return False


def cleanup_loginlog_references():
    """清理代码中的LoginLog引用"""
    print("🧹 清理代码引用...")
    
    # 这里只是示例，实际需要手动清理代码
    cleanup_tasks = [
        "移除 apps/users/views.py 中的 LoginLog 导入和使用",
        "移除 apps/audit/models.py 中的 LoginLog 模型定义",
        "移除相关的序列化器和视图",
        "更新测试用例",
    ]
    
    print("📋 需要手动执行的清理任务:")
    for i, task in enumerate(cleanup_tasks, 1):
        print(f"   {i}. {task}")
    
    return True


def test_axes_functionality():
    """测试django-axes功能是否正常"""
    print("🧪 测试django-axes功能...")
    
    try:
        from axes.models import AccessAttempt, AccessFailureLog, AccessLog
        
        # 检查模型是否可用
        attempt_count = AccessAttempt.objects.count()
        failure_count = AccessFailureLog.objects.count()
        success_count = AccessLog.objects.count()
        
        print(f"📊 Django-Axes数据统计:")
        print(f"   AccessAttempt: {attempt_count} 条")
        print(f"   AccessFailureLog: {failure_count} 条")
        print(f"   AccessLog: {success_count} 条")
        
        # 测试API端点
        import requests
        base_url = 'http://localhost:8001'
        
        endpoints = [
            '/api/audit/axes/access-attempts/',
            '/api/audit/axes/access-failures/',
            '/api/audit/axes/access-logs/',
        ]
        
        api_success = 0
        for endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                if response.status_code in [200, 401, 403]:
                    print(f"   ✅ {endpoint} - 状态码: {response.status_code}")
                    api_success += 1
                else:
                    print(f"   ❌ {endpoint} - 状态码: {response.status_code}")
            except Exception as e:
                print(f"   ❌ {endpoint} - 错误: {e}")
        
        if api_success == len(endpoints):
            print("✅ Django-Axes功能测试通过")
            return True
        else:
            print("⚠️ 部分API端点测试失败")
            return False
            
    except Exception as e:
        print(f"❌ Django-Axes功能测试失败: {e}")
        return False


def generate_cleanup_report():
    """生成清理报告"""
    print("📄 生成清理报告...")
    
    try:
        from apps.audit.models import LoginLog
        from axes.models import AccessAttempt, AccessFailureLog, AccessLog
        
        report = {
            "cleanup_time": datetime.now().isoformat(),
            "original_data": {
                "loginlog_total": LoginLog.objects.count(),
                "loginlog_success": LoginLog.objects.filter(result='success').count(),
                "loginlog_failure": LoginLog.objects.exclude(result='success').count(),
            },
            "migrated_data": {
                "access_log": AccessLog.objects.count(),
                "access_failure_log": AccessFailureLog.objects.count(),
                "access_attempt": AccessAttempt.objects.count(),
            },
            "cleanup_status": "completed",
            "next_steps": [
                "手动清理代码中的LoginLog引用",
                "创建数据库迁移删除LoginLog表",
                "运行完整测试套件",
                "部署到生产环境",
            ]
        }
        
        report_filename = f"cleanup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path = os.path.join('debug', report_filename)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 清理报告已生成: {report_path}")
        return report_path
        
    except Exception as e:
        print(f"❌ 生成清理报告失败: {e}")
        return None


def main():
    """主清理流程"""
    print("🚀 开始审计模型清理流程")
    print("=" * 60)
    
    # 执行清理步骤
    steps = [
        ("数据备份", backup_loginlog_data),
        ("数据迁移", migrate_loginlog_to_axes),
        ("迁移验证", verify_migration),
        ("功能测试", test_axes_functionality),
        ("代码清理指导", cleanup_loginlog_references),
        ("生成报告", generate_cleanup_report),
    ]
    
    results = {}
    
    for step_name, step_func in steps:
        print(f"\n📋 执行步骤: {step_name}")
        print("-" * 40)
        
        try:
            result = step_func()
            results[step_name] = result
            
            if result:
                print(f"✅ {step_name} 完成")
            else:
                print(f"⚠️ {step_name} 有问题，但继续执行")
                
        except Exception as e:
            print(f"❌ {step_name} 失败: {e}")
            results[step_name] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 清理流程总结:")
    
    success_count = sum(1 for result in results.values() if result)
    total_count = len(results)
    
    for step_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {step_name}")
    
    print(f"\n🎯 完成度: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 审计模型清理流程完成！")
        print("\n📋 后续手动步骤:")
        print("   1. 清理代码中的LoginLog引用")
        print("   2. 创建删除LoginLog表的迁移")
        print("   3. 运行完整测试")
        print("   4. 部署到生产环境")
    else:
        print("⚠️ 清理流程部分完成，请检查失败的步骤")
    
    return success_count == total_count


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
