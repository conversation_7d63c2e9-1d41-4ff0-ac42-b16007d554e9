#!/usr/bin/env python
"""
修复apps/sharing/views.py中的模型引用
"""
import re

def fix_sharing_views():
    """修复sharing views中的模型引用"""
    file_path = 'apps/sharing/views.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换OperationLog为BusinessOperationLog
    content = re.sub(r'\bOperationLog\b', 'BusinessOperationLog', content)
    
    # 替换AccessLog为PasswordAccessLog
    content = re.sub(r'\bAccessLog\b', 'PasswordAccessLog', content)
    
    # 修复字段名
    content = re.sub(r'description=', r'extra_data={"description": ', content)
    content = re.sub(r'extra_data=\{"description": f"([^"]+)"\}', r'extra_data={"description": f"\1"}', content)
    
    # 添加缺失的字段
    # 为BusinessOperationLog添加target_name字段
    content = re.sub(
        r'(BusinessOperationLog\.objects\.create\([^}]+?)(\s+extra_data=)',
        r'\1\n            target_name="",\2',
        content,
        flags=re.DOTALL
    )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已修复apps/sharing/views.py中的模型引用")

if __name__ == '__main__':
    fix_sharing_views()
