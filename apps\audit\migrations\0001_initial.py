# Generated by Django 5.2.4 on 2025-08-04 15:12

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('passwords', '0020_alter_grouppermission_permission'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LoginLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(max_length=150)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('login_type', models.CharField(default='password', max_length=20)),
                ('result', models.CharField(default='success', max_length=20)),
                ('failure_reason', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'login_logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BusinessOperationLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('action_type', models.CharField(choices=[('user_login', '用户登录'), ('user_logout', '用户登出'), ('user_create', '创建用户'), ('user_update', '更新用户'), ('user_delete', '删除用户'), ('password_change', '密码修改'), ('password_view', '查看密码'), ('password_copy', '复制密码'), ('password_export', '导出密码'), ('password_import', '导入密码'), ('password_share', '分享密码'), ('share_revoke', '撤销分享'), ('onetime_link_create', '创建一次性链接'), ('onetime_link_access', '访问一次性链接'), ('system_backup', '系统备份'), ('system_restore', '系统恢复'), ('system_setting_update', '系统设置更新'), ('mfa_enable', '启用多因素认证'), ('mfa_disable', '禁用多因素认证'), ('bulk_operation', '批量操作'), ('data_export', '数据导出')], max_length=30, verbose_name='操作类型')),
                ('result', models.CharField(choices=[('success', '成功'), ('failed', '失败'), ('warning', '警告')], default='success', max_length=10, verbose_name='操作结果')),
                ('description', models.TextField(blank=True, verbose_name='操作描述')),
                ('target_type', models.CharField(blank=True, max_length=50, verbose_name='目标类型')),
                ('target_id', models.CharField(blank=True, max_length=100, verbose_name='目标ID')),
                ('target_name', models.CharField(blank=True, max_length=200, verbose_name='目标名称')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('request_method', models.CharField(blank=True, max_length=10, verbose_name='请求方法')),
                ('request_path', models.CharField(blank=True, max_length=500, verbose_name='请求路径')),
                ('extra_data', models.JSONField(blank=True, default=dict, verbose_name='额外数据')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='操作时间')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='操作用户')),
            ],
            options={
                'verbose_name': '业务操作日志',
                'verbose_name_plural': '业务操作日志',
                'db_table': 'business_operation_logs',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', '-created_at'], name='business_op_user_id_35b1c3_idx'), models.Index(fields=['action_type', '-created_at'], name='business_op_action__8e2b82_idx'), models.Index(fields=['ip_address', '-created_at'], name='business_op_ip_addr_3ef02f_idx'), models.Index(fields=['-created_at'], name='business_op_created_78d1e5_idx')],
            },
        ),
        migrations.CreateModel(
            name='PasswordAccessLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('access_type', models.CharField(choices=[('view', '查看'), ('copy_username', '复制用户名'), ('copy_password', '复制密码'), ('copy_url', '复制网址'), ('edit', '编辑'), ('delete', '删除'), ('share', '分享'), ('export', '导出')], max_length=20, verbose_name='访问类型')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('access_source', models.CharField(blank=True, help_text='direct, share, onetime_link', max_length=50, verbose_name='访问来源')),
                ('source_id', models.CharField(blank=True, max_length=100, verbose_name='来源ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='访问时间')),
                ('password_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='access_logs', to='passwords.passwordentry', verbose_name='密码条目')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='访问用户')),
            ],
            options={
                'verbose_name': '密码访问日志',
                'verbose_name_plural': '密码访问日志',
                'db_table': 'password_access_logs',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['password_entry', '-created_at'], name='password_ac_passwor_637cfe_idx'), models.Index(fields=['user', '-created_at'], name='password_ac_user_id_0bf4ba_idx'), models.Index(fields=['access_type', '-created_at'], name='password_ac_access__5e9639_idx'), models.Index(fields=['-created_at'], name='password_ac_created_b16217_idx')],
            },
        ),
        migrations.CreateModel(
            name='SecurityEvent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('event_type', models.CharField(choices=[('suspicious_login', '可疑登录'), ('multiple_failed_logins', '多次登录失败'), ('account_locked', '账户锁定'), ('password_breach', '密码泄露'), ('unusual_access', '异常访问'), ('data_export', '数据导出'), ('bulk_operation', '批量操作'), ('privilege_escalation', '权限提升'), ('unauthorized_access', '未授权访问')], max_length=30, verbose_name='事件类型')),
                ('severity', models.CharField(choices=[('low', '低'), ('medium', '中'), ('high', '高'), ('critical', '严重')], default='medium', max_length=10, verbose_name='严重程度')),
                ('status', models.CharField(choices=[('open', '开放'), ('investigating', '调查中'), ('resolved', '已解决'), ('false_positive', '误报')], default='open', max_length=15, verbose_name='状态')),
                ('title', models.CharField(max_length=200, verbose_name='事件标题')),
                ('description', models.TextField(verbose_name='事件描述')),
                ('affected_resources', models.JSONField(blank=True, default=list, verbose_name='受影响资源')),
                ('event_data', models.JSONField(blank=True, default=dict, verbose_name='事件数据')),
                ('resolution_notes', models.TextField(blank=True, verbose_name='解决备注')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='解决时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_security_events', to=settings.AUTH_USER_MODEL, verbose_name='分配给')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='相关用户')),
            ],
            options={
                'verbose_name': '安全事件',
                'verbose_name_plural': '安全事件',
                'db_table': 'security_events',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['event_type', '-created_at'], name='security_ev_event_t_33b3e5_idx'), models.Index(fields=['severity', '-created_at'], name='security_ev_severit_768a26_idx'), models.Index(fields=['status', '-created_at'], name='security_ev_status_0ded42_idx'), models.Index(fields=['user', '-created_at'], name='security_ev_user_id_c77a93_idx'), models.Index(fields=['-created_at'], name='security_ev_created_baa0cf_idx')],
            },
        ),
    ]
