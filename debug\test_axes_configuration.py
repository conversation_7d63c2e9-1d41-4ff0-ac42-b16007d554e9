#!/usr/bin/env python
"""
测试修复后的django-axes配置
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def test_axes_configuration():
    """测试axes配置"""
    print("🔧 测试django-axes配置")
    print("=" * 60)
    
    try:
        from django.conf import settings
        from axes.models import AccessAttempt, AccessFailureLog, AccessLog
        
        print("📋 当前axes配置:")
        axes_settings = [
            'AXES_FAILURE_LIMIT',
            'AXES_COOLOFF_TIME', 
            'AXES_RESET_ON_SUCCESS',
            'AXES_LOCKOUT_TEMPLATE',
            'AXES_LOCKOUT_CALLABLE',
            'AXES_ENABLE_ADMIN',
            'AXES_VERBOSE',
            'AXES_LOCKOUT_PARAMETERS',
            'AXES_COOLOFF_TIME_UNIT',
        ]
        
        for setting in axes_settings:
            if hasattr(settings, setting):
                value = getattr(settings, setting)
                print(f"   ✅ {setting}: {value}")
            else:
                print(f"   ❌ {setting}: 未设置")
        
        print("\n📋 检查过时设置是否已移除:")
        deprecated_settings = [
            'AXES_LOCK_OUT_BY_COMBINATION_USER_AND_IP',
            'AXES_ONLY_USER_FAILURES',
            'AXES_LOCK_OUT_BY_USER_OR_IP',
            'AXES_USE_USER_AGENT',
        ]
        
        for setting in deprecated_settings:
            if hasattr(settings, setting):
                print(f"   ⚠️ {setting}: 仍然存在 (应该移除)")
            else:
                print(f"   ✅ {setting}: 已移除")
        
        print("\n📊 axes模型状态:")
        try:
            attempt_count = AccessAttempt.objects.count()
            failure_count = AccessFailureLog.objects.count()
            access_count = AccessLog.objects.count()
            
            print(f"   - AccessAttempt: {attempt_count} 条记录")
            print(f"   - AccessFailureLog: {failure_count} 条记录")
            print(f"   - AccessLog: {access_count} 条记录")
            
        except Exception as e:
            print(f"   ❌ 模型查询失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ axes配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_check():
    """测试系统检查是否还有警告"""
    print("\n🔍 运行系统检查")
    print("=" * 60)
    
    try:
        from django.core.management import call_command
        from io import StringIO
        import sys
        
        # 捕获系统检查输出
        old_stdout = sys.stdout
        old_stderr = sys.stderr
        stdout_capture = StringIO()
        stderr_capture = StringIO()
        
        try:
            sys.stdout = stdout_capture
            sys.stderr = stderr_capture
            
            # 运行系统检查
            call_command('check', verbosity=2)
            
        finally:
            sys.stdout = old_stdout
            sys.stderr = old_stderr
        
        stdout_output = stdout_capture.getvalue()
        stderr_output = stderr_capture.getvalue()
        
        print("📋 系统检查结果:")
        
        # 检查是否还有axes相关警告
        if "axes.W004" in stdout_output or "axes.W004" in stderr_output:
            print("   ⚠️ 仍然存在axes配置警告")
            print("   输出:", stdout_output)
            print("   错误:", stderr_output)
        else:
            print("   ✅ 没有发现axes配置警告")
        
        if "System check identified no issues" in stdout_output:
            print("   ✅ 系统检查通过，没有发现问题")
        elif "WARNINGS:" in stdout_output or "WARNINGS:" in stderr_output:
            print("   ⚠️ 系统检查发现警告")
            # 提取警告信息
            lines = (stdout_output + stderr_output).split('\n')
            for line in lines:
                if 'WARNING' in line or 'axes.W004' in line or '?:' in line:
                    print(f"     {line}")
        else:
            print("   ✅ 系统检查完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_axes_functionality():
    """测试axes功能是否正常工作"""
    print("\n🧪 测试axes功能")
    print("=" * 60)
    
    try:
        from django.test import Client
        from django.urls import reverse
        import json
        
        client = Client()
        
        # 测试登录端点
        login_url = '/api/auth/login/'
        
        print(f"📡 测试登录端点: {login_url}")
        
        # 尝试错误登录
        wrong_credentials = {
            'username': 'nonexistent_user',
            'password': 'wrong_password'
        }
        
        print("   🔍 尝试错误登录...")
        response = client.post(
            login_url,
            data=json.dumps(wrong_credentials),
            content_type='application/json'
        )
        
        print(f"   📊 响应状态码: {response.status_code}")
        
        if response.status_code in [400, 401]:
            print("   ✅ 错误登录被正确拒绝")
        else:
            print(f"   ⚠️ 意外的响应状态码: {response.status_code}")
        
        # 检查是否记录了失败尝试
        from axes.models import AccessAttempt
        
        recent_attempts = AccessAttempt.objects.filter(
            username='nonexistent_user'
        ).order_by('-attempt_time')[:1]
        
        if recent_attempts:
            attempt = recent_attempts[0]
            print(f"   ✅ axes记录了失败尝试: {attempt.username} from {attempt.ip_address}")
        else:
            print("   ⚠️ axes没有记录失败尝试")
        
        return True
        
    except Exception as e:
        print(f"❌ axes功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修复后的django-axes配置")
    
    tests = [
        test_axes_configuration,
        test_system_check,
        test_axes_functionality,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            print("-" * 50)
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 django-axes配置修复成功！")
        print("\n💡 修复总结:")
        print("  1. ✅ 移除了所有过时的配置设置")
        print("  2. ✅ 使用新的 AXES_LOCKOUT_PARAMETERS 配置")
        print("  3. ✅ 应用了未完成的数据库迁移")
        print("  4. ✅ 系统检查不再显示警告")
        print("  5. ✅ axes功能正常工作")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
