from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import permissions, status
from django.contrib.auth import get_user_model

User = get_user_model()


class MenuListView(APIView):
    """获取用户菜单列表"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """根据用户权限返回菜单列表"""
        user = request.user

        # 基础菜单结构
        menus = [
            {
                "path": "/dashboard",
                "name": "Dashboard",
                "component": "BasicLayout",
                "meta": {
                    "title": "概览",
                    "icon": "lucide:layout-dashboard",
                    "order": 1,
                },
                "children": [
                    {
                        "path": "/dashboard/analytics",
                        "name": "DashboardAnalytics",
                        "component": "/views/dashboard/analytics/index.vue",
                        "meta": {
                            "title": "运维仪表盘",
                            "icon": "lucide:bar-chart-3",
                        },
                    }
                ],
            },
            {
                "path": "/passwords",
                "name": "Passwords",
                "component": "BasicLayout",
                "meta": {
                    "title": "密码管理",
                    "icon": "lucide:key",
                    "order": 2,
                },
                "children": [
                    {
                        "path": "/passwords/list",
                        "name": "PasswordsList",
                        "component": "/views/passwords/index.vue",
                        "meta": {
                            "title": "所有密码",
                            "icon": "lucide:list",
                        },
                    }
                ],
            },
        ]

        # 添加审计报表菜单（需要系统读取权限）
        if self._has_permission(user, "system:read"):
            audit_menu = {
                "path": "/audit",
                "name": "Audit",
                "component": "BasicLayout",
                "meta": {
                    "title": "审计报表",
                    "icon": "lucide:file-text",
                    "order": 5,
                },
                "children": [
                    {
                        "path": "/audit/dashboard",
                        "name": "AuditDashboard",
                        "component": "/views/audit/index.vue",
                        "meta": {
                            "title": "审计概览",
                            "icon": "lucide:bar-chart-3",
                            "authority": ["system:read"],
                        },
                    },
                    {
                        "path": "/audit/operation-logs",
                        "name": "OperationLogs",
                        "component": "/views/audit/operation-logs.vue",
                        "meta": {
                            "title": "操作日志",
                            "icon": "lucide:list",
                            "authority": ["system:read"],
                        },
                    },
                    {
                        "path": "/audit/access-logs",
                        "name": "AccessLogs",
                        "component": "/views/audit/access-logs.vue",
                        "meta": {
                            "title": "访问日志",
                            "icon": "lucide:eye",
                            "authority": ["system:read"],
                        },
                    },
                    {
                        "path": "/audit/security-events",
                        "name": "SecurityEvents",
                        "component": "/views/audit/security-events.vue",
                        "meta": {
                            "title": "安全事件",
                            "icon": "lucide:shield-alert",
                            "authority": ["system:read"],
                        },
                    },
                ],
            }
            menus.append(audit_menu)

        # 根据用户角色添加系统管理菜单
        if self._has_admin_permission(user):
            system_menu = {
                "path": "/system",
                "name": "System",
                "component": "BasicLayout",
                "meta": {
                    "title": "系统管理",
                    "icon": "lucide:settings",
                    "order": 10,
                },
                "children": [],
            }

            # 用户管理
            if self._has_permission(user, "user:read"):
                system_menu["children"].append(
                    {
                        "path": "/users",
                        "name": "Users",
                        "component": "/views/users/index.vue",
                        "meta": {
                            "title": "用户管理",
                            "icon": "lucide:users",
                            "authority": ["user:read"],
                        },
                    }
                )

            # 角色管理
            if self._has_permission(user, "user:read"):
                system_menu["children"].append(
                    {
                        "path": "/roles",
                        "name": "Roles",
                        "component": "/views/roles/index.vue",
                        "meta": {
                            "title": "角色管理",
                            "icon": "lucide:shield",
                            "authority": ["user:read"],
                        },
                    }
                )

            # 分类管理
            if self._has_permission(user, "category:read"):
                system_menu["children"].append(
                    {
                        "path": "/categories",
                        "name": "Categories",
                        "component": "/views/categories/index.vue",
                        "meta": {
                            "title": "分类管理",
                            "icon": "lucide:folder",
                            "authority": ["category:read"],
                        },
                    }
                )

            # 部门管理
            if self._has_permission(user, "user:read"):
                system_menu["children"].append(
                    {
                        "path": "/departments",
                        "name": "Departments",
                        "component": "/views/departments/index.vue",
                        "meta": {
                            "title": "部门管理",
                            "icon": "lucide:building",
                            "authority": ["user:read"],
                        },
                    }
                )

            # 系统设置
            if self._has_permission(user, "system:update"):
                system_menu["children"].append(
                    {
                        "path": "/system/settings",
                        "name": "SystemSettings",
                        "component": "/views/system/settings/index.vue",
                        "meta": {
                            "title": "系统设置",
                            "icon": "lucide:settings",
                            "authority": ["system:update"],
                        },
                    }
                )

            if system_menu["children"]:
                menus.append(system_menu)

        return Response(menus, status=status.HTTP_200_OK)

    def _has_admin_permission(self, user):
        """检查用户是否有管理员权限"""
        return (
            user.is_staff
            or user.is_superuser
            or (user.roles.filter(name__in=["admin", "manager"]).exists())
        )

    def _has_permission(self, user, permission_code):
        """检查用户是否有特定权限"""
        # 超级用户拥有所有权限
        if user.is_superuser:
            return True

        # 根据角色检查权限
        user_roles = user.roles.all()
        for role in user_roles:
            if role.name == "admin":
                return True
            elif role.name == "manager" and permission_code.startswith(
                ("password:", "category:")
            ):
                return True
            elif role.name == "user" and permission_code in [
                "password:read",
                "password:create",
                "password:update",
            ]:
                return True

        return False
