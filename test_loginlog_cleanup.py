#!/usr/bin/env python
"""
测试LoginLog模型清理效果
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def test_loginlog_cleanup():
    """测试LoginLog模型清理效果"""
    print("🧪 测试LoginLog模型清理效果...")
    
    success_count = 0
    total_tests = 4
    
    # 测试1：检查LoginLog模型是否已删除
    print("\n1. 检查LoginLog模型是否已删除...")
    try:
        from apps.audit.models import LoginLog
        print("❌ LoginLog模型仍然存在")
    except ImportError:
        print("✅ LoginLog模型已成功删除")
        success_count += 1
    
    # 测试2：检查别名是否已清理
    print("\n2. 检查别名是否已清理...")
    try:
        from apps.audit.models import OperationLog, AccessLog
        print("❌ 别名仍然存在")
    except ImportError:
        print("✅ 别名已成功清理")
        success_count += 1
    
    # 测试3：检查新模型是否正常工作
    print("\n3. 检查新模型是否正常工作...")
    try:
        from apps.audit.models import BusinessOperationLog, PasswordAccessLog
        
        # 测试创建记录
        log_count_before = BusinessOperationLog.objects.count()
        
        BusinessOperationLog.objects.create(
            action_type="test_cleanup",
            target_type="test",
            target_id="test_id",
            target_name="test_name",
            ip_address="127.0.0.1",
            extra_data={"test": "cleanup"}
        )
        
        log_count_after = BusinessOperationLog.objects.count()
        
        if log_count_after > log_count_before:
            print("✅ BusinessOperationLog模型正常工作")
            success_count += 1
        else:
            print("❌ BusinessOperationLog模型创建记录失败")
            
    except Exception as e:
        print(f"❌ 新模型测试失败: {e}")
    
    # 测试4：检查django-axes是否正常工作
    print("\n4. 检查django-axes是否正常工作...")
    try:
        from axes.models import AccessAttempt, AccessFailureLog, AccessLog
        
        attempt_count = AccessAttempt.objects.count()
        failure_count = AccessFailureLog.objects.count()
        success_count_axes = AccessLog.objects.count()
        
        print(f"   AccessAttempt: {attempt_count} 条记录")
        print(f"   AccessFailureLog: {failure_count} 条记录")
        print(f"   AccessLog: {success_count_axes} 条记录")
        print("✅ Django-Axes模型正常工作")
        success_count += 1
        
    except Exception as e:
        print(f"❌ Django-Axes测试失败: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 LoginLog模型清理成功！")
        print("\n📋 清理总结:")
        print("   • LoginLog模型已删除")
        print("   • 别名已清理")
        print("   • BusinessOperationLog正常工作")
        print("   • Django-Axes正常工作")
        print("   • 功能完全由django-axes替代")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试LoginLog模型清理效果")
    print("=" * 50)
    
    try:
        success = test_loginlog_cleanup()
        
        if success:
            print("\n🎯 任务1完成总结:")
            print("   ✅ LoginLog模型已完全删除")
            print("   ✅ 所有代码引用已清理")
            print("   ✅ 功能完全由django-axes替代")
            print("   ✅ 审计功能完整保留")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程出现异常: {e}")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
