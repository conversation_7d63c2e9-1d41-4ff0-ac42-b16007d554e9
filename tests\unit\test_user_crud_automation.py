"""
用户管理CRUD API自动化测试套件
包含完整的API测试、性能测试、边界测试等
"""

import json
import time
from django.test import TestCase, TransactionTestCase
from django.urls import reverse
from django.contrib.auth.models import Group
from rest_framework.test import APIClient, APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from django.db import transaction
from django.core.management import call_command
from django.test.utils import override_settings
from unittest.mock import patch
import threading
from concurrent.futures import ThreadPoolExecutor
import random
import string

from apps.users.models import User, Department, Team, Role
from apps.audit.models import OperationLog


class BaseAPITestCase(APITestCase):
    """基础API测试类"""

    def setUp(self):
        """测试前置设置"""
        self.client = APIClient()

        # 创建测试用户
        self.admin_user = User.objects.create_user(
            username="admin_test",
            email="<EMAIL>",
            password="AdminTest123!",
            is_staff=True,
            is_superuser=True,
        )

        self.manager_user = User.objects.create_user(
            username="manager_test",
            email="<EMAIL>",
            password="ManagerTest123!",
            is_staff=True,
        )

        self.normal_user = User.objects.create_user(
            username="user_test", email="<EMAIL>", password="UserTest123!"
        )

        # 创建测试组织架构
        self.department = Department.objects.create(
            name="测试部门", description="自动化测试部门"
        )

        self.team = Team.objects.create(
            name="测试团队", description="自动化测试团队", department=self.department
        )

        self.role = Role.objects.create(
            name="test_role", description="测试角色", permissions=["test_permission"]
        )

        self.group = Group.objects.create(name="测试组")

    def get_jwt_token(self, user):
        """获取JWT Token"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)

    def authenticate_as_admin(self):
        """以管理员身份认证"""
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")
        return token

    def authenticate_as_manager(self):
        """以管理员身份认证"""
        token = self.get_jwt_token(self.manager_user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")
        return token

    def authenticate_as_user(self):
        """以普通用户身份认证"""
        token = self.get_jwt_token(self.normal_user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")
        return token

    def clear_authentication(self):
        """清除认证"""
        self.client.credentials()


class UserCRUDTestCase(BaseAPITestCase):
    """用户CRUD操作测试"""

    def test_user_list_api(self):
        """测试用户列表API"""
        self.authenticate_as_admin()

        url = reverse("user-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertIn("data", response.data)
        self.assertIn("total", response.data)
        self.assertIn("page", response.data)

        # 验证返回的用户数量
        self.assertGreaterEqual(len(response.data["data"]), 3)

    def test_user_create_api(self):
        """测试用户创建API"""
        self.authenticate_as_admin()

        url = reverse("user-list")
        data = {
            "username": "new_test_user",
            "email": "<EMAIL>",
            "name": "新测试用户",
            "password": "NewUserTest123!",
            "password_confirm": "NewUserTest123!",
            "phone": "13800138000",
            "department": self.department.id,
            "team": self.team.id,
            "role": self.role.id,
            "groups": [self.group.id],
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data["success"])
        self.assertEqual(response.data["data"]["username"], "new_test_user")

        # 验证用户确实被创建
        user = User.objects.get(username="new_test_user")
        self.assertEqual(user.email, "<EMAIL>")
        self.assertEqual(user.department, self.department)
        self.assertEqual(user.team, self.team)
        self.assertEqual(user.role, self.role)
        self.assertTrue(user.groups.filter(id=self.group.id).exists())

    def test_user_detail_api(self):
        """测试用户详情API"""
        self.authenticate_as_admin()

        url = reverse("user-detail", kwargs={"pk": self.normal_user.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertEqual(response.data["data"]["username"], "user_test")

    def test_user_update_api(self):
        """测试用户更新API"""
        self.authenticate_as_admin()

        url = reverse("user-detail", kwargs={"pk": self.normal_user.id})
        data = {
            "name": "更新后的用户名",
            "phone": "13800138001",
            "department": self.department.id,
            "groups": [self.group.id],
        }

        response = self.client.patch(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertEqual(response.data["data"]["name"], "更新后的用户名")

        # 验证数据库中的数据确实被更新
        self.normal_user.refresh_from_db()
        self.assertEqual(self.normal_user.name, "更新后的用户名")
        self.assertEqual(self.normal_user.phone, "13800138001")

    def test_user_delete_api(self):
        """测试用户删除API（软删除）"""
        self.authenticate_as_admin()

        # 创建一个临时用户用于删除测试
        temp_user = User.objects.create_user(
            username="temp_user", email="<EMAIL>", password="TempTest123!"
        )

        url = reverse("user-detail", kwargs={"pk": temp_user.id})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])

        # 验证用户被软删除
        temp_user.refresh_from_db()
        self.assertFalse(temp_user.is_active)

    def test_user_reset_password_api(self):
        """测试重置密码API"""
        self.authenticate_as_admin()

        url = reverse("user-reset-password", kwargs={"pk": self.normal_user.id})
        data = {"new_password": "NewPassword123!"}

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])

        # 验证密码确实被更改
        self.normal_user.refresh_from_db()
        self.assertTrue(self.normal_user.check_password("NewPassword123!"))

    def test_user_toggle_active_api(self):
        """测试切换用户激活状态API"""
        self.authenticate_as_admin()

        url = reverse("user-toggle-active", kwargs={"pk": self.normal_user.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])

        # 验证状态确实被切换
        self.normal_user.refresh_from_db()
        self.assertFalse(self.normal_user.is_active)


class PermissionTestCase(BaseAPITestCase):
    """权限控制测试"""

    def test_admin_has_full_access(self):
        """测试管理员拥有完整访问权限"""
        self.authenticate_as_admin()

        # 测试所有主要操作
        operations = [
            ("GET", reverse("user-list")),
            ("GET", reverse("department-list")),
            ("GET", reverse("team-list")),
            ("GET", reverse("role-list")),
            ("GET", reverse("group-list")),
        ]

        for method, url in operations:
            response = self.client.generic(method, url)
            self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_normal_user_limited_access(self):
        """测试普通用户的有限访问权限"""
        self.authenticate_as_user()

        # 普通用户只能查看自己的信息
        url = reverse("user-detail", kwargs={"pk": self.normal_user.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # 普通用户不能查看其他用户信息
        url = reverse("user-detail", kwargs={"pk": self.admin_user.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # 普通用户不能创建用户
        url = reverse("user-list")
        data = {"username": "test", "email": "<EMAIL>"}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_unauthenticated_access_denied(self):
        """测试未认证用户被拒绝访问"""
        self.clear_authentication()

        url = reverse("user-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class ValidationTestCase(BaseAPITestCase):
    """数据验证测试"""

    def test_user_creation_validation(self):
        """测试用户创建时的数据验证"""
        self.authenticate_as_admin()

        url = reverse("user-list")

        # 测试必填字段验证
        response = self.client.post(url, {}, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data["success"])
        self.assertIn("errors", response.data)

        # 测试用户名重复验证
        data = {
            "username": "admin_test",  # 重复的用户名
            "email": "<EMAIL>",
            "password": "Test123!",
            "password_confirm": "Test123!",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # 测试邮箱重复验证
        data = {
            "username": "new_user",
            "email": "<EMAIL>",  # 重复的邮箱
            "password": "Test123!",
            "password_confirm": "Test123!",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # 测试密码确认验证
        data = {
            "username": "new_user",
            "email": "<EMAIL>",
            "password": "Test123!",
            "password_confirm": "Different123!",  # 不匹配的密码确认
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_email_format_validation(self):
        """测试邮箱格式验证"""
        self.authenticate_as_admin()

        url = reverse("user-list")
        data = {
            "username": "test_user",
            "email": "invalid-email",  # 无效的邮箱格式
            "password": "Test123!",
            "password_confirm": "Test123!",
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("errors", response.data)

    def test_password_strength_validation(self):
        """测试密码强度验证"""
        self.authenticate_as_admin()

        url = reverse("user-list")

        # 测试弱密码
        weak_passwords = ["123", "password", "12345678"]

        for weak_password in weak_passwords:
            data = {
                "username": f"test_user_{weak_password}",
                "email": f"test_{weak_password}@test.com",
                "password": weak_password,
                "password_confirm": weak_password,
            }

            response = self.client.post(url, data, format="json")
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class SearchAndFilterTestCase(BaseAPITestCase):
    """搜索和过滤功能测试"""

    def setUp(self):
        super().setUp()

        # 创建更多测试数据
        for i in range(10):
            User.objects.create_user(
                username=f"search_user_{i}",
                email=f"search{i}@test.com",
                password="SearchTest123!",
                name=f"搜索用户{i}",
                department=self.department if i % 2 == 0 else None,
                team=self.team if i % 3 == 0 else None,
                is_active=i % 4 != 0,  # 部分用户设为非活跃
            )

    def test_search_functionality(self):
        """测试搜索功能"""
        self.authenticate_as_admin()

        url = reverse("user-list")

        # 按用户名搜索
        response = self.client.get(f"{url}?search=search_user")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreater(len(response.data["data"]), 0)

        # 按邮箱搜索
        response = self.client.get(f"{url}?search=<EMAIL>")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreater(len(response.data["data"]), 0)

        # 按姓名搜索
        response = self.client.get(f"{url}?search=搜索用户")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreater(len(response.data["data"]), 0)

    def test_filter_functionality(self):
        """测试过滤功能"""
        self.authenticate_as_admin()

        url = reverse("user-list")

        # 按部门过滤
        response = self.client.get(f"{url}?department={self.department.id}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        for user in response.data["data"]:
            if user["department"]:
                self.assertEqual(user["department"], self.department.id)

        # 按激活状态过滤
        response = self.client.get(f"{url}?is_active=true")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        for user in response.data["data"]:
            self.assertTrue(user["is_active"])

        response = self.client.get(f"{url}?is_active=false")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        for user in response.data["data"]:
            self.assertFalse(user["is_active"])

    def test_pagination_functionality(self):
        """测试分页功能"""
        self.authenticate_as_admin()

        url = reverse("user-list")

        # 测试第一页
        response = self.client.get(f"{url}?page=1&page_size=5")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertLessEqual(len(response.data["data"]), 5)
        self.assertEqual(response.data["page"], 1)
        self.assertEqual(response.data["page_size"], 5)

        # 测试第二页
        response = self.client.get(f"{url}?page=2&page_size=5")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["page"], 2)

    def test_ordering_functionality(self):
        """测试排序功能"""
        self.authenticate_as_admin()

        url = reverse("user-list")

        # 按用户名升序
        response = self.client.get(f"{url}?ordering=username")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        usernames = [user["username"] for user in response.data["data"]]
        self.assertEqual(usernames, sorted(usernames))

        # 按用户名降序
        response = self.client.get(f"{url}?ordering=-username")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        usernames = [user["username"] for user in response.data["data"]]
        self.assertEqual(usernames, sorted(usernames, reverse=True))


class DepartmentCRUDTestCase(BaseAPITestCase):
    """部门CRUD操作测试"""

    def test_department_list_api(self):
        """测试部门列表API"""
        self.authenticate_as_admin()

        url = reverse("department-list")
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertIn("data", response.data)

    def test_department_create_api(self):
        """测试部门创建API"""
        self.authenticate_as_admin()

        url = reverse("department-list")
        data = {"name": "新测试部门", "description": "这是一个新的测试部门"}

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data["success"])
        self.assertEqual(response.data["data"]["name"], "新测试部门")

        # 验证部门确实被创建
        department = Department.objects.get(name="新测试部门")
        self.assertEqual(department.description, "这是一个新的测试部门")

    def test_department_hierarchy(self):
        """测试部门层级关系"""
        self.authenticate_as_admin()

        # 创建父部门
        parent_dept = Department.objects.create(name="父部门", description="父级部门")

        # 创建子部门
        url = reverse("department-list")
        data = {"name": "子部门", "description": "子级部门", "parent": parent_dept.id}

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["data"]["parent"], parent_dept.id)

        # 获取父部门详情，检查子部门
        url = reverse("department-detail", kwargs={"pk": parent_dept.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(len(response.data["data"]["children"]) > 0)

    def test_department_users_api(self):
        """测试获取部门用户API"""
        self.authenticate_as_admin()

        # 将用户分配到部门
        self.normal_user.department = self.department
        self.normal_user.save()

        url = reverse("department-users", kwargs={"pk": self.department.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertGreater(len(response.data["data"]), 0)

    def test_department_teams_api(self):
        """测试获取部门团队API"""
        self.authenticate_as_admin()

        url = reverse("department-teams", kwargs={"pk": self.department.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertGreater(len(response.data["data"]), 0)


class TeamCRUDTestCase(BaseAPITestCase):
    """团队CRUD操作测试"""

    def test_team_create_api(self):
        """测试团队创建API"""
        self.authenticate_as_admin()

        url = reverse("team-list")
        data = {
            "name": "新测试团队",
            "description": "这是一个新的测试团队",
            "department": self.department.id,
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data["success"])
        self.assertEqual(response.data["data"]["name"], "新测试团队")
        self.assertEqual(response.data["data"]["department"], self.department.id)

    def test_team_users_api(self):
        """测试获取团队用户API"""
        self.authenticate_as_admin()

        # 将用户分配到团队
        self.normal_user.team = self.team
        self.normal_user.save()

        url = reverse("team-users", kwargs={"pk": self.team.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertGreater(len(response.data["data"]), 0)


class RoleCRUDTestCase(BaseAPITestCase):
    """角色CRUD操作测试"""

    def test_role_create_api(self):
        """测试角色创建API"""
        self.authenticate_as_admin()

        url = reverse("role-list")
        data = {
            "name": "new_test_role",
            "description": "新测试角色",
            "permissions": ["test_perm1", "test_perm2"],
        }

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data["success"])
        self.assertEqual(response.data["data"]["name"], "new_test_role")

    def test_role_users_api(self):
        """测试获取角色用户API"""
        self.authenticate_as_admin()

        # 将用户分配到角色
        self.normal_user.role = self.role
        self.normal_user.save()

        url = reverse("role-users", kwargs={"pk": self.role.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertGreater(len(response.data["data"]), 0)


class GroupCRUDTestCase(BaseAPITestCase):
    """用户组CRUD操作测试"""

    def test_group_create_api(self):
        """测试用户组创建API"""
        self.authenticate_as_admin()

        url = reverse("group-list")
        data = {"name": "新测试组", "users": [self.normal_user.id]}

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data["success"])
        self.assertEqual(response.data["data"]["name"], "新测试组")

    def test_group_add_users_api(self):
        """测试向用户组添加用户API"""
        self.authenticate_as_admin()

        # 创建一个新用户
        new_user = User.objects.create_user(
            username="group_test_user",
            email="<EMAIL>",
            password="GroupTest123!",
        )

        url = reverse("group-add-users", kwargs={"pk": self.group.id})
        data = {"user_ids": [new_user.id]}

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertEqual(response.data["data"]["added_count"], 1)

        # 验证用户确实被添加到组
        self.assertTrue(self.group.user_set.filter(id=new_user.id).exists())

    def test_group_remove_users_api(self):
        """测试从用户组移除用户API"""
        self.authenticate_as_admin()

        # 先将用户添加到组
        self.group.user_set.add(self.normal_user)

        url = reverse("group-remove-users", kwargs={"pk": self.group.id})
        data = {"user_ids": [self.normal_user.id]}

        response = self.client.post(url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertEqual(response.data["data"]["removed_count"], 1)

        # 验证用户确实被从组中移除
        self.assertFalse(self.group.user_set.filter(id=self.normal_user.id).exists())

    def test_group_users_api(self):
        """测试获取用户组成员API"""
        self.authenticate_as_admin()

        # 将用户添加到组
        self.group.user_set.add(self.normal_user)

        url = reverse("group-users", kwargs={"pk": self.group.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data["success"])
        self.assertGreater(len(response.data["data"]), 0)


class AuditLogTestCase(BaseAPITestCase):
    """审计日志测试"""

    def test_operation_logging(self):
        """测试操作日志记录"""
        self.authenticate_as_admin()

        # 记录操作前的日志数量
        initial_log_count = OperationLog.objects.count()

        # 执行一个创建操作
        url = reverse("user-list")
        data = {
            "username": "log_test_user",
            "email": "<EMAIL>",
            "name": "日志测试用户",
            "password": "LogTest123!",
            "password_confirm": "LogTest123!",
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # 检查是否记录了操作日志
        final_log_count = OperationLog.objects.count()
        self.assertEqual(final_log_count, initial_log_count + 1)

        # 检查日志内容
        latest_log = OperationLog.objects.latest("created_at")
        self.assertEqual(latest_log.user, self.admin_user)
        self.assertEqual(latest_log.action_type, "user_create")
        self.assertEqual(latest_log.target_type, "user")
        self.assertIsNotNone(latest_log.ip_address)
        self.assertIsNotNone(latest_log.user_agent)

    def test_multiple_operations_logging(self):
        """测试多个操作的日志记录"""
        self.authenticate_as_admin()

        initial_log_count = OperationLog.objects.count()

        # 创建用户
        user_data = {
            "username": "multi_log_user",
            "email": "<EMAIL>",
            "password": "MultiLog123!",
            "password_confirm": "MultiLog123!",
        }
        response = self.client.post(reverse("user-list"), user_data, format="json")
        user_id = response.data["data"]["id"]

        # 更新用户
        update_data = {"name": "更新后的用户"}
        self.client.patch(
            reverse("user-detail", kwargs={"pk": user_id}), update_data, format="json"
        )

        # 删除用户
        self.client.delete(reverse("user-detail", kwargs={"pk": user_id}))

        # 检查日志数量
        final_log_count = OperationLog.objects.count()
        self.assertEqual(final_log_count, initial_log_count + 3)

        # 检查日志类型
        recent_logs = OperationLog.objects.order_by("-created_at")[:3]
        action_types = [log.action_type for log in recent_logs]
        self.assertIn("user_create", action_types)
        self.assertIn("user_update", action_types)
        self.assertIn("user_delete", action_types)


class PerformanceTestCase(BaseAPITestCase):
    """性能测试"""

    def test_large_dataset_performance(self):
        """测试大数据集性能"""
        self.authenticate_as_admin()

        # 创建大量测试数据
        users_to_create = 100
        for i in range(users_to_create):
            User.objects.create_user(
                username=f"perf_user_{i}",
                email=f"perf{i}@test.com",
                password="PerfTest123!",
                name=f"性能测试用户{i}",
            )

        # 测试列表API性能
        start_time = time.time()
        url = reverse("user-list")
        response = self.client.get(f"{url}?page_size=50")
        end_time = time.time()

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertLess(end_time - start_time, 2.0)  # 应该在2秒内完成

        # 测试搜索性能
        start_time = time.time()
        response = self.client.get(f"{url}?search=perf_user&page_size=20")
        end_time = time.time()

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertLess(end_time - start_time, 1.0)  # 搜索应该在1秒内完成

    def test_pagination_performance(self):
        """测试分页性能"""
        self.authenticate_as_admin()

        url = reverse("user-list")

        # 测试不同页大小的性能
        page_sizes = [10, 20, 50, 100]

        for page_size in page_sizes:
            start_time = time.time()
            response = self.client.get(f"{url}?page_size={page_size}")
            end_time = time.time()

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertLess(end_time - start_time, 1.0)  # 每个请求应该在1秒内完成


class ConcurrencyTestCase(TransactionTestCase):
    """并发测试"""

    def setUp(self):
        """测试前置设置"""
        self.admin_user = User.objects.create_user(
            username="admin_concurrent",
            email="<EMAIL>",
            password="AdminConcurrent123!",
            is_staff=True,
        )

        self.department = Department.objects.create(
            name="并发测试部门", description="并发测试部门"
        )

    def get_jwt_token(self, user):
        """获取JWT Token"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)

    def test_concurrent_user_creation(self):
        """测试并发用户创建"""
        token = self.get_jwt_token(self.admin_user)

        def create_user(index):
            client = APIClient()
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")

            data = {
                "username": f"concurrent_user_{index}",
                "email": f"concurrent{index}@test.com",
                "password": "ConcurrentTest123!",
                "password_confirm": "ConcurrentTest123!",
            }

            url = reverse("user-list")
            response = client.post(url, data, format="json")
            return response.status_code == status.HTTP_201_CREATED

        # 使用线程池并发创建用户
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_user, i) for i in range(10)]
            results = [future.result() for future in futures]

        # 检查所有创建操作是否成功
        success_count = sum(results)
        self.assertGreater(success_count, 8)  # 至少80%的操作应该成功

    def test_concurrent_department_access(self):
        """测试并发部门访问"""
        token = self.get_jwt_token(self.admin_user)

        def access_department():
            client = APIClient()
            client.credentials(HTTP_AUTHORIZATION=f"Bearer {token}")

            url = reverse("department-detail", kwargs={"pk": self.department.id})
            response = client.get(url)
            return response.status_code == status.HTTP_200_OK

        # 并发访问部门详情
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(access_department) for _ in range(20)]
            results = [future.result() for future in futures]

        # 所有访问都应该成功
        self.assertEqual(sum(results), 20)


class EdgeCaseTestCase(BaseAPITestCase):
    """边界条件测试"""

    def test_invalid_user_id(self):
        """测试无效用户ID"""
        self.authenticate_as_admin()

        # 测试不存在的用户ID
        url = reverse("user-detail", kwargs={"pk": 99999})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # 测试无效的用户ID格式
        url = "/api/users/crud/users/invalid_id/"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_extremely_long_input(self):
        """测试极长输入"""
        self.authenticate_as_admin()

        url = reverse("user-list")

        # 创建极长的用户名和邮箱
        long_string = "a" * 1000
        data = {
            "username": long_string,
            "email": f"{long_string}@test.com",
            "password": "LongTest123!",
            "password_confirm": "LongTest123!",
        }

        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_special_characters_input(self):
        """测试特殊字符输入"""
        self.authenticate_as_admin()

        url = reverse("user-list")

        # 测试包含特殊字符的输入
        special_chars = ["<script>", '"; DROP TABLE users; --', "../../etc/passwd"]

        for special_char in special_chars:
            data = {
                "username": f"test_{special_char}",
                "email": f"test_{special_char}@test.com",
                "password": "SpecialTest123!",
                "password_confirm": "SpecialTest123!",
            }

            response = self.client.post(url, data, format="json")
            # 应该返回验证错误或成功创建（取决于验证规则）
            self.assertIn(
                response.status_code,
                [status.HTTP_400_BAD_REQUEST, status.HTTP_201_CREATED],
            )

    def test_empty_request_body(self):
        """测试空请求体"""
        self.authenticate_as_admin()

        url = reverse("user-list")
        response = self.client.post(url, {}, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data["success"])
        self.assertIn("errors", response.data)

    def test_malformed_json(self):
        """测试格式错误的JSON"""
        self.authenticate_as_admin()

        url = reverse("user-list")

        # 发送格式错误的JSON
        response = self.client.post(
            url,
            '{"username": "test", "email": "<EMAIL>", "password": "Test123!", "password_confirm": "Test123!"',  # 缺少结束括号
            content_type="application/json",
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class SecurityTestCase(BaseAPITestCase):
    """安全测试"""

    def test_sql_injection_protection(self):
        """测试SQL注入防护"""
        self.authenticate_as_admin()

        url = reverse("user-list")

        # 尝试SQL注入攻击
        sql_injection_attempts = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'; UPDATE users SET is_superuser=1; --",
        ]

        for injection in sql_injection_attempts:
            response = self.client.get(f"{url}?search={injection}")
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            # 确保没有返回所有用户（防止绕过过滤）
            self.assertIsInstance(response.data["data"], list)

    def test_xss_protection(self):
        """测试XSS防护"""
        self.authenticate_as_admin()

        url = reverse("user-list")

        # 尝试XSS攻击
        xss_payloads = [
            '<script>alert("XSS")</script>',
            'javascript:alert("XSS")',
            '<img src="x" onerror="alert(\'XSS\')">',
        ]

        for payload in xss_payloads:
            data = {
                "username": f"xss_test_{random.randint(1000, 9999)}",
                "email": f"xss{random.randint(1000, 9999)}@test.com",
                "name": payload,
                "password": "XSSTest123!",
                "password_confirm": "XSSTest123!",
            }

            response = self.client.post(url, data, format="json")

            if response.status_code == status.HTTP_201_CREATED:
                # 如果创建成功，检查返回的数据是否被正确转义
                returned_name = response.data["data"]["name"]
                self.assertNotIn("<script>", returned_name)
                self.assertNotIn("javascript:", returned_name)

    def test_authorization_bypass_attempts(self):
        """测试授权绕过尝试"""
        # 尝试在没有认证的情况下访问API
        self.clear_authentication()

        urls_to_test = [
            reverse("user-list"),
            reverse("department-list"),
            reverse("team-list"),
            reverse("role-list"),
            reverse("group-list"),
        ]

        for url in urls_to_test:
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

            response = self.client.post(url, {}, format="json")
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
