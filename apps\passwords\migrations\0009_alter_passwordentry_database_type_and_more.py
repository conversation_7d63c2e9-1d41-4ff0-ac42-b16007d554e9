# Generated by Django 5.2.4 on 2025-07-30 09:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("passwords", "0008_add_password_groups"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name="passwordentry",
            name="database_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("gaussdb", "GaussDB"),
                    ("mysql", "MySQL"),
                    ("postgresql", "PostgreSQL"),
                    ("oracle", "Oracle"),
                    ("redis", "Redis"),
                    ("other", "其他"),
                ],
                max_length=20,
                verbose_name="数据库类型",
            ),
        ),
        migrations.AlterField(
            model_name="passwordentry",
            name="protocol",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ssh", "SSH"),
                    ("rdp", "RDP"),
                    ("http", "HTTP"),
                    ("https", "HTTPS"),
                    ("ftp", "FTP"),
                    ("other", "其他"),
                ],
                max_length=10,
                verbose_name="连接协议",
            ),
        ),
        migrations.CreateModel(
            name="PasswordPolicy",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="策略名称")),
                ("description", models.TextField(blank=True, verbose_name="策略描述")),
                (
                    "min_length",
                    models.PositiveIntegerField(
                        default=8,
                        help_text="密码的最小字符数",
                        verbose_name="密码最小长度",
                    ),
                ),
                (
                    "max_length",
                    models.PositiveIntegerField(
                        default=128,
                        help_text="密码的最大字符数",
                        verbose_name="密码最大长度",
                    ),
                ),
                (
                    "uppercase_count",
                    models.PositiveIntegerField(
                        default=1,
                        help_text="密码中必须包含的大写字母最少个数",
                        verbose_name="大写字母最少个数",
                    ),
                ),
                (
                    "lowercase_count",
                    models.PositiveIntegerField(
                        default=1,
                        help_text="密码中必须包含的小写字母最少个数",
                        verbose_name="小写字母最少个数",
                    ),
                ),
                (
                    "digit_count",
                    models.PositiveIntegerField(
                        default=1,
                        help_text="密码中必须包含的数字最少个数",
                        verbose_name="数字最少个数",
                    ),
                ),
                (
                    "special_char_count",
                    models.PositiveIntegerField(
                        default=1,
                        help_text="密码中必须包含的特殊字符最少个数",
                        verbose_name="特殊字符最少个数",
                    ),
                ),
                (
                    "allowed_special_chars",
                    models.CharField(
                        default="!@#$%^&*()_+-=[]{}|;:,.<>?",
                        help_text="密码中允许使用的特殊字符集合",
                        max_length=100,
                        verbose_name="允许的特殊字符",
                    ),
                ),
                (
                    "allow_repeated_chars",
                    models.BooleanField(
                        default=False,
                        help_text="是否允许密码中出现连续重复的字符",
                        verbose_name="是否允许重复字符",
                    ),
                ),
                (
                    "max_repeated_chars",
                    models.PositiveIntegerField(
                        default=2,
                        help_text="允许连续重复字符的最大个数",
                        verbose_name="最大重复字符数",
                    ),
                ),
                (
                    "forbid_common_passwords",
                    models.BooleanField(
                        default=True,
                        help_text="是否禁止使用常见的弱密码",
                        verbose_name="禁止常见密码",
                    ),
                ),
                (
                    "forbid_personal_info",
                    models.BooleanField(
                        default=True,
                        help_text="是否禁止密码中包含用户名、邮箱等个人信息",
                        verbose_name="禁止个人信息",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="是否启用此密码策略",
                        verbose_name="是否启用",
                    ),
                ),
                (
                    "is_default",
                    models.BooleanField(
                        default=False,
                        help_text="是否为系统默认的密码策略",
                        verbose_name="是否为默认策略",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_password_policies",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建者",
                    ),
                ),
            ],
            options={
                "verbose_name": "密码策略",
                "verbose_name_plural": "密码策略",
                "db_table": "password_policies",
                "ordering": ["-is_default", "-created_at"],
            },
        ),
    ]
