# Generated by Django 5.2.4 on 2025-07-28 13:17

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='分类名称')),
                ('description', models.TextField(blank=True, verbose_name='分类描述')),
                ('icon', models.CharField(blank=True, max_length=50, verbose_name='图标')),
                ('color', models.CharField(default='#1890ff', max_length=7, verbose_name='颜色')),
                ('is_system', models.BooleanField(default=False, verbose_name='系统分类')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='passwords.category', verbose_name='上级分类')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户')),
            ],
            options={
                'verbose_name': '密码分类',
                'verbose_name_plural': '密码分类',
                'db_table': 'password_categories',
                'unique_together': {('name', 'user', 'parent')},
            },
        ),
        migrations.CreateModel(
            name='PasswordEntry',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='标题')),
                ('username', models.CharField(blank=True, max_length=200, verbose_name='用户名')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='邮箱')),
                ('password', models.TextField(verbose_name='密码')),
                ('url', models.URLField(blank=True, verbose_name='网址')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('strength', models.CharField(choices=[('weak', '弱'), ('medium', '中等'), ('strong', '强'), ('very_strong', '非常强')], default='medium', max_length=20, verbose_name='密码强度')),
                ('is_compromised', models.BooleanField(default=False, verbose_name='是否泄露')),
                ('last_used', models.DateTimeField(blank=True, null=True, verbose_name='最后使用时间')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='过期时间')),
                ('is_favorite', models.BooleanField(default=False, verbose_name='收藏')),
                ('is_pinned', models.BooleanField(default=False, verbose_name='置顶')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='passwords.category', verbose_name='分类')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='owned_passwords', to=settings.AUTH_USER_MODEL, verbose_name='所有者')),
            ],
            options={
                'verbose_name': '密码条目',
                'verbose_name_plural': '密码条目',
                'db_table': 'password_entries',
                'ordering': ['-is_pinned', '-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomField',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='字段名称')),
                ('field_type', models.CharField(choices=[('text', '文本'), ('password', '密码'), ('email', '邮箱'), ('url', '网址'), ('phone', '电话'), ('date', '日期'), ('number', '数字')], default='text', max_length=20, verbose_name='字段类型')),
                ('field_value', models.TextField(verbose_name='字段值')),
                ('is_sensitive', models.BooleanField(default=False, verbose_name='敏感数据')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('password_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_fields', to='passwords.passwordentry', verbose_name='密码条目')),
            ],
            options={
                'verbose_name': '自定义字段',
                'verbose_name_plural': '自定义字段',
                'db_table': 'password_custom_fields',
                'ordering': ['order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='Attachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.CharField(max_length=255, verbose_name='文件名')),
                ('file_path', models.CharField(max_length=500, verbose_name='文件路径')),
                ('file_size', models.BigIntegerField(verbose_name='文件大小')),
                ('content_type', models.CharField(max_length=100, verbose_name='文件类型')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='上传用户')),
                ('password_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='passwords.passwordentry', verbose_name='密码条目')),
            ],
            options={
                'verbose_name': '附件',
                'verbose_name_plural': '附件',
                'db_table': 'password_attachments',
            },
        ),
        migrations.CreateModel(
            name='PasswordHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_password', models.TextField(verbose_name='旧密码')),
                ('changed_at', models.DateTimeField(auto_now_add=True, verbose_name='修改时间')),
                ('changed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='修改用户')),
                ('password_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='password_history', to='passwords.passwordentry', verbose_name='密码条目')),
            ],
            options={
                'verbose_name': '密码历史',
                'verbose_name_plural': '密码历史',
                'db_table': 'password_history',
                'ordering': ['-changed_at'],
            },
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='标签名称')),
                ('color', models.CharField(default='#87d068', max_length=7, verbose_name='标签颜色')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建用户')),
            ],
            options={
                'verbose_name': '标签',
                'verbose_name_plural': '标签',
                'db_table': 'password_tags',
                'unique_together': {('name', 'user')},
            },
        ),
        migrations.AddField(
            model_name='passwordentry',
            name='tags',
            field=models.ManyToManyField(blank=True, to='passwords.tag', verbose_name='标签'),
        ),
    ]
