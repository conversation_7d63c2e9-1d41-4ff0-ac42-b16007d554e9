# Audit API 权限验证指南

## 概述

audit应用的所有API端点都需要**管理员权限**，这是为了确保敏感的审计数据只能被授权的管理员访问。

## 权限要求

### 🔒 管理员权限要求

所有audit API端点都需要满足以下条件：

1. **认证要求**: 用户必须已登录并提供有效的JWT令牌
2. **管理员权限**: 用户必须具有管理员权限 (`is_superuser=True`)

### 权限验证机制

```python
# 在视图中的权限设置
permission_classes = [IsAuthenticated, IsAdminUser]
```

这意味着：
- `IsAuthenticated`: 用户必须已认证
- `IsAdminUser`: 用户必须是管理员 (`user.is_superuser == True`)

## API端点权限列表

| 端点 | 方法 | 权限要求 | 描述 |
|------|------|----------|------|
| `/api/audit/business-operations/` | GET | 🔒 管理员 | 业务操作日志 |
| `/api/audit/password-access/` | GET | 🔒 管理员 | 密码访问日志 |
| `/api/audit/security-events/` | GET | 🔒 管理员 | 安全事件列表 |
| `/api/audit/security-events/{id}/` | GET, PUT, PATCH | 🔒 管理员 | 安全事件详情 |
| `/api/audit/model-changes/` | GET | 🔒 管理员 | 模型变更日志 |
| `/api/audit/login-attempts/` | GET | 🔒 管理员 | 登录尝试记录 |
| `/api/audit/users/{user_id}/audit-trail/` | GET | 🔒 管理员 | 用户审计轨迹 |
| `/api/audit/statistics/` | GET | 🔒 管理员 | 审计统计信息 |
| `/api/audit/operation-logs/` | GET | 🔒 管理员 | 操作日志(兼容) |
| `/api/audit/access-logs/` | GET | 🔒 管理员 | 访问日志(兼容) |

## 使用示例

### 1. 获取JWT令牌

```bash
# 使用管理员账户登录
curl -X POST http://localhost:8001/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin_user",
    "password": "admin_password"
  }'
```

响应：
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "username": "admin_user",
    "is_superuser": true
  }
}
```

### 2. 访问audit API

```bash
# 使用JWT令牌访问审计API
curl -X GET http://localhost:8001/api/audit/business-operations/ \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

### 3. 前端权限检查示例

```javascript
// 检查用户是否有管理员权限
function canAccessAuditAPI(user) {
  return user && user.is_authenticated && user.is_superuser;
}

// 在访问audit API前检查权限
async function fetchAuditLogs() {
  const user = getCurrentUser();
  
  if (!canAccessAuditAPI(user)) {
    throw new Error('需要管理员权限才能访问审计日志');
  }
  
  const response = await fetch('/api/audit/business-operations/', {
    headers: {
      'Authorization': `Bearer ${getAccessToken()}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    if (response.status === 403) {
      throw new Error('权限不足：需要管理员权限');
    }
    throw new Error('请求失败');
  }
  
  return response.json();
}
```

## 错误处理

### 常见错误响应

#### 1. 未认证 (401 Unauthorized)
```json
{
  "detail": "Authentication credentials were not provided."
}
```

#### 2. 权限不足 (403 Forbidden)
```json
{
  "detail": "You do not have permission to perform this action."
}
```

#### 3. 令牌过期 (401 Unauthorized)
```json
{
  "detail": "Given token not valid for any token type",
  "code": "token_not_valid",
  "messages": [
    {
      "token_class": "AccessToken",
      "token_type": "access",
      "message": "Token is invalid or expired"
    }
  ]
}
```

### 错误处理最佳实践

```javascript
async function handleAuditAPIRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${getAccessToken()}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    });
    
    if (response.status === 401) {
      // 令牌过期，尝试刷新
      await refreshToken();
      // 重试请求
      return handleAuditAPIRequest(url, options);
    }
    
    if (response.status === 403) {
      throw new Error('权限不足：需要管理员权限访问审计功能');
    }
    
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status}`);
    }
    
    return response.json();
  } catch (error) {
    console.error('Audit API请求失败:', error);
    throw error;
  }
}
```

## 安全注意事项

1. **最小权限原则**: 只有真正需要访问审计数据的用户才应该被授予管理员权限
2. **令牌安全**: 确保JWT令牌的安全存储和传输
3. **权限检查**: 在前端和后端都要进行权限检查
4. **审计日志**: 所有对审计API的访问都会被记录
5. **定期审查**: 定期审查管理员权限的分配

## 开发建议

1. **权限检查组件**: 创建可复用的权限检查组件
2. **错误处理**: 统一处理权限相关的错误
3. **用户体验**: 为权限不足的情况提供友好的提示
4. **测试**: 确保权限验证逻辑的正确性

## 总结

audit应用采用严格的权限控制策略，确保只有管理员才能访问敏感的审计数据。开发者在使用这些API时，必须：

1. 确保用户具有管理员权限
2. 正确处理认证和权限错误
3. 在前端进行适当的权限检查
4. 遵循安全最佳实践

这种设计确保了审计数据的安全性和系统的整体安全性。
