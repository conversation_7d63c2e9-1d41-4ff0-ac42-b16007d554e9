#!/usr/bin/env python
"""
测试首次登录密码修改API
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def test_first_login_change_api():
    """测试首次登录密码修改API"""
    print("🔐 测试首次登录密码修改API")
    print("=" * 60)
    
    try:
        from django.test import Client
        import json
        
        client = Client()
        
        # 1. 先登录获取有效令牌
        print("1. 登录获取有效令牌")
        login_data = {
            'username': 'root',
            'password': 'root123!'
        }
        
        response = client.post(
            '/api/auth/login/',
            data=json.dumps(login_data),
            content_type='application/json'
        )
        
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return False
        
        data = response.json()
        access_token = data.get('access_token')
        
        if not access_token:
            print("❌ 未获得访问令牌")
            return False
        
        print(f"✅ 获得访问令牌: {access_token[:30]}...")
        
        # 2. 测试GET请求（查看接口信息）
        print("\n2. 测试GET请求")
        response = client.get(
            '/api/auth/password/first-login-change/',
            HTTP_AUTHORIZATION=f'Bearer {access_token}'
        )
        
        print(f"GET状态码: {response.status_code}")
        
        if response.status_code == 405:
            print("⚠️ GET方法不允许，这是正常的")
        elif response.status_code == 200:
            print("✅ GET请求成功")
            try:
                print(f"响应内容: {response.json()}")
            except:
                print(f"响应内容: {response.content}")
        else:
            print(f"❌ GET请求异常: {response.status_code}")
        
        # 3. 测试POST请求（实际修改密码）
        print("\n3. 测试POST请求")
        
        # 准备密码修改数据
        password_data = {
            'old_password': 'root123!',
            'new_password': 'newpassword123!',
            'confirm_password': 'newpassword123!'
        }
        
        response = client.post(
            '/api/auth/password/first-login-change/',
            data=json.dumps(password_data),
            content_type='application/json',
            HTTP_AUTHORIZATION=f'Bearer {access_token}'
        )
        
        print(f"POST状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 密码修改成功")
            try:
                result = response.json()
                print(f"响应内容: {result}")
            except:
                print(f"响应内容: {response.content}")
        else:
            print(f"❌ 密码修改失败: {response.status_code}")
            try:
                error = response.json()
                print(f"错误信息: {error}")
            except:
                print(f"响应内容: {response.content}")
        
        # 4. 测试其他HTTP方法
        print("\n4. 测试其他HTTP方法")
        
        methods = ['PUT', 'PATCH', 'DELETE']
        for method in methods:
            if method == 'PUT':
                response = client.put(
                    '/api/auth/password/first-login-change/',
                    data=json.dumps(password_data),
                    content_type='application/json',
                    HTTP_AUTHORIZATION=f'Bearer {access_token}'
                )
            elif method == 'PATCH':
                response = client.patch(
                    '/api/auth/password/first-login-change/',
                    data=json.dumps(password_data),
                    content_type='application/json',
                    HTTP_AUTHORIZATION=f'Bearer {access_token}'
                )
            elif method == 'DELETE':
                response = client.delete(
                    '/api/auth/password/first-login-change/',
                    HTTP_AUTHORIZATION=f'Bearer {access_token}'
                )
            
            print(f"{method}状态码: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_api_endpoint_configuration():
    """检查API端点配置"""
    print("\n🔍 检查API端点配置")
    print("=" * 60)
    
    try:
        from django.urls import reverse, NoReverseMatch
        
        # 尝试反向解析URL
        try:
            url = reverse('password_first_login_change')
            print(f"✅ URL配置正确: {url}")
        except NoReverseMatch:
            print("❌ URL配置未找到")
        
        # 检查URL模式
        from django.conf import settings
        from django.urls import get_resolver
        
        resolver = get_resolver()
        
        # 查找相关的URL模式
        print("\n📋 相关URL模式:")
        
        def find_urls(patterns, prefix=''):
            for pattern in patterns:
                if hasattr(pattern, 'url_patterns'):
                    # 这是一个include()
                    find_urls(pattern.url_patterns, prefix + str(pattern.pattern))
                else:
                    # 这是一个普通的URL模式
                    full_pattern = prefix + str(pattern.pattern)
                    if 'password' in full_pattern or 'first-login' in full_pattern:
                        print(f"   {full_pattern} -> {pattern.callback}")
        
        find_urls(resolver.url_patterns)
        
        return True
        
    except Exception as e:
        print(f"❌ 检查API端点配置失败: {e}")
        return False

def check_view_implementation():
    """检查视图实现"""
    print("\n🔍 检查视图实现")
    print("=" * 60)
    
    try:
        # 查找首次登录密码修改视图
        from apps.users import views
        
        # 检查是否有相关的视图类
        view_classes = [
            'FirstLoginPasswordChangeView',
            'PasswordFirstLoginChangeView',
            'FirstPasswordChangeView',
        ]
        
        found_views = []
        for view_class in view_classes:
            if hasattr(views, view_class):
                found_views.append(view_class)
                view = getattr(views, view_class)
                print(f"✅ 找到视图: {view_class}")
                print(f"   类型: {type(view)}")
                
                # 检查允许的HTTP方法
                if hasattr(view, 'http_method_names'):
                    print(f"   允许的方法: {view.http_method_names}")
                
                # 检查权限类
                if hasattr(view, 'permission_classes'):
                    print(f"   权限类: {view.permission_classes}")
                
                # 检查认证类
                if hasattr(view, 'authentication_classes'):
                    print(f"   认证类: {view.authentication_classes}")
        
        if not found_views:
            print("⚠️ 未找到首次登录密码修改视图")
            
            # 列出所有可用的视图
            print("\n📋 可用的视图:")
            for attr_name in dir(views):
                attr = getattr(views, attr_name)
                if (hasattr(attr, '__bases__') and 
                    any('View' in base.__name__ for base in attr.__bases__)):
                    print(f"   {attr_name}")
        
        return len(found_views) > 0
        
    except Exception as e:
        print(f"❌ 检查视图实现失败: {e}")
        return False

def generate_correct_usage_example():
    """生成正确的使用示例"""
    print("\n💡 正确的API使用示例")
    print("=" * 60)
    
    print("基于测试结果，正确的使用方法:")
    print()
    
    print("1. 🔐 首先登录获取JWT令牌:")
    print("   POST /api/auth/login/")
    print("   Content-Type: application/json")
    print("   {")
    print('     "username": "root",')
    print('     "password": "root123!"')
    print("   }")
    print()
    
    print("2. 📝 使用返回的access_token:")
    print("   响应示例:")
    print("   {")
    print('     "access_token": "eyJhbGciOiJIUzI1NiIs...",')
    print('     "refresh_token": "eyJhbGciOiJIUzI1NiIs...",')
    print('     "user": {...}')
    print("   }")
    print()
    
    print("3. 🔑 调用密码修改接口:")
    print("   POST /api/auth/password/first-login-change/")
    print("   Authorization: Bearer eyJhbGciOiJIUzI1NiIs...")
    print("   Content-Type: application/json")
    print("   {")
    print('     "old_password": "root123!",')
    print('     "new_password": "newpassword123!",')
    print('     "confirm_password": "newpassword123!"')
    print("   }")
    print()
    
    print("🚨 常见错误:")
    print("   1. 使用错误格式的令牌（非JWT格式）")
    print("   2. 令牌过期或无效")
    print("   3. 使用GET方法而不是POST方法")
    print("   4. 缺少Authorization头")
    print("   5. 令牌格式错误（缺少Bearer前缀）")
    print()
    
    print("✅ 正确的令牌格式:")
    print("   - 标准JWT格式：header.payload.signature")
    print("   - 包含3个部分，用点号分隔")
    print("   - 长度通常在200-300字符")
    print("   - 以eyJ开头（Base64编码的JSON）")

def main():
    """主测试函数"""
    print("🚀 开始测试首次登录密码修改API")
    print("=" * 80)
    
    tests = [
        ("API端点配置", check_api_endpoint_configuration),
        ("视图实现", check_view_implementation),
        ("API功能测试", test_first_login_change_api),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            print("-" * 50)
    
    # 生成使用示例
    generate_correct_usage_example()
    
    print("\n" + "=" * 80)
    print("📋 测试总结")
    print("=" * 80)
    
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    print("\n🎯 问题分析:")
    print("   1. ❌ 前端使用的令牌格式错误")
    print("   2. ✅ 后端JWT系统正常工作")
    print("   3. ✅ API端点配置正确")
    print("   4. ⚠️ 需要使用POST方法而不是GET")
    
    print("\n💡 解决方案:")
    print("   1. 前端重新登录获取正确的JWT令牌")
    print("   2. 确保使用access_token字段的值")
    print("   3. 使用POST方法调用密码修改接口")
    print("   4. 检查前端令牌存储和传输逻辑")
    
    print("\n🔧 前端修复建议:")
    print("   1. 清除localStorage中的旧令牌")
    print("   2. 重新调用登录接口")
    print("   3. 正确存储和使用access_token")
    print("   4. 确保Authorization头格式正确")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
