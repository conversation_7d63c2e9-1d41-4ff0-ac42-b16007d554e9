# 用户管理CRUD API快速启动指南

## 概述

本指南帮助您快速开始使用新实现的用户管理CRUD API，该API完全兼容现有的Django密码管理系统。

## 前置条件

1. 确保Django服务器正在运行
2. 拥有管理员账户或相应权限的用户账户
3. 了解基本的REST API调用方式

## 快速开始

### 1. 获取认证Token

首先需要通过现有的登录API获取JWT Token：

```bash
curl -X POST "http://localhost:8001/api/users/auth/login/" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "your_password"
  }'
```

响应示例：
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>"
    }
  }
}
```

### 2. 设置认证头

在后续的API调用中，需要在请求头中包含JWT Token：

```bash
Authorization: Bearer <access_token>
```

### 3. 基本CRUD操作示例

#### 3.1 获取用户列表

```bash
curl -X GET "http://localhost:8001/api/users/crud/users/" \
  -H "Authorization: Bearer <access_token>"
```

#### 3.2 创建新用户

```bash
curl -X POST "http://localhost:8001/api/users/crud/users/" \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "name": "新用户",
    "password": "SecurePassword123!",
    "password_confirm": "SecurePassword123!",
    "first_name": "新",
    "last_name": "用户",
    "phone": "13800138001"
  }'
```

#### 3.3 获取用户详情

```bash
curl -X GET "http://localhost:8001/api/users/crud/users/1/" \
  -H "Authorization: Bearer <access_token>"
```

#### 3.4 更新用户信息

```bash
curl -X PATCH "http://localhost:8001/api/users/crud/users/1/" \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的姓名",
    "phone": "13800138002"
  }'
```

#### 3.5 删除用户（软删除）

```bash
curl -X DELETE "http://localhost:8001/api/users/crud/users/1/" \
  -H "Authorization: Bearer <access_token>"
```

## 组织架构管理

### 4.1 部门管理

#### 创建部门
```bash
curl -X POST "http://localhost:8001/api/users/crud/departments/" \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "技术部",
    "description": "技术开发部门"
  }'
```

#### 获取部门列表
```bash
curl -X GET "http://localhost:8001/api/users/crud/departments/" \
  -H "Authorization: Bearer <access_token>"
```

#### 获取部门下的用户
```bash
curl -X GET "http://localhost:8001/api/users/crud/departments/1/users/" \
  -H "Authorization: Bearer <access_token>"
```

### 4.2 团队管理

#### 创建团队
```bash
curl -X POST "http://localhost:8001/api/users/crud/teams/" \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "后端团队",
    "description": "后端开发团队",
    "department": 1
  }'
```

### 4.3 角色管理

#### 创建角色
```bash
curl -X POST "http://localhost:8001/api/users/crud/roles/" \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "developer",
    "description": "开发人员角色",
    "permissions": ["view_password", "create_password"]
  }'
```

## 用户组管理

### 5.1 创建用户组

```bash
curl -X POST "http://localhost:8001/api/users/crud/groups/" \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "开发组",
    "users": [1, 2, 3]
  }'
```

### 5.2 向用户组添加用户

```bash
curl -X POST "http://localhost:8001/api/users/crud/groups/1/add_users/" \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "user_ids": [4, 5, 6]
  }'
```

### 5.3 从用户组移除用户

```bash
curl -X POST "http://localhost:8001/api/users/crud/groups/1/remove_users/" \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "user_ids": [4, 5]
  }'
```

## 高级功能

### 6.1 搜索和过滤

#### 搜索用户
```bash
curl -X GET "http://localhost:8001/api/users/crud/users/?search=admin" \
  -H "Authorization: Bearer <access_token>"
```

#### 按部门过滤
```bash
curl -X GET "http://localhost:8001/api/users/crud/users/?department=1" \
  -H "Authorization: Bearer <access_token>"
```

#### 按状态过滤
```bash
curl -X GET "http://localhost:8001/api/users/crud/users/?is_active=true" \
  -H "Authorization: Bearer <access_token>"
```

### 6.2 分页

```bash
curl -X GET "http://localhost:8001/api/users/crud/users/?page=1&page_size=10" \
  -H "Authorization: Bearer <access_token>"
```

### 6.3 排序

```bash
curl -X GET "http://localhost:8001/api/users/crud/users/?ordering=-created_at" \
  -H "Authorization: Bearer <access_token>"
```

### 6.4 组合查询

```bash
curl -X GET "http://localhost:8001/api/users/crud/users/?search=dev&department=1&is_active=true&page=1&page_size=20&ordering=username" \
  -H "Authorization: Bearer <access_token>"
```

## 特殊操作

### 7.1 重置用户密码

```bash
curl -X POST "http://localhost:8001/api/users/crud/users/1/reset_password/" \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "new_password": "NewSecurePassword123!"
  }'
```

### 7.2 切换用户激活状态

```bash
curl -X POST "http://localhost:8001/api/users/crud/users/1/toggle_active/" \
  -H "Authorization: Bearer <access_token>"
```

## 错误处理

### 常见错误响应

#### 认证失败 (401)
```json
{
  "detail": "给定的认证凭据无效。"
}
```

#### 权限不足 (403)
```json
{
  "success": false,
  "message": "权限不足",
  "code": "INSUFFICIENT_PERMISSION"
}
```

#### 验证错误 (400)
```json
{
  "success": false,
  "message": "数据验证失败",
  "code": "VALIDATION_ERROR",
  "errors": {
    "username": ["该用户名已存在"],
    "email": ["请输入有效的邮箱地址"]
  }
}
```

## 测试API

### 使用Postman

1. 导入API集合（如果有提供）
2. 设置环境变量：
   - `base_url`: http://localhost:8001
   - `access_token`: 从登录API获取的token

### 使用Python requests

```python
import requests

# 登录获取token
login_response = requests.post(
    'http://localhost:8001/api/users/auth/login/',
    json={
        'username': 'admin',
        'password': 'your_password'
    }
)
token = login_response.json()['data']['access']

# 设置认证头
headers = {'Authorization': f'Bearer {token}'}

# 获取用户列表
users_response = requests.get(
    'http://localhost:8001/api/users/crud/users/',
    headers=headers
)
print(users_response.json())
```

## 注意事项

1. **权限要求**: 大部分写操作需要管理员权限
2. **软删除**: 用户删除采用软删除方式，不会真正删除数据
3. **关系约束**: 删除部门/团队/角色前需确保没有关联用户
4. **密码安全**: 创建用户时密码需要满足强度要求
5. **Token过期**: JWT Token有过期时间，需要定期刷新

## 获取帮助

- 查看详细API文档: `docs/user-management-api-examples.md`
- 查看兼容性说明: `docs/crud-api-compatibility.md`
- 运行测试: `python manage.py test apps.users.test_crud_compatibility`

## 下一步

1. 熟悉基本的CRUD操作
2. 了解权限和安全机制
3. 集成到前端应用
4. 根据需要扩展功能
