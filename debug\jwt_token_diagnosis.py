#!/usr/bin/env python
"""
JWT令牌问题诊断
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def analyze_token_format():
    """分析令牌格式"""
    print("🔍 分析JWT令牌格式")
    print("=" * 60)
    
    # 问题令牌
    problem_token = "mwiovVOLyoytmzbSKGBeHNjuDOgej6dhR49qSaZUi9c"
    
    print(f"问题令牌: {problem_token}")
    print(f"令牌长度: {len(problem_token)}")
    print(f"令牌格式: {'JWT格式' if '.' in problem_token else '非JWT格式'}")
    
    # JWT令牌应该有3个部分，用.分隔
    parts = problem_token.split('.')
    print(f"令牌部分数: {len(parts)}")
    
    if len(parts) != 3:
        print("❌ 这不是标准的JWT令牌格式")
        print("💡 JWT令牌应该包含3个部分：header.payload.signature")
    else:
        print("✅ JWT令牌格式正确")
        for i, part in enumerate(parts, 1):
            print(f"   部分{i}: {part[:20]}... (长度: {len(part)})")
    
    return len(parts) == 3

def test_login_and_get_valid_token():
    """测试登录并获取有效令牌"""
    print("\n🔐 测试登录获取有效令牌")
    print("=" * 60)
    
    try:
        from django.test import Client
        import json
        
        client = Client()
        
        # 使用正确凭据登录
        login_data = {
            'username': 'root',
            'password': 'root123!'
        }
        
        print(f"🧪 使用凭据登录: {login_data['username']}")
        
        response = client.post(
            '/api/auth/login/',
            data=json.dumps(login_data),
            content_type='application/json'
        )
        
        print(f"登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if 'access_token' in data:
                access_token = data['access_token']
                print(f"✅ 获得访问令牌")
                print(f"令牌格式: {access_token[:50]}...")
                print(f"令牌长度: {len(access_token)}")
                
                # 分析令牌结构
                parts = access_token.split('.')
                print(f"令牌部分数: {len(parts)}")
                
                if len(parts) == 3:
                    print("✅ 标准JWT格式")
                    
                    # 尝试解码header和payload
                    try:
                        import base64
                        import json
                        
                        # 解码header
                        header_data = base64.urlsafe_b64decode(parts[0] + '==').decode('utf-8')
                        header = json.loads(header_data)
                        print(f"Header: {header}")
                        
                        # 解码payload
                        payload_data = base64.urlsafe_b64decode(parts[1] + '==').decode('utf-8')
                        payload = json.loads(payload_data)
                        print(f"Payload keys: {list(payload.keys())}")
                        print(f"User ID: {payload.get('user_id')}")
                        print(f"Token type: {payload.get('token_type')}")
                        print(f"Expires: {payload.get('exp')}")
                        
                    except Exception as e:
                        print(f"⚠️ 令牌解码失败: {e}")
                
                return access_token
            else:
                print("❌ 响应中没有access_token")
                print(f"响应内容: {data}")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            try:
                print(f"错误信息: {response.json()}")
            except:
                print(f"响应内容: {response.content}")
        
        return None
        
    except Exception as e:
        print(f"❌ 测试登录失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_token_validation(token):
    """测试令牌验证"""
    print(f"\n🔍 测试令牌验证")
    print("=" * 60)
    
    if not token:
        print("❌ 没有有效令牌进行测试")
        return False
    
    try:
        from django.test import Client
        
        client = Client()
        
        # 测试使用令牌访问受保护的端点
        protected_endpoints = [
            '/api/auth/password/first-login-change/',
            '/api/auth/users/',
            '/api/auth/password/change/',
        ]
        
        for endpoint in protected_endpoints:
            print(f"\n🧪 测试端点: {endpoint}")
            
            response = client.get(
                endpoint,
                HTTP_AUTHORIZATION=f'Bearer {token}'
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 令牌验证成功")
            elif response.status_code == 401:
                print("   ❌ 令牌验证失败")
                try:
                    error_data = response.json()
                    print(f"   错误信息: {error_data}")
                except:
                    print(f"   响应内容: {response.content[:100]}")
            elif response.status_code == 405:
                print("   ⚠️ 方法不允许（可能需要POST）")
            else:
                print(f"   ⚠️ 其他状态码: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试令牌验证失败: {e}")
        return False

def check_jwt_settings():
    """检查JWT设置"""
    print(f"\n🔍 检查JWT设置")
    print("=" * 60)
    
    try:
        from django.conf import settings
        
        # 检查JWT相关设置
        jwt_settings = [
            'SECRET_KEY',
            'SIMPLE_JWT',
        ]
        
        for setting in jwt_settings:
            if hasattr(settings, setting):
                value = getattr(settings, setting)
                if setting == 'SECRET_KEY':
                    print(f"{setting}: {'已设置' if value else '未设置'} (长度: {len(value) if value else 0})")
                elif setting == 'SIMPLE_JWT':
                    print(f"{setting}:")
                    for key, val in value.items():
                        if 'SECRET' in key.upper():
                            print(f"   {key}: {'已设置' if val else '未设置'}")
                        else:
                            print(f"   {key}: {val}")
                else:
                    print(f"{setting}: {value}")
            else:
                print(f"{setting}: 未设置")
        
        # 检查认证后端
        auth_backends = getattr(settings, 'AUTHENTICATION_BACKENDS', [])
        print(f"\n认证后端:")
        for backend in auth_backends:
            print(f"   {backend}")
        
        # 检查REST框架设置
        rest_framework = getattr(settings, 'REST_FRAMEWORK', {})
        auth_classes = rest_framework.get('DEFAULT_AUTHENTICATION_CLASSES', [])
        print(f"\nREST框架认证类:")
        for auth_class in auth_classes:
            print(f"   {auth_class}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查JWT设置失败: {e}")
        return False

def test_problem_token():
    """测试问题令牌"""
    print(f"\n🧪 测试问题令牌")
    print("=" * 60)
    
    problem_token = "mwiovVOLyoytmzbSKGBeHNjuDOgej6dhR49qSaZUi9c"
    
    try:
        from django.test import Client
        
        client = Client()
        
        print(f"使用问题令牌访问API...")
        
        response = client.get(
            '/api/auth/password/first-login-change/',
            HTTP_AUTHORIZATION=f'Bearer {problem_token}'
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 401:
            try:
                error_data = response.json()
                print(f"错误详情: {error_data}")
                
                # 分析错误信息
                if 'detail' in error_data:
                    detail = error_data['detail']
                    if 'not valid for any token type' in detail:
                        print("💡 问题分析: 令牌格式不正确或已过期")
                    elif 'invalid' in detail.lower():
                        print("💡 问题分析: 令牌无效")
                
            except:
                print(f"响应内容: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试问题令牌失败: {e}")
        return False

def check_token_blacklist():
    """检查令牌黑名单"""
    print(f"\n🔍 检查令牌黑名单")
    print("=" * 60)
    
    try:
        # 检查是否启用了令牌黑名单
        from django.conf import settings
        
        installed_apps = getattr(settings, 'INSTALLED_APPS', [])
        
        if 'rest_framework_simplejwt.token_blacklist' in installed_apps:
            print("✅ JWT令牌黑名单已启用")
            
            try:
                from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken, OutstandingToken
                
                blacklisted_count = BlacklistedToken.objects.count()
                outstanding_count = OutstandingToken.objects.count()
                
                print(f"黑名单令牌数: {blacklisted_count}")
                print(f"未完成令牌数: {outstanding_count}")
                
                if outstanding_count > 0:
                    print("最近的令牌:")
                    recent_tokens = OutstandingToken.objects.order_by('-created_at')[:5]
                    for token in recent_tokens:
                        print(f"   令牌ID: {token.id}, 创建时间: {token.created_at}")
                
            except ImportError:
                print("⚠️ 无法导入令牌黑名单模型")
        else:
            print("⚠️ JWT令牌黑名单未启用")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查令牌黑名单失败: {e}")
        return False

def generate_solution():
    """生成解决方案"""
    print(f"\n💡 解决方案建议")
    print("=" * 60)
    
    print("基于分析，问题可能的原因和解决方案:")
    print()
    
    print("1. 令牌格式错误:")
    print("   - 问题令牌不是标准JWT格式")
    print("   - 解决方案: 重新登录获取新的JWT令牌")
    print()
    
    print("2. 令牌来源错误:")
    print("   - 可能是其他系统的令牌")
    print("   - 解决方案: 确保使用本系统的登录接口获取令牌")
    print()
    
    print("3. 前端缓存问题:")
    print("   - 前端可能缓存了旧的令牌")
    print("   - 解决方案: 清除前端localStorage/sessionStorage")
    print()
    
    print("4. 令牌传输问题:")
    print("   - 令牌在传输过程中被截断或修改")
    print("   - 解决方案: 检查前端令牌存储和传输逻辑")
    print()
    
    print("🔧 立即尝试的解决步骤:")
    print("1. 重新登录获取新令牌")
    print("2. 检查前端令牌存储逻辑")
    print("3. 确保令牌完整传输")
    print("4. 检查令牌过期时间")

def main():
    """主诊断函数"""
    print("🚀 开始JWT令牌问题诊断")
    print("=" * 80)
    
    tests = [
        ("令牌格式分析", analyze_token_format),
        ("JWT设置检查", check_jwt_settings),
        ("问题令牌测试", test_problem_token),
        ("令牌黑名单检查", check_token_blacklist),
        ("登录获取有效令牌", test_login_and_get_valid_token),
    ]
    
    passed = 0
    total = len(tests)
    valid_token = None
    
    for test_name, test_func in tests:
        try:
            if test_name == "登录获取有效令牌":
                result = test_func()
                if result:
                    valid_token = result
                    passed += 1
            else:
                if test_func():
                    passed += 1
            print("-" * 50)
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            print("-" * 50)
    
    # 如果获得了有效令牌，测试验证
    if valid_token:
        print("🧪 使用有效令牌测试验证")
        test_token_validation(valid_token)
        print("-" * 50)
    
    # 生成解决方案
    generate_solution()
    
    print("\n" + "=" * 80)
    print("📋 诊断总结")
    print("=" * 80)
    
    print(f"📊 诊断结果: {passed}/{total} 个检查完成")
    
    print("\n🎯 主要发现:")
    print("   1. 问题令牌不是标准JWT格式")
    print("   2. 系统可以正常生成有效的JWT令牌")
    print("   3. 需要重新登录获取正确的令牌")
    
    print("\n💡 解决建议:")
    print("   1. 前端重新调用登录接口")
    print("   2. 使用返回的access_token字段")
    print("   3. 检查前端令牌存储逻辑")
    print("   4. 确保令牌完整传输")
    
    if valid_token:
        print(f"\n🎫 有效令牌示例:")
        print(f"   {valid_token[:50]}...")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
