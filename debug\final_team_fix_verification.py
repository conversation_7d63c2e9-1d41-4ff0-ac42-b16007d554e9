#!/usr/bin/env python
"""
最终验证Team字段问题修复
"""
import os
import sys
import django

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
django.setup()

def test_user_queries():
    """测试用户查询"""
    print("🔍 测试用户查询")
    print("=" * 60)
    
    try:
        from apps.users.models import User
        
        # 测试各种查询
        tests = [
            ("基本查询", lambda: User.objects.all()),
            ("select_related查询", lambda: User.objects.select_related('department', 'role')),
            ("过滤查询", lambda: User.objects.filter(is_active=True)),
            ("复杂查询", lambda: User.objects.filter(department__isnull=False)),
            ("排序查询", lambda: User.objects.order_by('username')),
            ("聚合查询", lambda: User.objects.count()),
        ]
        
        for test_name, query_func in tests:
            try:
                result = query_func()
                if hasattr(result, 'count'):
                    count = result.count()
                    print(f"   ✅ {test_name}: 成功 ({count} 条记录)")
                else:
                    print(f"   ✅ {test_name}: 成功 (结果: {result})")
            except Exception as e:
                print(f"   ❌ {test_name}: 失败 - {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 用户查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_serializers():
    """测试序列化器"""
    print("\n🔍 测试序列化器")
    print("=" * 60)
    
    try:
        from apps.users.serializers import (
            UserSerializer,
            UserListSerializer,
            DepartmentListSerializer,
        )
        from apps.users.models import User, Department
        
        # 测试用户序列化器
        user = User.objects.first()
        if user:
            serializer = UserSerializer(user)
            data = serializer.data
            
            if 'team' in data or 'team_name' in data:
                print("   ❌ 用户序列化器仍包含team字段")
                return False
            else:
                print("   ✅ 用户序列化器正常，不包含team字段")
        
        # 测试部门序列化器
        dept = Department.objects.first()
        if dept:
            serializer = DepartmentListSerializer(dept)
            data = serializer.data
            
            if 'teams_count' in data:
                print("   ❌ 部门序列化器仍包含teams_count字段")
                return False
            else:
                print("   ✅ 部门序列化器正常，不包含teams_count字段")
        
        return True
        
    except Exception as e:
        print(f"❌ 序列化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """测试API端点"""
    print("\n🔍 测试API端点")
    print("=" * 60)
    
    try:
        from django.test import Client
        
        client = Client()
        
        # 测试用户相关端点
        endpoints = [
            "/api/auth/users/",
            "/api/auth/departments/",
            "/api/auth/roles/",
        ]
        
        for endpoint in endpoints:
            response = client.get(endpoint)
            if response.status_code in [200, 401, 403]:  # 正常状态码
                print(f"   ✅ {endpoint}: {response.status_code} (正常)")
            else:
                print(f"   ❌ {endpoint}: {response.status_code} (异常)")
                return False
        
        # 测试已删除的team端点
        team_endpoints = [
            "/api/auth/teams/",
            "/api/auth/teams/1/",
        ]
        
        for endpoint in team_endpoints:
            response = client.get(endpoint)
            if response.status_code == 404:
                print(f"   ✅ {endpoint}: 404 (已删除)")
            else:
                print(f"   ❌ {endpoint}: {response.status_code} (应该是404)")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_imports():
    """测试模型导入"""
    print("\n🔍 测试模型导入")
    print("=" * 60)
    
    try:
        # 测试正常模型导入
        from apps.users.models import User, Department, Role
        print("   ✅ User模型导入成功")
        print("   ✅ Department模型导入成功")
        print("   ✅ Role模型导入成功")
        
        # 测试Team模型不存在
        try:
            from apps.users.models import Team
            print("   ❌ Team模型仍然存在")
            return False
        except ImportError:
            print("   ✅ Team模型已删除")
        
        # 检查User模型字段
        user_fields = [field.name for field in User._meta.fields]
        if 'team' in user_fields:
            print("   ❌ User模型仍包含team字段")
            return False
        else:
            print("   ✅ User模型不包含team字段")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_structure():
    """测试数据库结构"""
    print("\n🔍 测试数据库结构")
    print("=" * 60)
    
    try:
        from django.db import connection
        
        # 检查users表结构
        with connection.cursor() as cursor:
            cursor.execute("PRAGMA table_info(users);")
            columns = cursor.fetchall()
            
            team_id_exists = any(col[1] == 'team_id' for col in columns)
            if team_id_exists:
                print("   ❌ users表仍包含team_id字段")
                return False
            else:
                print("   ✅ users表不包含team_id字段")
            
            # 检查teams表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='teams';
            """)
            teams_table = cursor.fetchone()
            
            if teams_table:
                print("   ❌ teams表仍然存在")
                return False
            else:
                print("   ✅ teams表已删除")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主验证函数"""
    print("🚀 开始最终验证Team字段问题修复")
    print("=" * 80)
    
    tests = [
        ("模型导入", test_model_imports),
        ("数据库结构", test_database_structure),
        ("用户查询", test_user_queries),
        ("序列化器", test_serializers),
        ("API端点", test_api_endpoints),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
        
        print("-" * 50)
    
    print("=" * 80)
    print(f"📊 最终验证结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 Team字段问题修复完成！")
        print("\n✅ 修复成果:")
        print("   1. Team模型已完全删除")
        print("   2. User.team字段已删除")
        print("   3. teams数据库表已删除")
        print("   4. 所有序列化器已清理team字段")
        print("   5. 所有代码引用已清理")
        print("   6. 用户查询正常工作")
        print("   7. API端点正常响应")
        
        print("\n🎯 系统现状:")
        print("   - 用户管理功能完全正常")
        print("   - 不再出现team_id字段错误")
        print("   - 所有查询操作正常")
        print("   - API响应正确")
        
        print("\n💡 使用建议:")
        print("   - 重启Django开发服务器以确保所有更改生效")
        print("   - 通知前端团队移除team相关的UI组件")
        print("   - 更新API文档移除team相关字段")
        
    else:
        print("⚠️ 仍有部分问题需要解决")
        print("💡 建议:")
        print("   1. 检查失败的测试项目")
        print("   2. 重启Django服务器")
        print("   3. 清除可能的缓存")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
