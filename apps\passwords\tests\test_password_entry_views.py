"""
密码条目视图测试
"""
from rest_framework import status
from django.urls import reverse
from django.utils import timezone
from apps.passwords.models import PasswordEntry, PasswordHistory
from apps.audit.models import BusinessOperationLog, PasswordAccessLog
from .base import BasePasswordAPITestCase, BasePasswordPermissionTestCase
from .factories import PasswordEntryFactory, CategoryFactory
from utils.encryption import decrypt_data


class PasswordEntryListCreateViewTest(BasePasswordAPITestCase):
    """密码条目列表和创建视图测试"""
    
    def test_list_passwords_authenticated(self):
        """测试认证用户获取密码列表"""
        self.authenticate_user()
        url = self.get_url("password_list_create")
        
        response = self.client.get(url)
        data = self.assert_response_success(response)
        
        # 验证返回数据
        self.assertIn("results", data)
        self.assertEqual(len(data["results"]), 1)  # 只能看到自己的密码
        self.assertEqual(data["results"][0]["id"], str(self.password_entry.id))
    
    def test_list_passwords_unauthenticated(self):
        """测试未认证用户获取密码列表"""
        url = self.get_url("password_list_create")
        
        response = self.client.get(url)
        self.assert_response_error(response, status.HTTP_401_UNAUTHORIZED)
    
    def test_list_passwords_with_filters(self):
        """测试带过滤条件的密码列表"""
        self.authenticate_user()
        
        # 创建不同分类的密码
        other_category = CategoryFactory(name="Other Category")
        PasswordEntryFactory(owner=self.user, category=other_category)
        
        url = self.get_url("password_list_create")
        
        # 按分类过滤
        response = self.client.get(url, {"category": self.category.id})
        data = self.assert_response_success(response)
        self.assertEqual(len(data["results"]), 1)
        
        # 按标题搜索
        response = self.client.get(url, {"search": self.password_entry.title})
        data = self.assert_response_success(response)
        self.assertEqual(len(data["results"]), 1)
    
    def test_create_password_success(self):
        """测试成功创建密码"""
        self.authenticate_user()
        self.clear_logs()
        
        url = self.get_url("password_list_create")
        data = {
            "title": "Test Password",
            "username": "testuser",
            "password": "test_password_123",
            "url": "https://example.com",
            "notes": "Test notes",
            "category": self.category.id,
            "system_type": "web",
            "environment": "dev"
        }
        
        response = self.client.post(url, data, format="json")
        response_data = self.assert_response_success(response, status.HTTP_201_CREATED)
        
        # 验证密码已创建
        password_entry = PasswordEntry.objects.get(id=response_data["id"])
        self.assertEqual(password_entry.title, data["title"])
        self.assertEqual(password_entry.username, data["username"])
        self.assertEqual(password_entry.owner, self.user)
        
        # 验证密码已加密
        decrypted_password = decrypt_data(password_entry.password)
        self.assertEqual(decrypted_password, data["password"])
        
        # 验证操作日志
        self.assert_operation_logged("password_create", "password_entry")
    
    def test_create_password_invalid_data(self):
        """测试创建密码时数据无效"""
        self.authenticate_user()
        
        url = self.get_url("password_list_create")
        data = {
            "title": "",  # 标题不能为空
            "username": "testuser",
            "password": "test_password_123"
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
    
    def test_create_password_missing_required_fields(self):
        """测试创建密码时缺少必需字段"""
        self.authenticate_user()
        
        url = self.get_url("password_list_create")
        data = {
            "title": "Test Password"
            # 缺少username和password
        }
        
        response = self.client.post(url, data, format="json")
        self.assert_response_error(response, status.HTTP_400_BAD_REQUEST)


class PasswordEntryDetailViewTest(BasePasswordPermissionTestCase):
    """密码条目详情视图测试"""
    
    def test_retrieve_own_password(self):
        """测试获取自己的密码详情"""
        self.authenticate_user()
        self.clear_logs()
        
        url = self.get_url("password_detail", pk=self.password_entry.id)
        
        response = self.client.get(url)
        data = self.assert_response_success(response)
        
        # 验证返回数据
        self.assertEqual(data["id"], str(self.password_entry.id))
        self.assertEqual(data["title"], self.password_entry.title)
        
        # 验证访问日志
        self.assert_access_logged(self.password_entry, "view")
        
        # 验证最后使用时间已更新
        self.password_entry.refresh_from_db()
        self.assertIsNotNone(self.password_entry.last_used)
    
    def test_retrieve_others_password_no_permission(self):
        """测试获取他人密码详情（无权限）"""
        self.authenticate_user()
        
        url = self.get_url("password_detail", pk=self.other_password_entry.id)
        
        response = self.client.get(url)
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)
    
    def test_retrieve_others_password_with_permission(self):
        """测试获取他人密码详情（有权限）"""
        # 授予权限
        self.grant_password_permission(
            self.user, 
            self.other_password_entry, 
            "read"
        )
        
        self.authenticate_user()
        self.clear_logs()
        
        url = self.get_url("password_detail", pk=self.other_password_entry.id)
        
        response = self.client.get(url)
        data = self.assert_response_success(response)
        
        # 验证返回数据
        self.assertEqual(data["id"], str(self.other_password_entry.id))
        
        # 验证访问日志
        self.assert_access_logged(self.other_password_entry, "view")
    
    def test_update_own_password(self):
        """测试更新自己的密码"""
        self.authenticate_user()
        self.clear_logs()
        
        url = self.get_url("password_detail", pk=self.password_entry.id)
        data = {
            "title": "Updated Title",
            "username": "updated_user",
            "notes": "Updated notes"
        }
        
        response = self.client.patch(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证更新成功
        self.password_entry.refresh_from_db()
        self.assertEqual(self.password_entry.title, data["title"])
        self.assertEqual(self.password_entry.username, data["username"])
        self.assertEqual(self.password_entry.notes, data["notes"])
        
        # 验证操作日志
        self.assert_operation_logged("password_update", "password_entry")
    
    def test_update_others_password_no_permission(self):
        """测试更新他人密码（无权限）"""
        self.authenticate_user()
        
        url = self.get_url("password_detail", pk=self.other_password_entry.id)
        data = {"title": "Hacked Title"}
        
        response = self.client.patch(url, data, format="json")
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)
    
    def test_update_others_password_with_write_permission(self):
        """测试更新他人密码（有写权限）"""
        # 授予写权限
        self.grant_password_permission(
            self.user, 
            self.other_password_entry, 
            "write"
        )
        
        self.authenticate_user()
        self.clear_logs()
        
        url = self.get_url("password_detail", pk=self.other_password_entry.id)
        data = {"title": "Updated by Permission"}
        
        response = self.client.patch(url, data, format="json")
        response_data = self.assert_response_success(response)
        
        # 验证更新成功
        self.other_password_entry.refresh_from_db()
        self.assertEqual(self.other_password_entry.title, data["title"])
        
        # 验证操作日志
        self.assert_operation_logged("password_update", "password_entry")
    
    def test_delete_own_password(self):
        """测试删除自己的密码"""
        self.authenticate_user()
        self.clear_logs()
        
        url = self.get_url("password_detail", pk=self.password_entry.id)
        
        response = self.client.delete(url)
        self.assert_response_success(response, status.HTTP_204_NO_CONTENT)
        
        # 验证软删除
        self.password_entry.refresh_from_db()
        self.assertTrue(self.password_entry.is_deleted)
        self.assertIsNotNone(self.password_entry.deleted_at)
        
        # 验证操作日志
        self.assert_operation_logged("password_delete", "password_entry")
    
    def test_delete_others_password_no_permission(self):
        """测试删除他人密码（无权限）"""
        self.authenticate_user()
        
        url = self.get_url("password_detail", pk=self.other_password_entry.id)
        
        response = self.client.delete(url)
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)
    
    def test_retrieve_deleted_password(self):
        """测试获取已删除的密码"""
        # 软删除密码
        self.password_entry.is_deleted = True
        self.password_entry.deleted_at = timezone.now()
        self.password_entry.save()
        
        self.authenticate_user()
        
        url = self.get_url("password_detail", pk=self.password_entry.id)
        
        response = self.client.get(url)
        self.assert_response_error(response, status.HTTP_404_NOT_FOUND)
